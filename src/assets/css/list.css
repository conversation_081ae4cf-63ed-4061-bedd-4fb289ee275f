.sp-panel-data-bottom{
    display: flex;
    font-size: 0.8rem;
    font-weight: 500;
    line-height: 1.7rem;
    margin: 10px 0px;
}
.sp-panel-data-item{

}
.sp-panel-data-btn{
    text-align: right;
}
.sp-panel-data-line{
    text-align: left;
    /*margin-right: 10px;*/
    width: 33%;
}
.sp-panel-data-line-end{
    text-align: left;
}
.sp-panel-data-title{
    width: 90%;
    text-align: left;
    font-weight: 500;
    font-size: 0.8rem;
}
.sp-panel-data-close{
    width: 10%;
    text-align: center;
    cursor:pointer;
}
.sp-panel-data-top{
    display: flex;
    border-bottom: 1px solid #e5e7e8;
    padding-bottom: 15px;
}
.sp-panel-data{
    border: 1px solid #d4d4d6;
    padding: 20px;
    width: 40%;
    margin: 0 2.5% 20px 2.5%;
    display: inline-table;
}
.sp-panel-data-img{
    width: 15px;
}
.sp-panel-data-close{

}
.sp-data-bottom{
    display: flex;
    margin-top: 10px;
    padding: 0 20px;
    flex-wrap: wrap;
}
.demonstration{
    margin-right: 10px;
}
.sp-sort-bg-unselect{
    margin: 0px 20px;
    padding: 2px 4px;
    cursor:pointer;
    user-select:none;
    border: 1px solid #fff;
    display: flex;
}
.sp-sort-bg-select{
    margin: 0px 20px;
    border: 1px solid #449BF3;
    color: #449BF3;
    padding: 1px 4px;
    border-radius: 2px;
    display: flex;
    cursor:pointer;
    user-select:none;
}
.sp-sort-bg-title{
    padding: 2px 0px;
}
.sp-sort-bg{
    display: flex;
    padding: 0 20px;
    margin-top: 10px;
    font-size: 0.8rem;
}
.sp-bottom-search-right{
    width: 30%;
    text-align: left;
}
.sp-bottom-search-text{

}
.sp-bottom-search-left{
    width: 70%;
    text-align: left;
}
.sp-bottom-search{
    display: flex;
    padding: 0 20px;
    margin-top: 15px;
}
.sp-bottom-bg{
    padding-bottom: 20px;
}
.sp-content-bg{
    background-color: #fff;
    margin-top: 10px;
    border-radius: 5px;
    overflow: hidden;
}
.sp-bg{
    /*padding: 10px;*/
}
.sp-line-bg{
    display: flex;
    height: 50px;
    background-color: #F6F9FF;
    line-height: 50px;
    user-select:none;
    cursor:pointer;
}
.sp-line-bg-item{
    width: 140px;
    font-size: 0.8rem;
    color: #5A8DE8;
    background-color: #fff;
    font-weight: 500;
}
.sp-line-bg-item-unselect{
    width: 140px;
    font-size: 0.8rem;
    color: #A8A8A9;
    font-weight: 500;
}
.sp-line-bg-item-sple{
    margin: 0px 10px;
}
.sp-title{
    text-align: left;
    font-weight: 600;
    font-size: 1.2rem;
}
.sp-nav{
    text-align: left;
    font-size: 0.9rem;
    color: #7E8AA4;
    font-weight: 500;
    margin-top: 10px;
}
.sp-nav-item{

}
.sp-nav-item-sple{
    margin: 0 5px;
}
.el-dialog__header{
    text-align: left;
}
.el-dialog__title{
    font-size: 16px;
    font-weight: 500;
}
.sp-sort-bg-select-dltop{
    line-height: 0px;
    margin-left: 5px;
    margin-top: 2px;
}
.sp-sort-bg-select-daolijt{
    height: 14px;
}