@charset "UTF-8";

* {
  box-sizing: border-box;
}

html {
  overflow: auto;
}

body {
  background: #F5F6FA;
  min-width: 1200px;
  font: status-bar;
  font-size: 14px;
  line-height: 1.42858;
}

html,
body {
  height: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  margin: 0;
  font-weight: 400;
}

body,
form,
ul,
ol,
dl,
dd,
p {
  margin: 0;
}

ul,
ol {
  list-style-type: none;
  padding: 0;
}

img {
  border: 0 none;
  vertical-align: bottom;
}

button,
input,
select,
textarea {
  font-size: 1em;
  font-family: inherit;
}

a,
a:hover {
  text-decoration: none;
}

i {
  font-style: normal;
}

a {
  color: #4786FF;
}

/*常用样式*/
.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
}

.clearfix {
  *zoom: 1;
}

.main-wrap {
  height: 100%;
  min-height: calc(100vh);
}

.right-div-cls-global-only>section {
  height: 100%;
}

.right-main-wrap {
  -padding-left: 200px;
  height: 100%;
  overflow: auto;
}

.right-main {
  padding: 60px 30px;
}

.pd20 {
  padding: 20px;
}

/*页面标题*/
.page-title {
  font-size: 24px;
  color: #354052;
  font-weight: bold;
  margin-top: 20px;
}

/*面包屑*/
.el-breadcrumb {
  margin-top: 15px;
}

.el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link {
  color: #6F7D9B !important;
}

.global-right-content {
  margin-top: 20px;
  position: relative;
}

.global-border-block {
  background: #ffffff;
  border: 1px solid #E8EAEF;
  border-radius: 4px;
  box-shadow: 0 2px 15px rgba(230, 230, 230, 0.12);
}

/*表格*/
.el-table {
  font-size: 13px !important;
}

.el-table th {
  background: #ECEFF5 !important;
  color: #7F8FA4;
}

.el-table td,
.el-table th.is-leaf {
  border-color: #F5F4F5 !important;
}

/*按钮*/
.el-button--primary {
  background-color: #4786FF;
}

.global-input-wrap {
  width: 164px;
  display: inline-block;
  margin-right: 10px;
}

.global-large-input-wrap {
  width: 285px;
}

.global-flex-input-wrap {
  width: 50%;
}

.global-primary-btn {
  padding: 10px 25px;
  background: #4786FF;
  border-radius: 25px;
  color: #fff;
  font-size: 16px;
  position: absolute;
  top: -60px;
  right: 0px;
  box-shadow: 0 6px 22px rgba(71, 134, 255, .33);
}

.global-drop-add {
  position: absolute;
  top: -60px;
  right: 0px;
}

.global-drop-add .el-dropdown-link {
  padding: 10px 25px;
  background: #4786FF;
  border-radius: 25px;
  color: #fff;
  font-size: 16px;
  display: inline-block;
  box-shadow: 0 6px 22px rgba(71, 134, 255, .33);
}

/*分页*/
.global-pagination {
  padding: 30px;
}

.el-pagination {
  text-align: center;
}

/*表单*/
.global-form .el-form--label-top .el-form-item__label {
  padding: 0;
  line-height: 20px;
  margin-bottom: 5px;
  font-size: 13px;
}

.global-form .el-form-item {
  width: 50%;
  padding: 0 20px;
  float: left;
  vertical-align: top;
}

.global-form .el-form-item__content .el-input-group {
  vertical-align: top;
}

.global-form .full-item {
  width: 100%;
}

.global-form .el-select {
  float: left;
  width: 100%;
  display: block;
}

.global-form-group {
  margin-bottom: 40px;
}

.global-sel-switch {
  height: 40px;
  line-height: 40px;
  border: 1px solid #DFE2E5;
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;
}

.global-sel-switch>span {
  float: left;
  padding: 0 15px;
  border-right: 1px solid #DFE2E5;
  cursor: pointer;
}

.global-sel-switch>span:last-of-type {
  border: none;
}

.global-sel-switch>span.on,
.global-sel-switch>span:hover {
  background-color: #4786FF;
  color: #ffffff;
  border-color: #4786FF;
  transition: all 0.5s;
}

.deep-blue.el-button--primary {
  background-color: #4786FF;
  border-color: #4786FF;
  ;
}

.global-form-50 {
  margin-left: -25px;
}

.global-form-50 .el-form-item {
  padding-left: 25px;
  padding-right: 0;
  width: 50%;
  float: left;
}

.global-dialog-form .el-form-item {
  margin-bottom: 5px;
}

.global-form-50 .full-item {
  width: 100%;
}

.global-form-50 .el-select {
  float: left;
  width: 100%;
  display: block;
}

.full-form .el-form-item__label {
  padding: 0 !important;
  line-height: 30px;
}

.full-form .el-select {
  display: block;
}

/*表格*/
.global-table-box>table {
  width: 100%;
  table-layout: fixed;
  border-spacing: 0;
  border-collapse: collapse\9;
}

.global-table thead td,
.global-table th {
  line-height: 20px;
  font-size: 13px;
  padding: 15px 0;
  background-color: #F5F6FA;
  text-align: left;
  font-weight: bold;
  font-style: normal;
  margin: 0;
  color: #757b84;
  white-space: nowrap;
  border-bottom: 1px solid #EBEEF5;
}

.global-table tbody td {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: #fff;
  color: #81858e;
  border-bottom: 1px solid #F5F4F5;
  font-size: 13px;
  white-space: normal;
  word-break: break-all;
}

.global-table td,
.global-table th {
  padding-left: 15px;
  padding-right: 5px;
}

.global-table tbody td.global-strong-td {
  font-weight: bold;
  color: #333;
  border-right: 1px solid #F5F4F5;
  background-color: #FAFBFD;
  padding: 0 20px;
}

.global-border-td {
  border-right: 1px solid #F5F4F5;
}

.global-table tbody td.globla-orange-td {
  color: #FF8446;
}

.global-table tr {
  cursor: default;
}

.global-table tr.selected td {
  background-color: #e0f0ff;
}

.global-table tr:nth-child(2n):not(.global-total-tr) td {
  background-color: #fbfbfb;
}

.global-table tr:hover td {
  background-color: #F5F8FF !important;
}

.global-table~.ui-loading {
  height: 300px;
}

.global-table .el-button--mini {
  font-size: 13px;
}

.global-table a {
  padding-right: 10px;
  font-size: 13px;
}

.global-table tbody tr.global-total-tr,
.global-table tbody .global-total-tr td {
  font-size: 14px;
  font-weight: bold;
  color: #65696d;
}

.global-table tbody tr.global-subtotal-tr {
  font-weight: 500;
}

/* 无表头的表格 */
.table-without-header .global-table thead td,
.table-without-header .global-table th {
  padding-top: 0;
  padding-bottom: 0;
  border-bottom: 0;
  background: none;
}

.table-without-header .global-table td,
.table-without-header .global-table th {
  padding-left: 20px;
}

/* 表头无底色有底边的表格 */
.table-without-header-bg .global-table thead td,
.table-without-header-bg .global-table th {
  border-bottom: 1px solid #EEEEEE;
  background: none;
}

.small-prepend .el-input-group__prepend {
  padding: 0 5px !important;
  min-width: 30px;
  text-align: center;
}

/*弹窗*/
.el-dialog__body {
  padding: 10px 20px !important;
}

.ell {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/*标题*/
.global-block-tit {
  font-size: 20px;
  font-weight: bold;
  color: #55575C;
}

/*无数据*/
.global-no-data {
  text-align: center;
  font-size: 16px;
  color: #BABABA;
  padding: 20px;
}

/*进度条*/
.g-progress-list>li {
  position: relative;
  padding-top: 10px;
  border-top: 2px solid #D6D6D6;
  float: left;
  width: 20%;
  margin-bottom: 10px;
}

.g-progress-list>li:last-of-type {
  border: 0;
  width: auto;
}

.g-progress-dot {
  position: absolute;
  top: -8px;
  left: 0;
  width: 15px;
  height: 15px;
  border: 2px solid #D6D6D6;
  background: #ffffff;
  border-radius: 50%;
}

.g-progress-dot>i {
  font-size: 12px;
  color: #ffffff;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
  display: none;
}

.g-progress-text {
  font-size: 15px;
  color: #BABABA;
}

.g-progress-time {
  color: #BABABA;
  font-size: 13px;
}

.g-progress-list>li.done .g-progress-dot {
  background: #4786FF;
  border-color: #4786FF;
}

.g-progress-list>li.done {
  border-color: #4786FF;
}

.g-progress-list>li.done .g-progress-dot>i {
  display: block;
}

.g-progress-list>li.done .g-progress-text,
.g-progress-list>li.on .g-progress-text {
  color: #4786FF;
}

.g-progress-list>li.on .g-progress-dot {
  background: #4786FF;
  border-color: #4786FF;
  box-shadow: 0 0 0 4px rgba(71, 134, 255, .18);
}

.g-progress-btn {
  margin-top: 5px;
}

.global-progress-dot {
  width: 14px;
  height: 14px;
  border: 2px solid #F3F3F3;
  border-radius: 50%;
}

.on .global-progress-dot {
  border-color: #4786FF;
  background: #4786FF;
  box-shadow: 0 0 0 4px rgba(71, 134, 255, .18);
}

.global-progress-tit {
  padding-bottom: 10px;
  font-weight: bold;
  color: #55575C;
  font-size: 15px;
}

.el-step__head.is-finish .el-step__line {
  background: #4786FF;
}

.global-progress .el-step__icon.is-icon {
  width: auto;
}

.global-progress .el-step__icon-inner[class*=el-icon]:not(.is-status) {
  font-size: 16px;
}

.global-progress .el-step__title {
  font-size: 14px;
}

.global-progress .el-step__head:not(.is-finish) .el-step__line {
  border-color: #F3F3F3;
  background-color: #F3F3F3;
}

.global-mask {
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  visibility: hidden;
}

.global-table-cell {
  display: table-cell;
  vertical-align: middle;
}

.global-blue-shadow {
  box-shadow: 0 9px 23px rgba(71, 134, 255, .35);
}

.g-table-like {
  display: table;
}

.g-table-cell {
  display: table-cell;
  vertical-align: middle;
}

.router-link-active {
  display: inline-block;
}

/* 无数据 */
.global-no-data {
  text-align: center;
  font-size: 14px;
  color: #bababa;
  margin: 20px auto;
}

.global-no-data-img {
  display: inline-block;
  opacity: 0.5;
  width: 130px;
  height: 87px;
  background: url("/static/images//no-data.png") center center no-repeat;
  background-size: contain;
  margin: 20px auto;
}

/* 未读标记 */
.global-badge {
  display: inline-block;
  vertical-align: middle;
  width: 8px;
  height: 8px;
  line-height: 1.5;
  background: #f25584;
  border-radius: 50%;
  border: 1px solid #ffffff;
  position: absolute;
}

/* 表格里的tag样式 */
.global-table tbody td .el-tag {
  border: 0;
  opacity: 0.9;
  height: 26px;
  line-height: 26px;
  padding: 0 15px;
}

/*滚动条整体部分*/
.mytable-scrollbar ::-webkit-scrollbar {
  width: 20px;
  height: 20px;
}
/*滚动条的轨道*/
.mytable-scrollbar ::-webkit-scrollbar-track {
  background-color: #FFFFFF;
}
/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar ::-webkit-scrollbar-thumb {
  background-color: #bfbfbf;
  border-radius: 5px;
  border: 1px solid #F1F1F1;
  box-shadow: inset 0 0 6px rgba(0,0,0,.3);
}
.mytable-scrollbar ::-webkit-scrollbar-thumb:hover {
  background-color: #A8A8A8;
}
.mytable-scrollbar ::-webkit-scrollbar-thumb:active {
  background-color: #787878;
}
/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar ::-webkit-scrollbar-corner {
  background-color: #FFFFFF;
}