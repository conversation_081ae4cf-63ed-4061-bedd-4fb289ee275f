
// export function judgeBillByJson() {
//   return judgeBillByJson1(yh)
// const r1 = judgeBillByJson1(yh)
//   console.log('r1', r1)
//   const r2 = judgeBillByJson1(yh1)
//   console.log('r2', r2)
//   const r3 = judgeBillByJson1(yh2)
//   console.log('r3', r3)
//   const r4 = judgeBillByJson1(cd)
//   console.log('r4', r4)
//   const r5 = judgeBillByJson1(cd1)
//   console.log('r5', r5)
//   return 'ok'
// }
// type =1 批量识别汇票
export function judgeBillByJson(str, cdlisttype = 0) {
  if (!str.RequestId || !str.StructuralItems || str.StructuralItems.length === 0) {
    return
  }
  _jigou = 0 // 清除机构计数
  _jigoumodel = 0 // 清除机构模式
  _zhuanrangmodel = 0
  _zhuanrang = 0
  const data = {
    type: 0, // 类型 0 现金 1 网银 2承兑
    isCdList: false, // 是否承兑列表
    cdlist: [], // 承兑列表
    receivablesCompany: '', // 收款单位
    receiptAmount: '', // 应收金额
    paymentAccount: '', // 付款账号
    paymentBank: '', // 付款银行
    paymentCompany: '', // 付款单位
    timeOfOccurrence: '', // 发生时间
    billNo: '', // 票据号码
    drawerName: '', // 出票人
    acceptorName: '', // 承兑人
    votingDate: '', // 出票日
    isTransferred: '', // 能否转让
    billType: '', // 票据类型
    expireDate: '' // 到期日
  }
  const tmp = str.StructuralItems
  // if (cdlisttype == 1) {
  //   data.type = 2
  //   data.isCdList = true
  //   data.cdlist = billListFmt(tmp)
  //   return data
  // }
  // 判断是否承兑
  // name 出现 承兑
  if (tmp.find(item => /承兑|出票|背书|票号|能否转让/.test(item.Name))) {
    data.type = 2
    const cds = tmp.filter(item => item.Name === '承兑人')
    if (cdlisttype == 1 || (cds && cds.length > 1)) {
      //   承兑列表
      data.isCdList = true
      data.cdlist = billListFmt(tmp)
      return data
    }
    // 汇票
    if (tmp.find(item => /银行(?=.*汇票)/.test(item.Value))) {
      data.billType = '银行汇票'
    }
    // 票据号码
    const billNoItem = tmp.find(item => /票据号码|票号/.test(item.Name))
    if (billNoItem) {
      data.billNo = billNoItem.Value
    }
    // 出票人
    const drawerNameItem = tmp.find(item => /出票人((?=.*名)|(?!.*?[号帐账银行]))/.test(item.Name))
    if (drawerNameItem) {
      data.drawerName = drawerNameItem.Value
    }
    // 承兑人
    const acceptorNameItem = tmp.find(item => /承兑人(?!.*?[号帐账银行])/.test(item.Name))
    if (acceptorNameItem) {
      data.acceptorName = acceptorNameItem.Value
    }
    // 出票日
    const votingDateItem = tmp.find(item => /出票日/.test(item.Name))
    if (votingDateItem) {
      data.votingDate = votingDateItem.Value
    }
    // 到期日
    const expireDateItem = tmp.find(item => /到期日/.test(item.Name))
    if (expireDateItem) {
      data.expireDate = expireDateItem.Value
    }
    // 能否转让
    const isTransferredItem = tmp.find(item => /转让/.test(item.Name))
    if (isTransferredItem) {
      data.isTransferred = /不/.test(isTransferredItem.Value) ? '不能转让' : '能转让'
    }
  } else if (tmp.find(item => /银行|网银/.test(item.Value)) || tmp.find(item => /流水|汇款/.test(item.Name))) {
  // 判断是否银行
  // 标题 出现银行 name 出现 流水、汇款
    data.type = 1
  }
  // 收款金额
  const amount = tmp.find(item => /金额(?!.*大写)/.test(item.Name))
  if (amount) {
    data.receiptAmount = amount.Value
  }

  // 收款方
  // 户名 金额
  const ritem = tmp.find(item => /收款((?=.*名)|(?!.*?[号帐账银行]))/.test(item.Name))
  if (ritem) {
    data.receivablesCompany = ritem.Value
  }

  // 付款方
  // 户名 银行 账户
  const pitem = tmp.find(item => /(付款((?=.*名)|(?!.*?[号帐账银行])))|(^背书人)/.test(item.Name))
  if (pitem) {
    data.paymentCompany = pitem.Value
  }
  const pactitem = tmp.find(item => /付款(?=.*[帐账])(?!.*名)/.test(item.Name))
  if (pactitem) {
    data.paymentAccount = pactitem.Value
  }
  const pbankitem = tmp.find(item => /付款(?=.*行)|行名/.test(item.Name))
  if (pbankitem) {
    data.paymentBank = pbankitem.Value
  }

  const occtime = tmp.find(item => /(交易|记[账帐]|背书)(?=.*(日期|时间))/.test(item.Name))
  if (occtime) {
    data.timeOfOccurrence = occtime.Value
  }

  return data
}

export function judgeBillByStr(str) {
  if (!str) {
    return
  }
  try {
    str = JSON.parse(str)
  } catch (e) {
    console.log(e)
    return
  }
  return judgeBillByJson(str)
}

function billListFmt(data) {
  const list = []
  const keyMap = {}
  for (let i = 0; i < data.length; i++) {
    const tmp = data[i]
    const key = nameConvert(tmp.Name, tmp.Value)
    if (!key) {
      continue
    }
    // 格式化
    if (key === 'isTransferred') {
      tmp.Value = /不/.test(tmp.Value) ? '不能转让' : '能转让'
    }
    if (key === 'billType') {
      tmp.Value = /商|业/.test(tmp.Value) ? '商承' : '银承'
    }
    let index = 0
    if (keyMap.hasOwnProperty(key)) {
      // 获取下标
      index = keyMap[key]
    }
    if (list.length > index) {
      list[index][key] = tmp.Value
    } else {
      list.push({ [key]: tmp.Value })
    }
    keyMap[key] = index + 1
    // let isAdd = true
    // for (let j = 0; j < list.length; j++) {
    //   const listItem = list[j]
    //   if (listItem.hasOwnProperty(key)) {
    //     continue
    //   } else {
    //     listItem[key] = tmp.Value
    //     isAdd = false
    //     break
    //   }
    // }
    // if (isAdd) {
    //   list.push({ [key]: tmp.Value })
    // }
  }
  return list

  // for (let i = 0; i < data.length; i++) {
  //   // debugger
  //   // 循环
  //   const tmp = data[i]
  //   const key = nameConvert(tmp.Name)
  //   if (!key) {
  //     continue
  //   }
  //   // if (tmp.ItemCoord && tmp.ItemCoord.Y) {
  //   //   if (y) {
  //   //     if (Math.abs(y - tmp.ItemCoord.Y) > yer) {
  //   //       list.push(item)
  //   //       item = {}
  //   //       y = tmp.ItemCoord.Y
  //   //     }
  //   //   } else {
  //   //     y = tmp.ItemCoord.Y
  //   //   }
  //   // }
  //   if (item.hasOwnProperty(key)) {
  //     if (num >= numRep) {
  //       list.push(item)
  //       item = {}
  //       num = 0
  //     } else {
  //       tmpList.push(tmp)
  //       continue
  //     }
  //     // 判断 同一个 key，放入缓存
  //     // 缓存已存在 重起一行
  //     // if (tmpMap.hasOwnProperty(key)) {
  //     //   list.push(item)
  //     //   item = {}
  //     //   // 缓存数据释放
  //     //   forTmp(tmpMap, item)
  //     //   tmpMap = {}
  //     //   tmpMap[key] = tmp.Value
  //     //   // list.push(item)
  //     //   // item = {}

  //     //   // 循环缓存
  //     // } else {
  //     //   tmpMap[key] = tmp.Value
  //     // }
  //     // continue
  //   }
  //   item[key] = tmp.Value
  // }
  // // 缓存数据释放
  // forTmp(tmpMap, item)
  // list.push(item)
  // return list
}
// function forTmp(data, item) {
//   for (const [key, value] of Object.entries(data)) {
//     item[key] = value
//   }
// }
// function forList(data) {
//   for (let i = 0; i < data.length; i++) {
//     const tmp = data[i]
//     const key = nameConvert(tmp.Name)
//     if (!key) {
//       continue
//     }
//     if (item.hasOwnProperty(key)) {
//       if (num >= numRep) {
//         list.push(item)
//         item = {}
//         num = 0
//       } else {
//         tmpList.push(tmp)
//         continue
//       }
//     }
//     item[key] = tmp.Value
//   }
// }
let _jigou = 0
// 0 出票人 承兑人 背书人
// 1 承兑人 背书人 出票人
// 2 背书人 承兑人 出票人
// 3 背书人 出票人 承兑人
let _jigoumodel = 0

// 转让
let _zhuanrang = 0
let _zhuanrangmodel = 0 // 0 未开启 1 开启
// 名称转换
// 票号 票据类型 能否转让 出票日 到期日 票面金额 出票人 承兑人 背书人
function nameConvert(name, value = '') {
  let key
  switch (true) {
    case /累计/.test(name):
      key = undefined
      break
    case /票号|号码/.test(name):
      key = 'billNo'
      break
    case /票据类型/.test(name):
      key = 'billType'
      break
    case /转让/.test(name):
      // 判断是否日期
      if (/年|月|日/.test(value)) {
        // 打开转让转换 模式
        _zhuanrangmodel = 1
      }
      if (_zhuanrangmodel === 1) {
        if (_zhuanrang % 2 === 0) {
          key = 'issueDate'
        } else {
          key = 'dueDate'
        }
        _zhuanrang++
      } else {
        key = 'isTransferred'
      }
      break
    case /出票日/.test(name):
      key = 'issueDate'
      break
    case /到期日/.test(name):
      key = 'dueDate'
      break
    case /金额/.test(name):
      key = 'billAmount'
      break
    case /出票人/.test(name):
      // 更改模式
      _jigoumodel = 0
      key = 'issuer'
      break
    case /承兑人/.test(name):
      key = 'acceptor'
      if (!value || value.length < 2) {
        key =undefined
      }
      break
    case /背书人/.test(name):
      key = 'underwriter'
      if (!value || value.length < 2) {
        key =undefined
      }
      break
    case /机构/.test(name):
      if (/银行/.test(value)) {
        key = 'acceptor'
        _jigoumodel = 0
        _jigou = 0
        break
      }
      if (/物流/.test(value)) {
        key = 'underwriter'
        _jigoumodel = 0
        _jigou = 0
        break
      }
      // 3 变 1
      if (_jigou % 3 === 0) {
        // 银行 一般 为 承兑人
        if (/银行/.test(value)) {
          _jigoumodel = 1
        }
        // 特殊公司 处理 珠海港成功航运有限公司 鞍钢股份有限公司 按内容处理
        if (/珠海港成功航运/.test(value)) {
          _jigoumodel = 3
        }
        if (_jigoumodel === 1 && !/银行/.test(value)) {
          // 改变模式
          _jigoumodel = 2
        }
        key = _jigoumodel === 0 ? 'issuer' // 出票人
          : _jigoumodel === 1 ? 'acceptor'
            : _jigoumodel === 2 ? 'underwriter' : 'underwriter' // 机构特殊处理
      } else if (_jigou % 3 === 1) {
        key = _jigoumodel === 0 ? 'acceptor'
          : _jigoumodel === 1 ? 'underwriter'
            : _jigoumodel === 2 ? 'acceptor' : 'issuer'// 机构特殊处理
      } else if (_jigou % 3 === 2) {
        key = _jigoumodel === 0 ? 'underwriter'
          : _jigoumodel === 1 ? 'issuer'
            : _jigoumodel === 2 ? 'issuer' : 'acceptor' // 机构特殊处理
      }
      _jigou++
      break
    default:
      key = undefined
  }
  return key
}
