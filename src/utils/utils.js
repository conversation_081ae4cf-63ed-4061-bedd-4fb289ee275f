function getColumnIndexMap(table) {
  // console.log(table);
  var res = {}
  table.getColumns().forEach((column, i) => {
    if (column.property) {
      var prop = convertPropName(column.property)
      res[prop] = i
    }
  })

  return res
}

function convertPropName(prop) {
  if (prop == null) {
    return null
  }
  // 把类似xxx.0这种带数字的转换成xxx[0]
  return prop.replace(/\.(\d+)/g, '[$1]')
}

// function convertPropName(prop) {
//   if (prop == null) {
//     return null;
//   }
//   // 把类似xxx.0这种带数字的转换成xxx[0]
//   return prop.replace(/\.(\d+)/g, '[$1]');;
// }

function ifSameByProps(data1, data2, byProps) {
  if (data1 == null && data2 == null) {
    return false
  }
  if (data1 == null || data2 == null) {
    return false
  }
  if (byProps == null || byProps.length == 0) {
    return false
  }

  for (var i = 0; i < byProps.length; i++) {
    var propNameOri = byProps[i]
    const _propName = convertPropName(propNameOri)

    const valueOfData1 = eval('data1.' + _propName)
    const valueOfData2 = eval('data2.' + _propName)
    // if (!valueOfData1 || !valueOfData2) {
    //   return false;
    // }

    if (valueOfData1 != valueOfData2) {
      return false
    }
  }
  return true
}
function getFloatValue(v) {
  if (!v) {
    return 0.0
  }
  return parseFloat(v)
}

/**
 * 根据指定的几个属性值是否相同计算出某一个属性的合计值，并插入到相应数据组后面
 * @export
 * @param {Array} dataList 数据集合
 * @param {String} calcProp 需要计算合计的属性
 * @param {Array} byProps 以此属性名数组为依据判断是否合并计算和值
 * @param {Function} formatter 处理每一个数字的处理器，大部分是需要先四射五入，再计算和值
 */
export function fillSubTotal(dataList, calcProp, byProps, formatter = function(num) { return num }) {
  if (byProps == null || byProps.length == 0) {
    return
  }
  if (dataList == null || dataList.length < 2) {
    return
  }

  var subTotalArray = []
  var _currentSubTotalInfo

  for (let rowIndex = 1; rowIndex < dataList.length; rowIndex++) {
    var prevData = dataList[rowIndex - 1]
    var currentData = dataList[rowIndex]
    var nextData = (rowIndex + 1) >= dataList.length ? null : dataList[rowIndex + 1]

    var sameToPrev = ifSameByProps(prevData, currentData, byProps)
    var sameToNext = ifSameByProps(currentData, nextData, byProps)

    if (sameToPrev) {
      var prevValue = getFloatValue(formatter(getFloatValue(eval('prevData.' + convertPropName(calcProp)))))
      var currentValue = getFloatValue(formatter(getFloatValue(eval('currentData.' + convertPropName(calcProp)))))

      if (!_currentSubTotalInfo) {
        var subTotalData = {}
        subTotalData._mergeOfFirst = currentData
        subTotalData[calcProp] = prevValue
        byProps.forEach(pName => {
          subTotalData[pName] = '小计'
        })
        _currentSubTotalInfo = { 'insertIndex': rowIndex - 1, 'subTotal': subTotalData }
      }

      _currentSubTotalInfo.subTotal[calcProp] = _currentSubTotalInfo.subTotal[calcProp] + currentValue
      _currentSubTotalInfo.insertIndex = rowIndex
    }

    // 当前有正在计算小计的数据，并且当前数据和下一个要循环的数据不相等，说明要结束当前计算的小计
    if (_currentSubTotalInfo && !sameToNext) {
      subTotalArray.push(_currentSubTotalInfo)
      _currentSubTotalInfo = null
    }
  }

  // 倒序从后往前插入，已插入的数据不会对后面需要插入数据的索引有影响，反之从前往后插入的话就不行
  for (var i = subTotalArray.length - 1; i >= 0; i--) {
    var subTotalInfo = subTotalArray[i]
    // console.log(subTotalInfo.insertIndex);
    dataList.splice(subTotalInfo.insertIndex + 1, 0, subTotalInfo.subTotal)
  }
}

/**
 * 根据指定的属性值是否相同，计算合并其他几个属性的单元格的数组
 * @export
 * @param {*} table vxe-table 的引用对象，如: this.$refs.xTable
 * @param {Array} dataList 数据集合
 * @param {Array} propsByBase 以此属性名数组为依据判断是否需要合并单元格
 * @param {Array} props 需要合并单元格的属性数组
 * @param {Array} [merges=[]] 单元格合并描述信息数组，如果没有可以传null，有的话系统会在此基础上继续添加新的‘单元格合并描述信息’对象
 * @return {Array} 计算好的单元格合并描述信息数组
 */
export function calcMergeOfColumns2(table, dataList, propsByBase, props, merges = []) {
  if (props == null || props.length == 0) {
    return merges
  }
  if (propsByBase == null || propsByBase.length == 0) {

  }
  if (dataList == null || dataList.length < 2) {
    return merges
  }

  var columns = getColumnIndexMap(table)

  // console.log('==========');// currentSameValueOfColumns
  var currentSameValueOfRows = null
  for (let rowIndex = 1; rowIndex < dataList.length; rowIndex++) {
    var prevData = dataList[rowIndex - 1]
    var currentData = dataList[rowIndex]
    var nextData = (rowIndex + 1) >= dataList.length ? null : dataList[rowIndex + 1]

    var sameToPrev = ifSameByProps(prevData, currentData, propsByBase)
    var sameToNext = ifSameByProps(currentData, nextData, propsByBase)

    // 判断是否有新发现相同的值
    if (sameToPrev && !currentSameValueOfRows) {
      currentSameValueOfRows = { row: rowIndex - 1, col: 0, rowspan: 1, colspan: 1 }
    }

    // 判断是否应该结束当前有相同值的列
    if (!sameToNext && currentSameValueOfRows) {
      currentSameValueOfRows.rowspan = rowIndex - currentSameValueOfRows.row + 1
      props.forEach(pNameOri => {
        var _propName = convertPropName(pNameOri)
        var copy = JSON.parse(JSON.stringify(currentSameValueOfRows))

        // console.log(copy)
        copy.col = columns[_propName]
        merges.push(copy)
      })

      currentSameValueOfRows = null
    }
  }

  // console.log('==========',merges);
  return merges
}

/**
 * 根据指定的属性值是否相同，计算合并单元格数组
 * @export
 * @param {*} table vxe-table 的引用对象，如: this.$refs.xTable
 * @param {Array} dataList 数据集合
 * @param {Array} props 需要合并单元格的属性数组
 * @param {Array} [merges=[]] 单元格合并描述信息数组，如果没有可以传null，有的话系统会在此基础上继续添加新的‘单元格合并描述信息’对象
 * @return {Array} 计算好的单元格合并描述信息数组
 */
export function calcMergeOfColumns(table, dataList, props, merges = []) {
  return calcMergeOfColumns2(table, dataList, props, props, merges)
}

/**
 * 判断是否清空级联数据
 */
export function isClearCascade(oldVal) {
  if (oldVal) {
    // 清空
    return Promise.resolve()
  }
  return Promise.reject()
}

export function tryCatch(fn, ...args) {
  try {
    const result = fn.apply(null, args);

    if (result.then) {
      return new Promise(resolve => {
          result
            .then(v => resolve([undefined, v]))
            .catch(e => resolve([e, undefined]))
      });
    }

    return [undefined, result];
  } catch (e) {
    return [e, undefined];
  }
}
