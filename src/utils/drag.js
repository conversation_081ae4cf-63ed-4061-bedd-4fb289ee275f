import Vue from 'vue'

// v-drag: 弹窗拖拽
Vue.directive('drag', {
  bind: function (el) {
    // 鼠标按下的时候
    el.onmousedown = function (ed) {
      // 需要移动的元素宽高
      let box_width = el.clientWidth
      let box_height = el.clientHeight
      // 屏幕宽高
      let screen_width = document.documentElement.clientWidth
      let screen_height = document.documentElement.clientHeight
      // 屏幕内可移动区域的最大宽高
      let maxX = screen_width - box_width
      let maxY = screen_height - box_height
      // 鼠标相对于可移动元素内的坐标点
      let disx = ed.clientX - el.offsetLeft
      let disy = ed.clientY - el.offsetTop
      // 移动的时候
      document.onmousemove = function (em) {
        // 阻止事件冒泡
        em.preventDefault()
        // 可移动元素相对于屏幕的坐标点
        let nx = em.clientX - disx
        let ny = em.clientY - disy
        // 处理边界问题
        nx = Math.max(nx, 0)
        nx = Math.min(nx, maxX)
        if (nx > maxX) {
          nx = maxX
        }
        ny = Math.max(ny, 0)
        ny = Math.min(ny, maxY)
        if (ny > maxY){
          ny = maxY
        }
        el.style.left = nx + 'px'
        el.style.top = ny + 'px'
      }
      // 鼠标释放的时候
      document.onmouseup = function () {
        document.onmousemove = null
      }
    }
  },
  inserted: function (el) { },
  //当VNode更新的时候会执行updated，可以触发多次
  updated: function (el) { }
})
