import StartContract from '@/components/start-contract'
export default {
  components: { StartContract },
  data() {
    return {
      contractDialogShow: false,
    };
  },
  methods: {
    contractComplete(res, callback) {
      // 发起合同完成后的回调
      console.log('contractComplete... ', res, callback)
    },
    openContractDialog() {
      // 打开合同iframe
      this.contractDialogShow = true
    },
    closeContractDialog() {
      // 关闭合同iframe
      this.contractDialogShow = false
    },
    setContractDialogPayload(cb) {
      // 设置发起合同时传递给合同iframe的payload
    },
    onSuccessBackPage() {
      // 合同流程成功后点击返回按钮的回调
    }
  },
};
