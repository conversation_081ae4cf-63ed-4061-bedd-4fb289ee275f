<template>
  <el-tabs v-model="activeName" @tab-click="handleClick"  type="border-card">
    <el-tab-pane label="进项" name="first"><income></income></el-tab-pane>
    <el-tab-pane label="销项" name="second"><outcome></outcome></el-tab-pane>
  </el-tabs>
</template>
<script>
import income from './income.vue';
import outcome from './outcomeTab.vue';
export default{
  name: 'CertificateManagementTaxation',
  components: { income,outcome },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
