<template>
  <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
    <!-- todo -->
    <el-tab-pane label="操作页面" name="first">汇总操作</el-tab-pane>
    <el-tab-pane label="销项明细" name="second"><commodity-income></commodity-income></el-tab-pane>
  </el-tabs>
</template>
<script>
import commodityIncome from './commodityIncome.vue';
import shipIncome from './shipIncome.vue';
export default{
  name: 'CertificateManagementTaxationIncome',
  components: { commodityIncome,shipIncome },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
