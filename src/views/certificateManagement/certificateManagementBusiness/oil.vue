<template>
  <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
    <el-tab-pane label="流水" name="first">
      <on-account></on-account>
    </el-tab-pane>
    <el-tab-pane label="库存" name="second">
      <oil-store></oil-store>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import onAccount from './oil/oilAccount.vue';
import oilStore from './oil/oilStore.vue';
export default{
  name: 'CertificateManagementBusinessOil',
  components: { onAccount,oilStore },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
