<template>
   <section>
    <div class="app-container">
      <el-form :inline="true" :model="params" class="query-form demo-form-inline" style="padding:10px;">
        <el-form-item label="船舶名称">
          <el-input
            v-model="params.shipName"
            placeholder="船舶名称"
            clearable
            size="small"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="时间范围">
           <el-date-picker
              v-model="dateRange"
              type="monthrange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              :picker-options="pickerOptions"
              style="width: 350px"
            />
        </el-form-item>
    <!-- 显示月列表 船舶 月 列表 按钮（收入、成本）-->

        <el-button @click="loadData(1)">查询</el-button>
      </el-form>
           <vxe-table
              v-loading="tableLoading"
                  ref="multipleTable"
              tooltip-effect="dark"
              stripe
              class="mytable-scrollbar"
              :data="tableData">
              <vxe-table-column field="shipName"  align="center"  title="船舶名称"></vxe-table-column>
              <vxe-table-column field="oilName"  align="center"  title="油品"></vxe-table-column>
              <vxe-table-column field="waterTime"  align="center"  title="时间"></vxe-table-column>
              <vxe-table-column  align="center"  title="加油">
                <template slot-scope="scope">
                  <div v-if="scope.row.waterType == 1">
                    <div>金额：{{ formatNumber(scope.row.actualFreight) }} </div>
                    <div>税额：{{ formatNumber(scope.row.taxAmount) }}</div>
                    <div>价税合计：{{ formatNumber(scope.row.waterBalance) }}</div>
                    <div>吨位：{{ scope.row.tonnage  }} </div>
                  </div>
                </template>
              </vxe-table-column>
              <vxe-table-column  align="center"  title="消耗">
                <template slot-scope="scope">
                  <div v-if="scope.row.waterType == -1">
                    <div>金额：{{ formatNumber(scope.row.actualFreight) }} </div>
                    <div>吨位：{{ scope.row.tonnage  }} </div>
                  </div>
                </template>
              </vxe-table-column>
              <!-- 操作 -->
              <vxe-table-column title="操作" align="center">
                <template slot-scope="scope">
                  <!-- <el-button type="text" @click="detailHandle(scope.row)">查看详情</el-button> -->
                  <el-button v-if="scope.row[codeListKey] && scope.row[codeListCount]==0" type="text" size="small" @click="showYacc(scope.row,codeKey)">生成凭证</el-button>
                  <el-button v-if="scope.row[codeListKey] && scope.row[codeListCount] > 0" type="text" size="small" @click="showTranByCode(scope.row,codeKey)">查看凭证</el-button>
                    <el-button v-if="scope.row[codeConsumptionListKey] && scope.row[codeConsumptionListCount]==0" type="text" size="small" @click="showYacc(scope.row,codeConsumptionKey)">生成凭证</el-button>
                  <el-button v-if="scope.row[codeConsumptionListKey] && scope.row[codeConsumptionListCount] > 0" type="text" size="small" @click="showTranByCode(scope.row,codeConsumptionKey)">查看凭证</el-button>
                </template>
              </vxe-table-column>
              </vxe-table>
              <div style="text-align: right;">
              <el-pagination
                hide-on-single-page
                background
                :total="total"
                :page-size="pageSize"
                :current-page="pageNo"
                layout="prev, pager, next"
                @current-change="loadData"
              />
              </div>


    </div>
    <el-dialog title="" :visible.sync="dialogVisible" width="width" :before-close="dialogBeforeClose">
      <el-row :gutter="10" type="flex" justify="center">
          <el-col :span="12" style="text-align: center;">{{selRow.shipName}}&nbsp;&nbsp;&nbsp;{{selRow.dataYearMonth}}月 账单</el-col>
      </el-row>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="收入" name="income">
          <ship-month-table :outcomeList="incomeList"></ship-month-table>
        </el-tab-pane>
        <el-tab-pane label="成本" name="outcome">
          <ship-month-table :outcomeList="outcomeList" :oilList="oilList"></ship-month-table>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer">
          <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 查看凭证对话框 -->
    <el-dialog
      :visible.sync="dialogTarnShow"
      title="查看凭证"
      width="65%"
      :before-close="()=>dialogTarnShow = false"
    >
      <resultTrans v-for="item in showTableTrans" :key="item.id" :tableData="item" :classList="kjClassList"></resultTrans>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogTarnShow = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成凭证对话框 -->
    <el-dialog
      :visible.sync="dialogYacc"
      title="生成凭证"
      width="80%"
      :before-close="()=>dialogYacc = false"
    >
      <span>&nbsp;&nbsp;账期:&nbsp;<el-date-picker
        v-model="monthDateAcc"
        type="month"
        value-format="yyyyMM"
        placeholder="选择日期">
      </el-date-picker>
      </span>
        <Subacc :tableData="tableDataAcc" :codeKey="tempCodeKey"  @update:tableData="v=> tableDataAcc = v" :accounts="ledgerAccountList" @ledgerAccount="v=> ledgerAccount = v" :classList="kjClassList"></Subacc>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogYacc = false">取消</el-button>
          <el-button type="primary" @click="saveYacc()">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
       <!-- Account Flow Details Dialog -->
        <el-drawer title="账户明细" :visible.sync="detailsDialogVisible" size="900px" direction="rtl">
          <div class="flow-summary" style="margin: 0 24px;">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="期初">{{ flowData.initializationMoney }}</el-descriptions-item>
              <el-descriptions-item label="期末">{{ flowData.endingBalance }}</el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="flow-table" style="margin: 20px 24px;">
            <el-table
              :data="flowData.inventryAccountWaters"
              style="width: 100%"
              stripe
              size="medium"
              max-height="500"
            >
              <el-table-column type="index" label="#" width="50" align="center" />
              <el-table-column prop="createTime" label="时间" width="110" align="center">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.waterTime||scope.row.createTime ) }}
                </template>
              </el-table-column>

              <el-table-column prop="waterType" label="类型" width="80" align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.waterType === 1 ? 'success' : 'danger'" size="small">
                    {{ scope.row.waterType === 1 ? '进项' : '消耗' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="tonnage" label="进项数量" width="90" align="center">
                <template slot-scope="scope">
                  {{ scope.row.waterType === 1 ? formatNumber(scope.row.tonnage ) : '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="tonnage" label="消耗数量" width="90" align="center">
                <template slot-scope="scope">
                  {{ scope.row.waterType === -1 ? formatNumber(scope.row.tonnage ) : '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="tprice" label="单价" width="90" align="center">
                <template slot-scope="scope">
                  {{ formatNumber( scope.row.tprice ) }}
                </template>
              </el-table-column>
              <el-table-column prop="waterBalance" label="金额" width="100" align="center">
                <template slot-scope="scope">
                  {{ formatNumber( scope.row.waterBalance ) }}
                </template>
              </el-table-column>
              <el-table-column prop="priceTaxRate" label="税率" width="80" align="center">
                <template slot-scope="scope">
                  {{ scope.row.priceTaxRate ? (scope.row.priceTaxRate * 100).toFixed(0) + '%' : '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="supplierShortName" label="供应商" width="100" align="center" show-overflow-tooltip />
              <el-table-column prop="voyageSummary" label="船期" width="120" align="center" show-overflow-tooltip />
              <el-table-column prop="remarks" label="备注" min-width="150" align="center" show-overflow-tooltip />
            </el-table>
          </div>
        </el-drawer>
  </section>
</template>
<script>
import {oilConsumeList,oilWaterDetail,getOilStats,oilAccountFlowList} from '@/api/system/onAccount.js'
import {tryCatch} from '@/utils/utils'
import ShipMonthTable from '@/views/components/shipMonthTable/index.vue'
import currency from 'currency.js'
import 'vxe-table/lib/style.css'
import {getAcctgTransListByCode, getHaokjSub, getIsCodeByPidAndCode, getStowageVocherListByCode, saveVoucher as saveVoucherProcessapi} from '@/api/business/processapi'
import resultTrans from '@/views/system/businessPayment/resultTrans.vue'
import Subacc from '@/views/system/businessPayment/subacc.vue'
import dayjs from 'dayjs'
export default{
  name: 'oilAccount',
  components: {
    ShipMonthTable,
    resultTrans,
    Subacc
  },
  data(){
    return {
      pageNo:1,
      pageSize:10,
      oilTotal:0,
      oilPageSize:10,
      oilPageNo:1,
      activeName:'outcome',
      total:0,
      tableData:[],
      tableLoading:false,
      params:{
        shipName:'',
      },

      incomeList:[],
      outcomeList:[],
      inCostList:[],
      outCostList:[],
      oilList:[],
      dialogVisible:false,
      selRow:{},

      // 凭证相关数据
      codeKey: 'oilWater_pzd',
      codeListKey: 'rKey',
      codeListCount: 'rCount',
      codeConsumptionKey: 'oilWaterConsumption_pzd',
      codeConsumptionListKey: 'rConsumptionKey',
      codeConsumptionListCount: 'rConsumptionCount',
      monthDateAcc: dayjs().format('YYYYMM'),
      kjClassList: [],
      tableDataAcc: [],
      tempCodeKey:'',
      dialogYacc: false,
      showTableTrans: [],
      dialogTarnShow: false,
      ledgerAccount: '',
      ledgerAccountList: [],
      tableProcessIdAcc: '',
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近半年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
            picker.$emit('pick', [start, end])
          }
        }],
        onPick: ({ maxDate, minDate }) => {
          if (minDate && maxDate) {
            const threeYearsInMs = 3 * 365 * 24 * 60 * 60 * 1000
            const diffInMs = maxDate - minDate
            if (diffInMs > threeYearsInMs) {
              this.$message.warning('时间范围不能超过3年')
              this.dateRange = []
            }
          }
        },
        disabledDate(time) {
          const currentDate = dayjs()
          return dayjs(time).isAfter(currentDate, 'month')
        }
      },
      activeOilName:'owater',
      oilList:[],
      detailsDialogVisible:false,
      flowData:{}
    }
  },
  mounted(){
    this.loadData()
    this.loadKeMuList()

  },
  computed: {
    monthDateStr() {
      return dayjs(this.monthDateAcc).format('YYYY年MM月')
    },
    totalDebit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.debit||0)), 0);
    },
    totalCredit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.credit||0)), 0);
    },
  },
  methods: {
    formatDateTime(date){
      return dayjs(date).format('YYYY-MM-DD')
    },
     handleViewDetails(row) {
      this.currentRow = row

      let startMonth = dayjs().format('YYYY-MM')
      let endMonth =dayjs().format('YYYY-MM')
      if(this.dateRange && this.dateRange.length === 2){
        startMonth = this.dateRange[0]
        endMonth = this.dateRange[1]
      }
      const params = {
        startDate: startMonth,
        endDate: endMonth,
        inventryAccountId: row.id
      }
      this.flowData = {}
      oilAccountFlowList(params)
        .then(res => {
          this.flowData = res.data && res.data.content || {
            initializationMoney: 0,
            endingBalance: 0,
            inventryAccountWaters: []
          }
          this.detailsDialogVisible = true
        })
        .catch(err => {
          console.error('Failed to load account flow:', err)
          this.$message.error('获取账户明细失败')
        })
    },
    loadOilStats(){
      let startMonth = dayjs().format('YYYY-MM')
      let endMonth =dayjs().format('YYYY-MM')
      if(this.dateRange && this.dateRange.length === 2){
        startMonth = this.dateRange[0]
        endMonth = this.dateRange[1]
      }
      const data = {
        ...this.params,
        pageNo:this.oilPageNo,
        pageSize:this.oilPageSize,
        startDate:startMonth,
        endDate:endMonth
      }
      getOilStats(data).then(res => {
        console.log('209',res)
        if (res && res.data) {
          this.oilList = res.data.content || []

        }
      })
    },
    // 凭证相关方法
    loadKeMuList() {
      getHaokjSub().then(res => {
        if (res && res.data) {
          this.kjClassList = res.data || []
          this.upSubIdByFind()
        }
      })
    },
    upSubIdByFind() {
      if (this.tableDataAcc && this.tableDataAcc.length > 0 && this.kjClassList && this.kjClassList.length > 0) {
        for (let i = 0; i < this.tableDataAcc.length; i++) {
          if (this.tableDataAcc[i].findSub && !this.tableDataAcc[i].findSubId) {
            try {
              const fundsCode = this.kjClassList.find(item => item.treePath.startsWith(this.tableDataAcc[i].subject + '^') && item.glAccountName === this.tableDataAcc[i].findSub)
              if (fundsCode) {
                this.tableDataAcc[i].findSubId = fundsCode.glAccountCode
                this.tableDataAcc[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }
      }
    },
    saveAccTable(type = '1', func = undefined) {
      // 验证数据
      if (this.totalCredit !== this.totalDebit) {
        this.$message.error('借方金额与贷方金额不一致')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })

      saveVoucherProcessapi(this.tableProcessIdAcc, type, this.monthDateAcc, this.tableDataAcc, '0', this.ledgerAccount).then((data) => {
        if (data.result) {
          this.$message.success('保存成功')
          // biome-ignore lint/complexity/useOptionalChain: <explanation>
          func && func()
        } else {
          this.$message.error(data.msg || '保存失败')
        }
        // this.loadIsCodeByPidAndCode(this.tableProcessIdAcc, type)
        this.loadIsCodeByPidAndCode(this.tableProcessIdAcc, type).then((res) => {
            // if (type == this.codeKeyYF) {
              // this.upPzCodeToList(res,this.codeListKeyYF,this.codeListCountYF)
            // } else {
            if(type===this.codeConsumptionKey){
                this.upPzCodeToList(res,this.codeConsumptionListKey,this.codeConsumptionListCount)
            }else{
              this.upPzCodeToList(res,this.codeListKey,this.codeListCount)
            }

            // }
        })
        loading.close()
      }).catch(() => {
        this.$message.error('保存失败')
        loading.close()
      })
    },
    loadIsCodeByPidAndCode(pids, code) {
      return getIsCodeByPidAndCode(pids,code)
    },
    saveYacc() {
      this.saveAccTable(this.tempCodeKey, () => {
        this.dialogYacc = false
      })
    },
    showYacc(data,codeKey) {
      this.tempCodeKey = codeKey
      this.loadAccTable(data.id, this.tempCodeKey, () => {
        this.dialogYacc = true
      })
    },
    loadAccTable(pid, type='1', func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.tableDataAcc = []
      this.tableProcessIdAcc = pid

      getStowageVocherListByCode(pid, type).then((res) => {
        this.tableDataAccCode(res)
          // biome-ignore lint/complexity/useOptionalChain: <explanation>
        func && func()
        loading.close()
      }).catch((err) => {
        this.$message.error(err.message || '暂不支持此类型')
        loading.close()
      })
    },
    tableDataAccCode(res) {
      // biome-ignore lint/complexity/useOptionalChain: <explanation>
      const list = res && res.data ? res.data : []
      // biome-ignore lint/complexity/useOptionalChain: <explanation>
      this.voucherCode = res && res.code
      this.tableDataAcc = list || []
      // biome-ignore lint/complexity/useOptionalChain: <explanation>
      this.ledgerAccountList = res && res.accounts || []
      this.upSubIdByFind()
    },
    showTranByCode(data,codeKey) {
      this.tempCodeKey = codeKey
      this.loadTranListByProcessIdAndCode(data.id, this.tempCodeKey, () => {
        this.dialogTarnShow = true
      })
    },
    loadTranListByProcessIdAndCode(pid, code, func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransListByCode(pid, code).then(res => {
        this.showTableTrans = res.result
        // biome-ignore lint/complexity/useOptionalChain: <explanation>
        func && func()
      }).finally(() => {
        loading.close()
      })
    },

    // 原有方法
    handleClick(tab, event) {
      console.log(tab, event);
    },
    dialogBeforeClose(){
      this.dialogVisible = false
    },
   async loadData(pageNo=undefined){
      this.loadOilStats()
      if(pageNo){
        this.pageNo = pageNo
      }
      let startMonth=''
      let endMonth = ''
      if(this.dateRange && this.dateRange.length === 2){
        startMonth = this.dateRange[0]
        endMonth = this.dateRange[1]
      }
      const query = {
        pageNo:this.pageNo,
        pageSize:this.pageSize,
        startMonth:startMonth,
        endMonth:endMonth,
        ...this.params
      }
      const [err,res] = await tryCatch(oilConsumeList,query)
      if(err || !res.data){
        // this.$message.error(err.message||res.resultMsg||'加载失败')
        // biome-ignore lint/complexity/useOptionalChain: <explanation>
                this.$message.error(err && err.message||res && res.resultMsg||'加载失败')
        return
      }
      const resData = res.data
      // console.log('resData',resData)
      this.tableData = resData.content
      this.total = resData.totalElements

      // 加载凭证状态
      this.loadVoucherStatus()
    },

    // 加载凭证状态
    loadVoucherStatus() {
      // biome-ignore lint/complexity/useOptionalChain: <explanation>
      const pids = this.tableData && this.tableData
      .filter(item => item.waterType == 1)
      .map(item => item.id).join(',')

      // biome-ignore lint/complexity/useOptionalChain: <explanation>
      const pidsConsumption = this.tableData && this.tableData
      .filter(item => item.waterType == -1)
      .map(item => item.id).join(',')
    Promise.all([this.loadIsCodeByPidAndCode(pids, this.codeKey),
                  this.loadIsCodeByPidAndCode(pidsConsumption, this.codeConsumptionKey)])
          .then(([res1,res2]) => {
              this.upPzCodeToList(res1, this.codeListKey, this.codeListCount)
              this.upPzCodeToList(res2, this.codeConsumptionListKey, this.codeConsumptionListCount)
          })
    //   Promise.all([this.loadIsCodeByPidAndCode(pids, this.codeKey)]).then(([res1]) => {
    //     this.upPzCodeToList(res1, this.codeListKey, this.codeListCount)
    //     // this.upPzCodeToList(res2,this.codeListKeyYF,this.codeListCountYF)
    // })

      // getIsCodeByPidAndCode(pids, this.codeKey).then((res) => {
      //   // if (res && res.length > 0) {
      //   //   // biome-ignore lint/complexity/noForEach: <explanation>
      //   //   res.forEach(item => {
      //   //     const tableItem = this.tableData.find(d => d.id === item.id)
      //   //     if (tableItem) {
      //   //       tableItem[this.codeListKey] = this.codeKey
      //   //       tableItem[this.codeListCount] = item.count || 0
      //   //     }
      //   //   })
      //   //   this.tableData = this.tableData.slice()
      //   // }
      //   this.upPzCodeToList(res,this.codeListKey,this.codeListCount)
      // })
    },
    upPzCodeToList(res,key,countKey) {
      if (!res) {
          return
      }
      // for (let i = 0; i < this.tableData.length; i++) {
      //     const item = this.tableData[i]
      //     if (res[item.id]) {
      //         item[key] = res[item.id].code
      //         item[countKey] = res[item.id].count
      //     }
      // }
      // this.tableData = this.tableData.slice()
      this.tableData = this.tableData.map(item => {
      if (res[item.id]) {
          return {
            ...item,
            [key]: res[item.id].code,
            [countKey]: res[item.id].count,
          };
        }
        return item;
      });
    },
    priceFormat({row, column, cellValue, index}) {
      let v = 0;
      if (cellValue !== undefined && cellValue != null && cellValue !== "") {
        v = cellValue.toFixed(2);
      }
      return  currency(v,{ symbol: '', precision: 2 }).format()
    },
    getTaxRateLabel(tax){
      return tax ? `${(tax * 100).toFixed(0)}%` : '无税'
    },
    formatNumber(num) {
      if(num === undefined || num === null || num === ''){
        return '--'
      }
      return new Intl.NumberFormat('zh-CN').format(num)
    },
    resetDetailList(){
      this.incomeList = []
      this.outcomeList = []
      this.inCostList = []
      this.outCostList = []
      this.oilList=[]
    },
    async detailHandle(row){
      this.selRow = row
      const id = row.id
      this.dialogVisible=true
      this.resetDetailList()
      const [err,res] = await tryCatch(oilWaterDetail,id)
      if(err || res.resultCode !== '0'){
        // biome-ignore lint/complexity/useOptionalChain: <explanation>
        this.$message.error(err && err.message||res && res.resultMsg||'加载失败')
        return
      }
      this.inCostList = this.sortCostTypes(res.inCostList)
      this.outCostList = this.sortCostTypes(res.outCostList)
      this.incomeList = this.sortCostPriceTypes(res.incomeList,this.inCostList)
      this.outcomeList = this.sortCostPriceTypes(res.outcomeList,this.outCostList)
      this.oilList = res.outOilList
      console.log('income',this.incomeList,this.outcomeList)

    },
    sortCostTypes(types) {
      if (types == null || types.length === 0) {
        return []
      }
      const typeMap = {}
      const root = []
      // biome-ignore lint/complexity/noForEach: <explanation>
      types.forEach(type => {
        // typeMap[type.id] = { ...type, children: [] }
        typeMap[type.id] = type
        typeMap[type.id].children = []
        if (!type.parentId) {
          root.push(typeMap[type.id])
        }
      })
      // biome-ignore lint/complexity/noForEach: <explanation>
      types.forEach(type => {
        if (type.parentId && typeMap[type.parentId]) {
          typeMap[type.parentId].children.push(typeMap[type.id])
        }
      })
      return root
    },
    sortCostPriceTypes(list,costTypeList) {
      if (list == null || list.length === 0) {
        return []
      }
      const listMap = {}
      // biome-ignore lint/complexity/noForEach: <explanation>
      list.forEach(item => {
        if (!listMap[item.ptypeId]) {
          listMap[item.ptypeId] = []
        }
        listMap[item.ptypeId].push(item)
      })
      const sortedTypes = []
      const lts = JSON.parse(JSON.stringify(costTypeList))
      // biome-ignore lint/complexity/noForEach: <explanation>
      lts.forEach(type => {
        const t = type
        let p = 0
        if (listMap[t.id]) {
          p = Number.parseFloat(listMap[t.id].reduce((sum, child) => {
            const price = Number.parseFloat(child.price)
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0) || 0
          t._detail = listMap[t.id]
        }
        const sum = this.addChildrenPriceToSortedList(t, listMap)
        t._sum = sum + p
        sortedTypes.push(t)
      })
      return sortedTypes
    },
    addChildrenPriceToSortedList(item, listMap) {
      if (!item.children || !item.children.length) return 0
      let sum = 0
      // biome-ignore lint/complexity/noForEach: <explanation>
      item.children.forEach(child => {
        const t = child
        let p = 0
        if (listMap[t.id]) {
          p = Number.parseFloat(listMap[t.id].reduce((s, c) => {
            const price = Number.parseFloat(c.price)
            return s + (Number.isFinite(price) ? price : 0)
          }, 0) || 0) || 0
          t._detail = listMap[t.id]
        }
        const csum = this.addChildrenPriceToSortedList(t, listMap)
        t._sum = csum + p
        sum += t._sum
      })
      return sum
    },

    // 计算总支出
    getTotalExpense() {
      if (!this.outcomeList || this.outcomeList.length === 0) {
        return 0
      }

      return this.outcomeList.reduce((total, category) => {
        return total + (category._sum || 0)
      }, 0)
    },
  }
}
</script>
<style scoped>

</style>
