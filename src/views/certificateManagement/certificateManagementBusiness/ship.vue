<template>
  <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
    <el-tab-pane label="挂帐" name="first">
      <ship-index></ship-index>
    </el-tab-pane>
    <el-tab-pane label="月成本" name="second">
      <ship-month></ship-month>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import shipIndex from './ship/shipIndex.vue';
import shipMonth from './ship/shipMonth.vue';
export default{
  name: 'CertificateManagementBusinessShip',
  components: { shipIndex,shipMonth },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
