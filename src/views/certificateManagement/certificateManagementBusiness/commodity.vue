<template>
  <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
    <el-tab-pane label="挂帐" name="first">
      <commodity-index></commodity-index>
    </el-tab-pane>
    <el-tab-pane label="改帐" name="second">
      <commodity-update></commodity-update>
    </el-tab-pane>

  </el-tabs>
</template>
<script>
import commodityIndex from './commodity/commodityIndex.vue';
import commodityUpdate from './commodity/commodityUpdate.vue';
export default{
  name: 'CertificateManagementBusinessCommodity',
  components: { commodityIndex,commodityUpdate },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
