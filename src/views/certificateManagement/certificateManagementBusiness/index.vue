<template>
  <el-tabs v-model="activeName" @tab-click="handleClick"   type="border-card">
     <el-tab-pane label="物流配载" name="first">
      <commodity></commodity>
    </el-tab-pane>
    <el-tab-pane label="船舶配载" name="second">
      <ship></ship>
    </el-tab-pane>
    <el-tab-pane label="库存消耗-油品" name="third">
      <oil></oil>
    </el-tab-pane>

  </el-tabs>
</template>
<script>
import commodity from './commodity.vue';
import oil from './oil.vue';
import ship from './ship.vue';
export default{
  name: 'CertificateManagementBusiness',
  components: { ship,commodity,oil },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>

</style>
