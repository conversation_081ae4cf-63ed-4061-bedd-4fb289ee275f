<template>
  <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
    <el-tab-pane label="物流配载" name="first">
      <statistical></statistical>
    </el-tab-pane>
    <el-tab-pane label="船舶配载" name="third">
      <invoice-sales></invoice-sales>
    </el-tab-pane>
     <el-tab-pane label="库存消耗-油品" name="third">
      <invoice-sales></invoice-sales>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import statistical from './statistical.vue';
import invoiceSales from './invoiceSales.vue';
export default{
  name: 'Commodity',
  components: { statistical,invoiceSales },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
