<template>
  <section>
    <AccModel @updateAccModel="updateAccModel"></AccModel>
    <div class="app-container">
      <div class="workflow_component_body">
        <el-form ref="queryForm" :model="queryParams" :inline="true">
          <el-form-item prop="spTime" label="申请时间">
            <el-date-picker
              v-model="queryParams.spTime"
              @change="handleQuery"
              size="small"
              type="daterange"
              range-separator="至"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item prop="userName" label="申请人">
            <el-input
              v-model="queryParams.userName"
              placeholder="申请人"
              clearable
              @keyup.enter.native="handleQuery"
              size="small"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item prop="money" label="金额">
            <el-input
              v-model="queryParams.money"
              placeholder="金额"
              clearable
              @keyup.enter.native="handleQuery"
              size="small"
              style="width: 240px"
            />
          </el-form-item>

          <el-form-item prop="companyid" label="公司">
            <el-select
              v-model="queryParams.companyid"
              @change="handleQuery"
              placeholder="公司"
              size="small"
              clearable
            >
              <el-option v-for="item in companylist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="false" prop="accid"  label="账户">
            <el-select
              v-model="queryParams.accid"
              @change="handleQuery"
              placeholder="账户"
              size="small"
              clearable
            >
              <el-option
                v-for="item in accountListNew"
                :key="item.id"
                :label="item.bankName||item.accountName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="applyStatus" label="审批状态">
            <el-select
              v-model="queryParams.applyStatus"
              @change="handleQuery"
              size="small"
              clearable
              placeholder="审批状态"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="code" label="申请类型">
            <el-select
              v-model="queryParams.code"
              @change="handleQuery"
              size="small"
              clearable
              placeholder="申请类型"
            >
              <el-option
                v-for="item in codeOptions"
                :key="item.id"
                :label="item.name"
                :value="item.spare1"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button  size="mini" @click="openPz">凭证总帐</el-button>
          </el-form-item>
        </el-form>
       <vxe-toolbar
      export
      custom
      print
      ref="xToolbar"
      :buttons="toolbarButtons"
    >
    </vxe-toolbar>
        <vxe-table
          v-loading="loading"
          class="el-table-info1"
          :data="dataList"
           stripe
          size="small"
          border
          align="center"
          :print-config="{}"
          max-height="800"
          highlight-current-row
          :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '付款待办', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
          resizable
        >
          <!-- <vxe-table-column label="#" align="center" type="index" /> -->
          <vxe-table-column title="公司" align="center" field="cName" width="100" />
          <vxe-table-column title="部门" align="center" field="dName" width="80"  />
          <vxe-table-column title="内外" align="center" field="payAccountTypeName" width="50"  />
          <vxe-table-column title="申请人" align="center" field="sponsorName" width="90" />
          <vxe-table-column title="申请类型" align="center" field="flowName" width="120"  />
          <vxe-table-column title="摘要" align="center" field="summary" :formatter="({row, column, cellValue, index}) => { return (cellValue && cellValue.substr(cellValue.indexOf('事由：')+3))||'--'}"  />
          <vxe-table-column title="金额(元)" align="center" field="param3" width="120" >
            <template slot-scope="scope">
              申请：{{scope.row.param3 }}
              <span v-show="scope.row.paidMoney && scope.row.param3 != scope.row.paidMoney">已付：{{ scope.row.paidMoney }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="收款账户" align="center" field="bankAccountName" width="80" />
          <vxe-table-column title="申请时间" align="center" field="sponsorTime" width="100" />
          <vxe-table-column title="进行状态" align="center" field="showStatus" width="80" />
          <vxe-table-column title="凭证状态" align="center">
            <template slot-scope="scope">
              <!-- <el-button type="text" size="small"  @click="showYacc(scope.row)">生成凭证</el-button> -->
              <el-button type="text" :class="{btnCol:isCertModel=='2'}" size="small" v-show="scope.row.isYfPz && scope.row.yfPzCount==0" @click="showYacc(scope.row)">生成凭证</el-button>
              <el-button type="text" :class="{btnCol:isCertModel=='2'}"  size="small" v-show="scope.row.isYfPz && scope.row.yfPzCount>0" @click="showTran(scope.row,'1')">查看未付凭证</el-button>
               <!-- 已生成业务招待费未付凭证可以生成业务税 -->
               <el-button type="text" :class="{btnCol:isCertModel=='2'}"  size="small" v-show="'business_paying' == scope.row.isYfPz && scope.row.busInputTaxCount == 0" @click="showPzByCode(scope.row,'business_input_tax')">生成进项税凭证</el-button>
               <!-- showTranByCode  busInputTaxCount-->
               <el-button type="text" :class="{btnCol:isCertModel=='2'}"  size="small" v-show="'business_paying' == scope.row.isYfPz && scope.row.busInputTaxCount>0" @click="showTranByCode(scope.row,'business_input_tax')">查看进项税凭证</el-button>
              <el-button type="text" :class="{btnCol:isCertModel=='2'}"  size="small" v-show="scope.row.isFuPz && scope.row.fuPzCount>0" @click="showTran(scope.row,'-1')">查看已付凭证</el-button>
            </template>
          </vxe-table-column>
          <vxe-table-column title="操作" align="center" width="120">
            <template slot-scope="scope">

              <el-button type="text" size="small" @click="handleRowClick(scope.row)">查看详情</el-button>
              <el-button v-if="Math.floor(scope.row.nodeId/10) == 8 && scope.row.param7 !== '1' && scope.row.flowCode !== 'CurrentAccountApplication' && ifrole(chuna)" type="text" size="small" @click="toPayBusinessCost(scope.row)">付款</el-button>
              <el-button v-if="Math.floor(scope.row.nodeId/10) == 8 && scope.row.param7 === '1' && scope.row.flowCode !== 'CurrentAccountApplication' && ifrole(chuna)" type="text" size="small" @click="toPayBusinessCostForfdbiz(scope.row)">付款</el-button>
              <el-button v-if="Math.floor(scope.row.nodeId/10) == 8 && scope.row.flowCode === 'CurrentAccountApplication' && ifrole(chuna)" type="text" size="small" @click="toPayComeToCost(scope.row)">付款</el-button>
              <el-button v-if="scope.row.isFuPz && (scope.row.fuPzCount==0 || queryParams.applyStatus==1)" type="text" :class="{btnCol:isCertModel=='2'}"  size="small" @click="showFuPzDialog(scope.row)">生成付款凭证</el-button>


              <!-- <el-button v-if="scope.row.nodeId == 90 && scope.row.param7 !== '1' && scope.row.flowCode !== 'CurrentAccountApplication' && ifrole(caiwu)" type="text" size="small" @click="finishProcess(scope.row)">生成凭证</el-button>
              <el-button v-if="scope.row.nodeId == 90 && scope.row.param7 === '1' && scope.row.flowCode !== 'CurrentAccountApplication' && ifrole(caiwu)" type="text" size="small" @click="finishProcessfdbiz(scope.row)">生成凭证</el-button>
              <el-button v-if="scope.row.nodeId == 90 && scope.row.flowCode === 'CurrentAccountApplication' && ifrole(caiwu)" type="text" size="small" @click="finishProcessComeTo(scope.row)">生成凭证</el-button> -->

            </template>
          </vxe-table-column>
        </vxe-table>

        <el-pagination
          background
          layout="prev, pager, next,sizes"
          :total="total"
          @size-change="handleSizeChange"
          :page-sizes="[10, 100, 300, 500, 1000]"
          :page-size="queryParams.pageSize"
          :current-page="queryParams.pageNo"
          style="text-align: right;padding: 10px 0px;background-color: #fff"
          @current-change="eventPage"
        />
      </div>
    </div>
    <work-flow-process-drawer ref="processDrawer" @refresh="getList" />
    <el-dialog
    :visible.sync="dialogTarnShow"
    title="查看凭证"
    width="65%"
    :before-close="closeTarnShow"
  >
    <resultTrans v-for="(item,idx) in showTableTrans" :key="idx" :tableData="item" :classList="kjClassList"></resultTrans>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTarnShow = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
    <el-dialog
    :visible.sync="dialogYacc"
    title="生成凭证"
    width="80%"
    :before-close="handleYacc"
  >
    <span>&nbsp;&nbsp;账期:&nbsp;<el-date-picker
      v-model="monthDateAcc"
      type="month"
      @change="changeMonthDateAcc"
      value-format="yyyyMM"
      placeholder="选择日期">
    </el-date-picker>
    </span>
    <Subacc :tableData="tableDataAcc" :accounts="ledgerAccountList" @update:tableData="v=> tableDataAcc = v" @ledgerAccount="v=> ledgerAccount = v" :paymentType="paymentType"  :classList="kjClassList"></Subacc>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogYacc = false">取消</el-button>
        <el-button type="primary" @click="saveYacc()">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog title="生成付款凭证" :visible.sync="dialogFkVisible" width="80%" :before-close="dialogFkBeforeClose">
      <div>
        <div >
          <el-divider content-position="center">生成凭证</el-divider>
          <div style="display: flex;justify-content: right;" v-show="'salary_payend'==voucherCode">
            人员工资：合并<el-switch
            v-model="sarAccSingle"
            @change="changeSarAccSingle"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>单人
          </div>
          <!-- <div style="display: flex;justify-content: right;" v-show="'salary_payend'==voucherCode">
            人员工资：合并<el-switch
            v-model="sarAccSingle"
            @change="changeSarAccSingle"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>单人
          </div> -->
          <Subacc :tableData="tableDataAcc" :paymentType="paymentType" @update:tableData="v=> tableDataAcc = v" :accounts="ledgerAccountList" @ledgerAccount="v=> ledgerAccount = v" :classList="kjClassList"></Subacc>
        </div>
      <div v-show="fuPzCount">
        <div>

          <el-divider content-position="center">已付凭证</el-divider>
        </div>
        <resultTrans v-for="(item,idx) in showTableTrans" :key="idx" :tableData="item" ></resultTrans>
      </div>
      </div>
      <div slot="footer">
          <el-button @click="dialogFkVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveFuPzSub()">确 定</el-button>
      </div>
  </el-dialog>
    <el-dialog
      title="出纳付款"
      :visible.sync="showPay"
      width="60%"
      :before-close="handleClosePay"
      :close-on-click-modal="false"
    >
      <div style="display: flex;">
        <div v-if="contract.accountName !== ' '" style="margin-right: 20px;">收款公司：{{ contract != null ?contract.accountName:"--" }}</div>
        <div style="margin-right: 20px;">付款金额：{{ pay }}元</div>
        <div>付款总金额：{{ paymentTotal }}元</div>
      </div>
      <div style="margin: 10px 0" />
      <div style="display: flex;justify-content: left;align-items: center;">
        <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款类型：</div>
        <div class="width">
          <FcItem :list="fundsClassList" v-model="fundsSelItemId" ></FcItem>
        </div>
      </div>
      <div style="margin: 10px 0" />
      <div style="display: flex;justify-content: left;align-items: center;">
        <div style="width: 100px;font-size: 14px;color: #6A6C70;">支付方式：</div>
        <div class="width">
          <el-tag
            v-for="(item,index) in paymentTypeList"
            :key="index"
            style="margin-right: 10px;cursor: pointer;"
            :type="paymentType==item.code?'success':'info'"
            @click="paymentTypeSelect(item)"
          >{{ item.value }}</el-tag>
        </div>
         <div style="width: 100px;font-size: 14px;color: #6A6C70;">发生时间：</div>
        <el-date-picker
          v-model="occurDate"
          @change="changeOccurDate"
          type="date"
          placeholder="选择日期"
          style="width: 200px;"
          value-format="yyyy-MM-dd"
          />
      </div>
      <div>
        <el-table
          ref="singleTable"
          class="el-table-info"
          :data="accountlist"
          :cell-class-name="setColumnClassName"
          style="width: 100%"
          :key="tableKey"
          @cell-click="showMsg"
          @row-click="checkRow"
        >
          <el-table-column
            key="0"
            width="30"
            fixed
          >
            <template slot-scope="scope"><img :class="!scope.row.checked?'showpic':''" style="width: 14px;vertical-align: middle;" src="../../../assets/images/yixuanze.png"></template>
          </el-table-column>
          <el-table-column fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}" />
          <el-table-column fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" />
          <el-table-column align="center" property="bankName" label="银行名称" />
          <el-table-column align="center" property="accountName" label="账户名称" width="120px" />
          <el-table-column align="center" property="accountNumber" label="账户号" />
          <el-table-column align="center" property="initializationMoney" label="余额" :formatter="NumFmt2" />
          <el-table-column align="center" property="paymentMoney" label="付款金额" />
        </el-table>
      </div>
      <div v-show="paymentType==3 && applyData && applyData.length>0">
        <!-- 承兑列表 -->
        <el-divider content-position="center">选择承兑汇票</el-divider>
        <el-table
            class="el-table-info"
          :data="cdList"
          style="width: 100%"
          @row-click="checkRowCd"
        >
        <el-table-column
            key="0"
            width="30"
            fixed
          >
            <template slot-scope="scope"><img :class="!scope.row.checked?'showpic':''" style="width: 14px;vertical-align: middle;" src="../../../assets/images/yixuanze.png"></template>
          </el-table-column>
          <el-table-column align="center" property="ticketNumber" label="承兑票号" />
          <el-table-column align="center" property="drawerName" label="出票人" />
          <el-table-column align="center" property="balance" label="票面金额" :formatter="NumFmt2" />
          <el-table-column label="操作" width="160px" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="splitTicketMsg(scope.row)">拆分</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div v-show="fuPzShow">
        <el-divider content-position="center">生成凭证</el-divider>
        <div style="display: flex;justify-content: right;" v-show="'salary_payend'==voucherCode">
          人员工资：合并<el-switch
          v-model="sarAccSingle"
          @change="changeSarAccSingle"
          active-color="#13ce66"
          inactive-color="#ff4949">
        </el-switch>单人
        </div>

        <Subacc :tableData="tableDataAcc" :paymentType="paymentType" :accounts="ledgerAccountList" @ledgerAccount="v=> ledgerAccount = v"  :classList="kjClassList"></Subacc>
      </div>
      <div v-show="fuPzCount">
        <div>

          <el-divider content-position="center">已付凭证</el-divider>
        </div>
        <resultTrans v-for="item in showTableTrans" :tableData="item" ></resultTrans>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button v-if="fdbiztype !== '1'" type="primary" @click="savePaymentWater">确 定</el-button>
        <el-button v-if="fdbiztype === '1'" type="primary" @click="savePaymentWaterfdbiz">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="往来款付款完成"
      :visible.sync="showComeToPay"
      width="60%"
      :before-close="handleCloseComeToPay"
      :close-on-click-modal="false"
    >
      <div style="display: flex;justify-content: left;align-items: center;">
        <div style="width: 90px;font-size: 14px;color: #6A6C70;">上传截图：</div>
        <div>
          <el-upload
            ref="upload"
            accept=".jpg,.png,.gif,.jpeg,.bmp"
            :file-list="fileList"
            :action="uploadUrl"
            list-type="picture-card"
            :before-remove="handleRemove"
            :on-error="uploadError"
            :on-success="uploadSuccess"
            :before-upload="beforeAvatarUpload"
            :on-preview="handlePictureCardPreview"
            :class="picList.length>=1?'hiddenClass':''"
          >
            <i class="el-icon-plus" />
          </el-upload>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">提交</el-button>
      </span>
    </el-dialog>

  </section>
</template>

<script>
import 'vxe-table/lib/style.css'
import { getAcctgTransList,getAcctgTransListByCode,getHaokjSub,getIsCodeByPid,getIsCodeByPidAndCode,getPaymentProcessList,getVocherListByCode, getVoucherListPost,saveVoucher} from '@/api/business/processapi'
import { getExternalAccount } from '@/api/business/sysExternalAccount'
import { getAllCompany,getAllCompanyDepart } from '@/api/business/wxDepartmentapi'
import { getShipLineDingjingById } from  "@/api/system/baseInit"
import { UPLOAD_URL } from '@/utils/config'
import currency from 'currency.js'
import dayjs from 'dayjs'
import VXETable from 'vxe-table'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx';
import { getDictionaryList, getPayment, getSysSupplierContactList } from '@/api/system/baseInit'
import { updateWater, updateWaterStatus } from '@/api/system/comeToPaymentWater'
import { userRole } from '@/api/system/jurisdiction'
import { savePaymentWater } from '@/api/system/paymentWater'
import { saveVoucherComeToPayment, saveVoucherPayment } from '@/api/system/processVoucherRecord'
import { getAllSysAccountByDeptId, selectbyDeptId, selectbycode } from '@/api/system/sysAccount'
import { getProcessCodeList } from '@/api/system/sysProcessDetail'
import WorkFlowProcessDrawer from '@/components/workflow/process'
VXETable.use(VXETablePluginExportXLSX)
import { selectAccount } from '@/api/business/accountMoneyForMonthapi'
import { onlyTreeList } from '@/api/system/fundsClassification'
import {getUnusedAcceptancesAllByAccountId,splitTicket,updateTicketById} from '@/api/system/receiveWater'
import AccModel from '@/components/AccModel/index.vue'
import FcItem from '@/views/components/fcitem/fcitem'
import resultTrans from '@/views/system/businessPayment/resultTrans.vue';
import Subacc from '@/views/system/businessPayment/subacc.vue'

const processApi = require('@/api/system/process')
export default {
  name: 'Index',
  components: { WorkFlowProcessDrawer,FcItem,Subacc,resultTrans,AccModel },
  data() {
    return {
      tableKey:0,
      occurDate: dayjs().format('YYYY-MM-DD'),
      toolbarButtons:[],
      loading: false,
      total: 0,
      dataList: [],
      queryParams: {
        pageNo: 1,
        pageSize: 100,
        code: undefined,
        userName: undefined,
        applyStatus: '1',
        spTime: undefined,
        startTime: undefined,
        endTime: undefined,
        accid: undefined,
        money: undefined,
        companyid: null,
        banid: null
      },
      options: [
        {
          value: '1',
          label: '未付款'
        },
        {
          value: '2',
          label: '已付款'
        },
        // {
        //   value: '3',
        //   label: '已录入凭证'
        // },
      ],
      codeOptions: [],
      showPay: false,
      applicationForm: {},
      tableData: [],
      deductList: [],
      pay: 0,
      fdbiztype: undefined,
      contract: {},
      accountlist: [],
      accountListNew: [],
      process: {},
      approveDialogForm: {},
      applyData: [],
      paymentTotal: 0,
      companylist: [],
      companyDepartlist:[],
      chuna: ['chuna', 'waizhangchuna','shandongchuna'],
      caiwu: ['neizhangkuaiji', 'waizhangkuaiji', 'caiwuzhuguan'],
      showComeToPay: false,
      picList: [],
      fileList: [],
      uploadUrl: UPLOAD_URL,
      dialogImageUrl: '',
      ExternalAccountList: [],
      dialogVisible: false,
      picUrl: null,
      paymentTypeList: [],
      paymentType: null,
      isBusiness: false,
      fundsClassList: [],
      fundsSelItemId: [],
      dialogYacc: false,
      tableProcessIdAcc: null,
      tableDataAcc: [],
      showTableTrans: [],
      monthDateAcc: dayjs().format('YYYYMM'),
      sarAccSingle: false,
      dialogTarnShow: false,
      fuPzShow: false,
      fuPzCount: 0,
      kjClassList: [],
      voucherCode: '',
      ctypeCode: '',
      cdList: [],
      selectCdIds:[],
      ledgerAccount:'',
      ledgerAccountList:[],
      dialogFkVisible:false,
      isCertModel:'1',
    }
  },
  computed: {
    monthDateStr() {
      return dayjs(this.monthDateAcc).format('YYYY年MM月')
    },
    totalDebit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.debit||0)), 0);
    },
    totalCredit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.credit||0)), 0);
    },
  },
  created() {
    this.loadFundsClassList()
    this.getList()
    this.getAccount()
    getAllCompany().then(res => {
      this.companylist = res.data
    })
    getAllCompanyDepart().then(res => {
      this.companyDepartlist = res.data
    })
    getProcessCodeList().then(res => {
      this.codeOptions = res.list
    })
    getExternalAccount().then(res => {
      this.ExternalAccountList = res.data
    })
    getDictionaryList('settlement_type').then(res => {
      this.paymentTypeList = res.map['settlement_type']
    })
    this.loadKeMuList()
  },
  mounted() {
    document.addEventListener('paste', this.onPasteUpload)
  },
  beforeDestroy() {
    document.removeEventListener('paste', this.onPasteUpload)
  },
  methods: {
    updateAccModel(type){
      this.isCertModel = type
      this.uplistByListPz()
    },
    dialogFkBeforeClose(){
      this.dialogFkVisible = false
    },
    async successWcCd() {
      // updateTicketById
      // processId=this.process.processId，ticketIds=selectCdIds
      if (3 != this.paymentType ) {
        return
      }
      this.selectCdIds = []
      if (this.cdList && this.cdList.length > 0) {
        this.cdList.forEach(item => {
          if (item.checked) {
            this.selectCdIds.push(item.id)
          }
        })
      }
      if (!this.selectCdIds || this.selectCdIds.length == 0) {
        return
      }
      await updateTicketById(this.selectCdIds.join(','), this.process.processId)

    },
    async loadCdListByAccountId(accountId) {
      // 根据accountId获取cdList
      this.cdList = []
      if(!accountId || 3 != this.paymentType) {
        return
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      // id 为accountId 账户类型是汇票
      const res = await getUnusedAcceptancesAllByAccountId(accountId)
      if(res.data && res.data.length > 0) {
        this.cdList = res.data.map(item => {
          item.checked=false
          return item
        })
      }
      loading.close()
    },
    async splitTicket(ticketId, money) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      await splitTicket(ticketId, money)
      await this.loadCdListByAccountId(this.applyData.map(i=>i.id).join(','))
      loading.close()
    },
    openPz() {
      window.open('https://cloud2.chanjet.com/accounting/ugeeuz0rv1rp/67quvo5kwt/index.html#/pingzhengs')
    },
    loadKeMuList() {
      getHaokjSub().then(res => {
        if (res && res.data) {
          this.kjClassList = res.data || []
          this.upSubIdByFind()
        }
      })
    },
    loadIsCodeByPid(pids,type,func=undefined) {
      getIsCodeByPid(pids,type).then(res => {
        func && func(res)
      })
    },
    loadIsCodeByPidAndCode(pids,code,func=undefined) {
      getIsCodeByPidAndCode(pids,code).then(res => {
        func && func(res)
      })
    },
    closeTarnShow() {
      this.dialogTarnShow = false
    },
    loadTranListByProcessId(pid, type,func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransList(pid, type).then(res => {
        this.showTableTrans = res.result
        func && func()
      }).finally(() => {
        loading.close()
      })
    },
    loadTranListByProcessIdAndCode(pid, code,func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransListByCode(pid, code).then(res => {
        this.showTableTrans = res.result
        func && func()
      }).finally(() => {
        loading.close()
      })
    },
    changeOccurDate() {
      this.monthDateAcc = dayjs(this.occurDate).format('YYYYMM')
    },
    saveYacc() {
      // save
      // this.saveAccTable('1', () => {
      this.saveAccTable(this.ctypeCode, () => {
        this.handleYacc()
      })
    },
    saveAccTable(type = '1', func = undefined) {
      // 验证数据，
      if (this.totalCredit != this.totalDebit) {
        this.$message.error('借方金额与贷方金额不一致')
        return
      }
      // if (type == '-1' && this.totalCredit > this.paymentTotal) {
      //   this.$message.error('借贷金额大于付款金额')
      //   return
      // }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })

      saveVoucher(this.tableProcessIdAcc, type, this.monthDateAcc,this.tableDataAcc,this.sarAccSingle?'1':'0',this.ledgerAccount).then((data) => {
        if (data.result) {
          this.$message.success('保存成功')
          func && func()
        } else {
          this.$message.error(data.msg || '保存失败')
        }
        if (type == 'business_input_tax') {
          this.loadIsCodeByPidAndCode(this.tableProcessIdAcc, 'business_input_tax', (res) => {
            this.upPzCodeToList(res,'isBusInputTax','busInputTaxCount')
          })
        } else {
          this.loadIsCodeByPid(this.tableProcessIdAcc, type, (res) => {
            if (type == '1') {
              this.upPzCodeToList(res,'isYfPz','yfPzCount')
            }
            if (type == '-1') {
              this.upPzCodeToList(res,'isFuPz','fuPzCount')
            }
          })
        }

        loading.close()
      }).catch((err) => {
        this.$message.error('保存失败')
        loading.close()
      })
    },
    handleYacc() {
      this.dialogYacc=false
    },
    showYacc(data) {
      this.ctypeCode = '1'
      this.loadAccTable(data.processId, '1', () => {
        this.dialogYacc = true
        // 刷新
      })
    },
    showPzByCode(data, code) {
      this.ctypeCode = code
      this.loadPzTable(data.processId, code, () => {
        this.dialogYacc = true
      })
    },
    showTran(data, type) {
      this.loadTranListByProcessId(data.processId, type, () => {
        this.dialogTarnShow = true
      })
    },
    showTranByCode(data,code) {
      this.loadTranListByProcessIdAndCode(data.processId, code, () => {
        this.dialogTarnShow = true
      })
    },
    changeMonthDateAcc() {
      // this.tableDataAcc = this.tableDataAcc.map(item => {
      //   return {
      //     ...item,
      //     // summary: '计提'+this.monthDateStr+'工资及社保公积金',
      //   }
      // })
    },
    changeSarAccSingle() {
      this.loadAccTable(this.tableProcessIdAcc, '-1')
    },
    loadPzTable(pid, code, func = undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.tableDataAcc = []
      this.tableProcessIdAcc = pid
      this.voucherCode = ''
      getVocherListByCode(pid, code).then((res) => {
          this.tableDataAccCode(res)
          func && func()
          loading.close()
      }).catch((err) => {
          this.$message.error(err.message||'暂不支持此类型')
          loading.close()
      })
    },
    tableDataAccCode(res) {
        const list = res && res.data ? res.data : []
        this.voucherCode = res && res.code
        // findSub
        this.tableDataAcc = list || []
        this.ledgerAccountList = res && res.accounts
        // this.tableDataAcc = list.map(item => {
        //   return {
        //     // summary: '计提'+this.monthDateStr+'工资及社保公积金',
        //     ...item
        //   }
        // })
        // this.dialogYacc = true
        this.upSubIdByFind()
    },
    loadAccTable(pid, type='1',func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.tableDataAcc = []
      this.tableProcessIdAcc = pid
      this.voucherCode = ''
      // if (type == '-1' && !this.paymentTotal) {
      //   loading.close()
      //   return
      // }
      getVoucherListPost(pid, type,this.sarAccSingle?'1':'',this.paymentTotal,this.applyData).then((res) => {
        // console.log('res', res)
        // let list = []
        // list = res && res?.data ? res.data : []
        this.tableDataAccCode(res)
        func && func()
        loading.close()
      }).catch((err) => {
        this.$message.error(err.message||'暂不支持此类型')
        loading.close()
      })
    },
    loadFundsClassList() {
      if(this.fundsClassList.length == 0) {
        onlyTreeList('out').then(res => {
          if (res.resultCode == '0' && res.data) {
            this.fundsClassList = res.data
            // console.log('fundsClassList', this.fundsClassList)

          }
        })
      }
    },
    upSubIdByFind() {
      if (this.tableDataAcc && this.tableDataAcc.length > 0 && this.kjClassList && this.kjClassList.length > 0) {
        // tableDataAcc 中 findSub 获取 kjClassList glAccountName 相等 ，treePath(2241^********^) = subject (2241)^
        //tableDataAcc -> findSub,subject
        // kjClassList -> glAccountName,treePath, glAccountCode
         // 承兑 最后一个科目改成1121
         if (3 == this.paymentType) {
          this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1121'
          this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1121'
         }
          if (1 == this.paymentType) {
            this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1001'
            this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1001'
          }

        for (let i = 0; i < this.tableDataAcc.length; i++) {
          if (this.tableDataAcc[i].findSub && !this.tableDataAcc[i].findSubId) {
            try {
              const fundsCode = this.kjClassList.find(item => item.treePath.startsWith(this.tableDataAcc[i].subject + '^')  && item.glAccountName == this.tableDataAcc[i].findSub)
              if (fundsCode) {
                this.tableDataAcc[i].findSubId = fundsCode.glAccountCode
                this.tableDataAcc[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }



      }
    },
    getAccount() {
      console.log(this.queryParams)
      // if (!this.queryForm.companyid){
      //   this.queryForm.accid = undefined
      // }
      selectAccount(this.queryParams.banid, this.queryParams.companyid).then(res => {
        this.accountListNew = res.list
      })
    },
    paymentTypeSelect(item) {
      const id = item.code
      if (this.paymentType == id) {
        this.paymentType = null
      } else {
        this.paymentType = id
      }
      this.accountlist = []
      var type = 0
      if (this.paymentType == 3) {
        type = 1
      } else if (this.paymentType == 1) {
        type = 2
      }

      // type = 1 承兑

      // if (this.isBusiness) {
      //   selectbycode(this.applicationForm.invoiceCompany, type).then(res => {
      //     if (res != undefined) {
      //       for (var i = 0; i < res.list.length; i++) {
      //         var tmp = res.list[i]
      //         tmp['checked'] = false
      //         tmp['paymentMoney'] = 0
      //       }
      //       this.accountlist = res.list
      //     }
      //   })
      // } else {
        var deptid = this.CompanyCodeFmt3(this.process.param4)
        selectbyDeptId(deptid, this.paymentType == null ? -1 : type).then(res => {
          if (res) {
            for (var i = 0; i < res.list.length; i++) {
              var tmp = res.list[i]
              tmp['checked'] = false
              tmp['paymentMoney'] = 0
            }
            this.accountlist = res.list
          }
        })
      // }
    },
    showFuPzDialog(obj){
        this.ctypeCode = '-1'
        this.loadAccTable(obj.processId, this.ctypeCode)
        this.loadTranListByProcessId(obj.processId, this.ctypeCode)
        this.dialogFkVisible = true
    },
    saveFuPzSub(){

      if (  this.totalCredit != this.totalDebit) {
        this.$message.error('借方金额与贷方金额不一致')
        return
      }
      // if (  this.totalCredit > this.paymentTotal) {
      //   this.$message.error('借贷金额大于付款金额')
      //   return
      // }
        this.saveAccTable('-1', () => {
          this.dialogFkVisible = false
        })
    },
    submit() {
      const loading = this.$loading({
        lock: true,
        text: '数据提交中，请稍后！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      var picList = this.picList.toString()
      updateWater(this.process.processId, picList).then(res => {
        if (res && res.resultCode === '0') {
          this.approveDialogForm.processId = this.process.processId
          this.approveDialogForm.type = 2
          this.approveDialogForm.nodeId = this.process.nodeId
          this.approveDialogForm.comment = '付款已完成'
          processApi.handleProcess(this.approveDialogForm).then(ress => {
            this.handleCloseComeToPay()
            loading.close()
            this.$message({
              type: 'success',
              message: '付款成功'
            })
          })
        }
      })
    },
    onPasteUpload(e) {
      console.log(e)
      const upload = this.$refs.upload
      if (!upload) {
        return
      }
      const items = e.clipboardData.items
      for (const item of items) {
        if (item.type === 'image/png') {
          const file = new File([item.getAsFile()], new Date().getTime() + '.png')
          upload.handleStart(file)
        }
      }
      upload.submit()
    },
    toremove(file, fileList) {
      if (file.response != undefined && file.response != null && file.response != '') {
        console.info(file, fileList)
        var list = Object.assign([], this.picList)
        this.picList = []
        for (var i = 0; i < list.length; i++) {
          if (list[i] != file.response.url) {
            this.picList.push(list[i])
          }
        }
      } else {
        console.info(file, fileList)
        var list = Object.assign([], this.picList)
        this.picList = []
        for (var i = 0; i < list.length; i++) {
          if (list[i] != file.url) {
            this.picList.push(list[i])
          }
        }
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    toDx(n) {
      switch (n) {
        case 'TravelReimbursement':
          return '差旅报销'
        case 'Reimbursement':
          return '费用报销'
        case 'CurrentAccountApplication':
          return '往来款'
        case 'LoanApplication':
          return '借款'
        case 'ExternalPaymentApplication':
          return '对外付款'
        case 'ExternalPaymentFinanceApplication':
          return '财务对外付款'
        case 'paymentApprove':
          return '业务费用付款'
        case 'shipDingjinPaymentApprove':
          return '钢材船舶定金付款'
        case 'OperatingPaymentApplication':
          return '经营付款'
        // case '7':
        //   return '柒'
        // case '8':
        //   return '捌'
        // case '9':
        //   return '玖'
      }
    },
    // 上传前对文件的大小的判断
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.warning('上传图片大小不能超过 5 MB!')
      }
      return isLt5M
    },
    // 上传成功后的回调
    uploadSuccess(response, file, fileList) {
      this.picList.push(response.url)
      console.info(this.picList.toString())
    },
    uploadError(response, file, fileList) {
      this.$message.error(`上传失败，请重试！`)
    },
    handleRemove(file, fileList) {
      return this.$confirm('此操作将删除该截图, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.toremove(file, fileList)
        return true
      })
    },
    handleCloseComeToPay() {
      this.showComeToPay = false
      this.getList()
    },
    ifrole(list) {
      return userRole(list)
    },
    splitTicketMsg(row) {
      this.$prompt('请输入拆分金额', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.NumFmt2(null, null, row.paymentMoney, null)
      }).then(({ value }) => {
        if (isNaN(value)) {
          this.$message({
            showClose: true,
            type: 'error',
            message: '只允许输入数字'
          })
        } else {
          this.splitTicket(row.id, value).then(() => {
            this.$message({
              showClose: true,
              type: 'success',
              message: '拆分成功'
            })
          })
        }
       })
    },
    showMsg(row, column, cell, event) {
      if (column.label == '付款金额') {
        this.$prompt('请输入付款金额', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: this.NumFmt2(null, null, row.paymentMoney, null)
        }).then(({ value }) => {
          if (isNaN(value)) {
            this.$message({
              showClose: true,
              type: 'error',
              message: '只允许输入数字'
            })
          } else {
            row.paymentMoney = 0
            var paymentTotal = 0
            for (var i = 0; i < this.accountlist.length; i++) {
              if (this.accountlist[i].checked) {
                console.log('1101',this.accountlist[i])
                //  paymentTotal += Number(this.accountlist[i].paymentMoney).toFixed(2)
                paymentTotal = currency(this.accountlist[i].paymentMoney).add(paymentTotal).value
              }
            }
            // paymentTotal = parseFloat(Math.abs(paymentTotal).toFixed(2))
            paymentTotal = currency(Math.abs(paymentTotal)).value
            var money = currency(this.pay).subtract(paymentTotal).value // this.pay - paymentTotal
            if (value > money) {
              this.$message({
                showClose: true,
                type: 'error',
                message: '支付金额超出应付金额'
              })
              row.paymentMoney = money
              return
            }
            row.paymentMoney = Number(value)
            if (value > 0) {
              row.checked = true
            } else {
              row.checked = false
            }
            this.applyData = []
            this.paymentTotal = 0
            const appylDataTmp = []
            let paymentTotalTmp = 0
            for (let i = 0; i < this.accountlist.length; i++) {
              if (this.accountlist[i].checked) {
                appylDataTmp.push(this.accountlist[i])
                // this.applyData.push(this.accountlist[i])
                // paymentTotalTmp += Number(this.accountlist[i].paymentMoney).toFixed(2)
                paymentTotalTmp = currency(this.accountlist[i].paymentMoney).add(paymentTotalTmp).value
              }
            }
            this.applyData = appylDataTmp
            // this.paymentTotal = parseFloat(Math.abs(paymentTotalTmp).toFixed(2))
            this.paymentTotal = currency(Math.abs(paymentTotalTmp)).value;
            // this.fuPzShow && this.loadAccTable(this.tableProcessIdAcc, '-1')
            this.loadCdListByAccountId(this.applyData.map(item=>item.id).join(','))
          }
        }).catch(() => {
          this.$message({
            showClose: true,
            type: 'info',
            message: '取消输入'
          })
        })
      }
    },
    setColumnClassName({ row, column, rowIndex, columnIndex }) {
      if (column.label == '付款金额' && row.checked) {
        return 'colum-class'
      }
      return ''
    },
    accSub(arg1, arg2) {
      var r1, r2, m, n;
      try { r1 = arg1.toString().split(".")[1].length } catch (e) { r1 = 0 }
      try { r2 = arg2.toString().split(".")[1].length } catch (e) { r2 = 0 }
      m = Math.pow(10, Math.max(r1, r2));
      n = (r1 >= r2) ? r1 : r2;
      return ((arg1 * m - arg2 * m) / m).toFixed(n);
    },
    checkRowCd(row, column, event) {
      if (row.checked == false) {
        // 判断金额是否足够
        const m = this.paymentCdTotal || 0
        if (currency(row.balance).add(m).value > this.paymentTotal) {
          this.$message.error('选择金额超过支付金额')
          return
        }

        row.checked = true
      } else {
        row.checked = false
      }
      let paymentTotalTmp = 0
      for (let i = 0; i < this.cdList.length; i++) {
        if (this.cdList[i].checked) {
          paymentTotalTmp = currency(this.cdList[i].balance).add(paymentTotalTmp).value
        }
      }
      this.paymentCdTotal = paymentTotalTmp
    },
    checkRow(row, column, event) {
      // 选择账户后  - 加载承兑汇票
      if (column.label !== '付款金额') {
        if (row.checked == false) {
          var money = this.accSub(this.pay,this.paymentTotal)
          if (money <= 0) {
            this.$message({
              showClose: true,
              type: 'error',
              message: '剩余应付款金额为0，无法继续付款'
            })
            return
          }
          row.checked = true
          if (row.initializationMoney >= money) {
            row.paymentMoney = money
          } else {
            console.log(row);
            if (/^虚拟账户\s*-/g.test(row.accountName)) {
              row.paymentMoney = money
            } else {
              row.paymentMoney = 0
            }
          }
        } else {
          row.checked = false
          row.paymentMoney = 0
        }
        this.applyData = []
        this.paymentTotal = 0
        const appylDataTmp = []
        let paymentTotalTmp = 0
        for (let i = 0; i < this.accountlist.length; i++) {
          if (this.accountlist[i].checked) {
            // console.log('1220',this.accountlist[i])
            // this.applyData.push(this.accountlist[i])
            appylDataTmp.push(this.accountlist[i])
            // this.paymentTotal += parseFloat(Number(this.accountlist[i].paymentMoney).toFixed(2))
            // this.paymentTotal = currency(this.accountlist[i].paymentMoney).add(this.paymentTotal).value
            paymentTotalTmp = currency(this.accountlist[i].paymentMoney).add(paymentTotalTmp).value
          }
        }
        this.applyData = appylDataTmp
        // console.log(this.paymentTotal);
        // this.paymentTotal = parseFloat(Math.abs(this.paymentTotal).toFixed(2))
        this.paymentTotal = currency(Math.abs(paymentTotalTmp)).value
        // this.fuPzShow && this.loadAccTable(this.tableProcessIdAcc, '-1')
        this.loadCdListByAccountId(this.applyData.map(item=>item.id).join(','))
      }
    },
    finishProcess(obj) {
      this.$prompt('请输入凭证号', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        console.info(value)
        this.approveDialogForm.processId = obj.processId
        this.approveDialogForm.type = 2
        this.approveDialogForm.nodeId = obj.nodeId
        this.approveDialogForm.comment = '凭证录入完成，凭证号：' + value
        saveVoucherPayment(value, obj.processId).then(res => {
          console.info(res)
          if (res) {
            processApi.handleProcess(this.approveDialogForm).then(ress => {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.getList()
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    finishProcessfdbiz(obj) {
      this.$prompt('请输入凭证号', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        console.info(value)
        this.approveDialogForm.processId = obj.processId
        this.approveDialogForm.type = 2
        this.approveDialogForm.nodeId = obj.nodeId
        this.approveDialogForm.comment = '凭证录入完成，凭证号：' + value

        processApi.handleProcess(this.approveDialogForm).then(ress => {
          this.$message({
            type: 'success',
            message: '操作成功！'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    savePaymentWaterfdbiz() {

      // this.waterObj.sysAccountId = this.currentRowId
      // this.waterObj.type = 0
      // this.waterObj.money = this.pay
      // this.waterObj.processId = this.process.processId
      // this.waterObj.receiveCompany = this.contract.accountName
      // paymentWaterApi.add(this.waterObj).then(res=>{
      //   if(res){
      //     this.submitProcess()
      //     this.handleClosePay()
      //     this.$message({
      //       type: 'success',
      //       message: '付款已完成'
      //     });
      //   }
      // })
      if (!this.fundsSelItemId || this.fundsSelItemId.length == 0) {
        this.$message({
          type: 'error',
          message: '请选择付款类型'
        })
        return
      }
      if (!this.paymentTotal) {
        this.$message({
          type: 'error',
          message: '付款金额不能为0'
        })
        return
      }
      // if (this.paymentTotal !== this.pay) {
      //   this.$message({
      //     type: 'error',
      //     message: '付款金额不一致'
      //   })
      //   return
      // }

      const fundClassId = this.fundsSelItemId && this.fundsSelItemId.length > 0 ? this.fundsSelItemId[this.fundsSelItemId.length - 1] : ''
      var data = JSON.stringify(this.applyData)
      var costType = this.toDx(this.process.flowCode)
      const loading = this.$loading({
        lock: true,
        text: '数据提交中，请稍后！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      savePaymentWater(data, this.process.processId, this.contract.accountName, costType, this.paymentType, this.occurDate, fundClassId).then(res => {
        if (res) {
          if (res.data) {
            this.approveDialogForm.processId = this.process.processId
            this.approveDialogForm.type = 2
            this.approveDialogForm.nodeId = this.process.nodeId
            this.approveDialogForm.comment = '付款已完成'
            processApi.handleProcess(this.approveDialogForm).then(ress => {
              loading.close()
              this.handleClosePay()
              this.$message({
                type: 'success',
                message: '付款成功'
              })
            })
          } else {
            loading.close()
            this.handleClosePay()
            this.$message({
              type: 'success',
              message: '付款成功'
            })
          }
          this.successWcCd()
        }
      })
    },
    savePaymentWater() {

      // this.waterObj.sysAccountId = this.currentRowId
      // this.waterObj.type = 0
      // this.waterObj.money = this.pay
      // this.waterObj.processId = this.process.processId
      // this.waterObj.receiveCompany = this.contract.accountName
      // paymentWaterApi.add(this.waterObj).then(res=>{
      //   if(res){
      //     this.submitProcess()
      //     this.handleClosePay()
      //     this.$message({
      //       type: 'success',
      //       message: '付款已完成'
      //     });
      //   }
      // })
      if (!this.fundsSelItemId || this.fundsSelItemId.length == 0) {
        this.$message({
          type: 'error',
          message: '请选择付款类型'
        })
        return
      }
      if(!this.paymentTotal){
        this.$message({
          type: 'error',
          message: '付款金额不能为0'
        })
        return
      }

      // if (this.paymentTotal !== this.pay) {
      //   this.$message({
      //     type: 'error',
      //     message: '付款金额不一致'
      //   })
      //   return
      // }

      var data = JSON.stringify(this.applyData)
      const loading = this.$loading({
        lock: true,
        text: '数据提交中，请稍后！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const fundClassId = this.fundsSelItemId && this.fundsSelItemId.length > 0 ? this.fundsSelItemId[this.fundsSelItemId.length - 1] : ''
      var costType = this.toDx(this.process.flowCode)
      savePaymentWater(data, this.process.processId, this.contract.accountName, costType, this.paymentType, this.occurDate, fundClassId).then(res => {
        if (res) {
          if (res.data) {
            this.approveDialogForm.processId = this.process.processId
            this.approveDialogForm.type = 2
            this.approveDialogForm.nodeId = this.process.nodeId
            this.approveDialogForm.comment = '付款已完成'
            processApi.handleProcess(this.approveDialogForm).then(ress => {
              loading.close()
              this.handleClosePay()
              this.$message({
                type: 'success',
                message: '付款成功'
              })
            })
          } else {
            loading.close()
            this.handleClosePay()
            this.$message({
              type: 'success',
              message: '付款成功'
            })
          }
          this.successWcCd()

        }
      })

    },
    selecRreceiveCompany(code) {
      var type = 0
      if (this.paymentType == 3) {
        type = 1
      } else if (this.paymentType == 1) {
        type = 2
      }
      selectbycode(code, type).then(res => {
        if (res != undefined) {
          for (var i = 0; i < res.list.length; i++) {
            var tmp = res.list[i]
            tmp['checked'] = false
            tmp['paymentMoney'] = 0
          }
          this.accountlist = res.list
        }
      })
    },
    selecRreceiveCompanyByDeptId(id) {
      getAllSysAccountByDeptId(id).then(res => {
        if (res != undefined) {
          for (var i = 0; i < res.list.length; i++) {
            var tmp = res.list[i]
            tmp['checked'] = false
            tmp['paymentMoney'] = 0
          }
          this.accountlist = res.list
        }
      })
    },
    // selectAccountById(){
    //   this.currentRowId = this.datas.formValue.accountId
    //   selectbyId(this.datas.formValue.accountId).then(res=>{
    //     if (res!=undefined) {
    //       this.choseAccountlist = res.list
    //     }
    //   })
    // },
    getSupplierContact() {
      getSysSupplierContactList(this.applicationForm.sysSupplierId).then(res => {
        if (res != undefined) {
          if (res.list != null && res.list.length > 0) {
            res.list.forEach(item => {
              if (item.id === this.applicationForm.contractId) {
                this.contract = item
              }
            })
          }
        }
      })
    },
    toPayComeToCost(obj) {
      console.info(obj)
      this.fuPzShow = obj.isFuPz ? true : false
      this.fuPzCount = obj.fuPzCount || 0
      this.paymentTotal = obj.param3
      this.showComeToPay = true
      this.process = {}
      this.process = obj
      // if (this.fuPzShow) {
      //   this.ctypeCode = '-1'
      //   this.loadAccTable(obj.processId, '-1')
      //   this.loadTranListByProcessId(obj.processId, '-1')
      // }
    },
    finishProcessComeTo(obj) {
      console.info(obj)
      this.$prompt('请输入凭证号', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        console.info(value)
        this.approveDialogForm.processId = obj.processId
        this.approveDialogForm.type = 2
        this.approveDialogForm.nodeId = obj.nodeId
        this.approveDialogForm.comment = '凭证录入完成，凭证号：' + value
        processApi.handleProcess(this.approveDialogForm).then(res => {
          var save = saveVoucherComeToPayment(value, obj.processId)
          var update = updateWaterStatus(obj.processId)
          Promise.all([save, update]).then(ress => {
            const ress1 = ress[0]
            const ress2 = ress[1]
            if (ress1 && ress2) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.getList()
            }
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    toPayBusinessCostForfdbiz(obj) {
      const paidMoney = obj.paidMoney || 0
      this.fuPzShow = obj.isFuPz ? true : false
      this.fuPzCount = obj.fuPzCount || 0
      this.tableKey = Math.random()
      this.process = {}
      this.accountlist = []
      this.fundsSelItemId = []
      this.occurDate = dayjs().format('YYYY-MM-DD')
      this.process = obj
      // this.pay = Number(obj.param3) // 增加已支付金额处理
      this.pay = currency(obj.param3).subtract(paidMoney).value
      this.paymentTotal = 0
      this.contract.id = obj.param4
      this.fdbiztype = obj.param7
      this.paymentType = null
      this.contract.accountName = this.ExternalAccountNameFmt(obj.param9)
      // this.selecRreceiveCompanyByDeptId(this.CompanyCodeFmt2(obj.param4))
      this.showPay = true
      this.isBusiness = false
      this.changeOccurDate()
      // if (this.fuPzShow) {
      //   this.ctypeCode = '-1'
      //   this.loadAccTable(obj.processId, '-1')
      //   this.loadTranListByProcessId(obj.processId, '-1')
      // }

    },
    toPayBusinessCost(obj) {
      console.log('toPayBusinessCost1592', obj)
      const paidMoney = obj.paidMoney || 0
      this.fuPzShow = obj.isFuPz ? true : false
      this.fuPzCount = obj.fuPzCount || 0
      this.tableKey = Math.random()
      this.process = {}
      this.accountlist = []
      this.fundsSelItemId = []
      this.occurDate = dayjs().format('YYYY-MM-DD')
      this.pay = 0
      this.process = obj
      this.paymentType = null
      this.paymentTotal = 0
      this.isBusiness = true
      if (obj.flowCode == 'shipDingjinPaymentApprove') {
        // this.pay = parseFloat(obj.param3)
        this.pay = currency(obj.param3).subtract(paidMoney).value

        getShipLineDingjingById(obj.param1,undefined,obj.param4).then(res =>{
          if (res) {
            this.applicationForm = {}
            this.tableData = res.list
            if (this.tableData.length <= 0) {
              return
            }
            const shipLine = this.tableData[0]
            this.applicationForm.invoiceCompany = shipLine.contractCode


            this.showPay = true
          }


        })

      } else if (obj.flowCode == 'paymentApprove') {
        getPayment(obj.param1, 0,obj.param10,obj.param4).then(res => {
          if (res != undefined) {
            this.applicationForm = res.obj ? res.obj : {}
            // this.selecRreceiveCompany(this.applicationForm.invoiceCompany)
            // this.selectAccountById()
            this.applicationForm.price = 0
            this.applicationForm.deductPrice = 0
            this.applicationForm.dingjin = 0
            this.applicationForm.totalExterAmount = 0
            this.tableData = res.list || []
            this.deductList = res.deduct || []
            for (var i = 0; i < this.tableData.length; i++) {
              var tmp = this.tableData[i]
              tmp.charge = tmp.amountDue - tmp.settleAmount
              if (tmp.dingjin) {
                this.applicationForm.dingjin += tmp.dingjin
              } else {
                this.applicationForm.dingjin = 0
              }
            }
            for (var i = 0; i < this.deductList.length; i++) {
              var tmp = this.deductList[i]
              // this.applicationForm.deductPrice += Number(tmp.surplus)
              this.applicationForm.deductPrice += Number(tmp.totalPrice)
            }
            // this.pay = Number(this.applicationForm.payment)
            this.pay = currency(this.applicationForm.payment).add(this.applicationForm.additionalPrice||0).value
            if (this.applicationForm.routeName !== 'applypayticketfandian') {
              // this.pay -= Number(this.applicationForm.deductPrice) - Number(this.applicationForm.price) - Number(this.applicationForm.dingjin) + Number(this.applicationForm.additionalPrice ? this.applicationForm.additionalPrice : 0)
              const fixValue = currency(this.applicationForm.deductPrice)
                .add(this.applicationForm.price)
                .add(this.applicationForm.dingjin)
                .value
              this.pay = currency(this.pay).subtract(fixValue).value
            }
            this.getSupplierContact()
          }
          this.pay = currency(this.pay).subtract(paidMoney).value
          this.showPay = true
        })
      } else {
        this.$message({
          type: 'error',
          message: '缺少流程配置'
        })
      }
      this.changeOccurDate()
      // if (this.fuPzShow) {
      //   this.ctypeCode = '-1'
      //   this.loadAccTable(obj.processId,'-1')
      //   this.loadTranListByProcessId(obj.processId, '-1')
      // }
    },
    handleClosePay() {
      this.showPay = false
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
      // this.getAccount()
    },
    /** 查询用户列表 */
    getList() {
      console.info(this.queryParams)
      if (
        this.queryParams.spTime !== undefined &&
        this.queryParams.spTime !== null
      ) {
        this.queryParams.startTime = this.queryParams.spTime[0]
        this.queryParams.endTime = this.queryParams.spTime[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
      this.loading = true
      getPaymentProcessList(this.queryParams).then(response => {
        const data = response.data
        this.dataList = data.records && data.records.map(item => {
          item.isYfPz = ''
          item.yfPzCount = 0
          item.isFuPz = ''
          item.fuPzCount = 0
          return item
        })
        this.total = data.total
        this.uplistByListPz()
      }).finally(() => {
        this.loading = false
      })
    },
    uplistByListPz(){
      // pids this.dataList
      const pids = this.dataList && this.dataList.map(item => item.processId).join(',')
      if (!pids) {
        return
      }
      this.loadIsCodeByPid(pids, '1', (res) => {
        // isYuPz
        this.upPzCodeToList(res,'isYfPz','yfPzCount')
      })
      this.loadIsCodeByPid(pids, '-1', (res) => {
        // isfuPz
        this.upPzCodeToList(res,'isFuPz','fuPzCount')
      })
      this.loadIsCodeByPidAndCode(pids, 'business_input_tax', (res) => {
          this.upPzCodeToList(res,'isBusInputTax','busInputTaxCount')
      })
    },
    upPzCodeToList(res,key,countKey) {
      if (!res) {
          return
      }
      for (let i = 0; i < this.dataList.length; i++) {
          const item = this.dataList[i]
          if (res[item.processId]) {
              item[key] = res[item.processId].code
              item[countKey] = res[item.processId].count
          }
      }
    },
    handleRowClick(row) {
      this.$refs.processDrawer.drawer = true
      this.$refs.processDrawer.processId = row.processId
      this.$refs.processDrawer.title = row.sponsorName + '提交的审批' + ' (' + row.showStatus + ')'
      this.$refs.processDrawer.doInit('show')
    },
    eventPage(e) {
      this.queryParams.pageNo = e
      this.getList()
    },
    handleSizeChange(v) {
      this.queryParams.pageSize = v;
      this.queryParams.pageNo = 1;
      this.getList();
    },
    NumFmt2(row, column, cellValue, index) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = Number.parseFloat(Math.abs(cellValue).toFixed(2))
      }
      return v
    },
    CompanyNameFmt2(id) {
      for (var a = 0; a < this.companylist.length; a++) {
        var item = this.companylist[a]
        if (item.id === id) {
          return item.label
        }
      }
      return ' '
    },
    ExternalAccountNameFmt(id) {
      for (var a = 0; a < this.ExternalAccountList.length; a++) {
        var item = this.ExternalAccountList[a]
        if (item.id === Number(id)) {
          return item.companyName
        }
      }
      return ' '
    },
    CompanyCodeFmt2(id) {
      console.log(id)
      for (var a = 0; a < this.companylist.length; a++) {
        var item = this.companylist[a]
        if (item.id === id) {
          console.log(1111111)
          return item.deptId
        }
      }
      return undefined
    },
    CompanyCodeFmt3(id) {
      console.log(id)
      for (var a = 0; a < this.companyDepartlist.length; a++) {
        var item = this.companyDepartlist[a]
        if (item.id === id) {
          return item.deptId
        }
      }
      return undefined
    }
  }
}
</script>

<style scoped>
  .workflow_component_body{
    text-align: left !important;
  }
  .el-table-info1 >>> .cell{
    text-align: center;
  }
  .xuanxiang >>> .el-dialog__body{
    padding: 10px 20px;
  }
  .sp_add_left{
    width: 45%;
    margin-right: 5%;
  }
  .sp_add_right{
    width: 45%;
  }
  .showpic{
    display: none;
  }
  .el-table-info >>> .colum-class {
    color: #2cb7fd;
  }
  .el-table-info >>> .colum-class-false {
    color: #C0C4CC;
  }
  .el-table-info >>> .cell{
    text-align: center;
  }
  .btnCol{
    color: #13ce66;
  }
  .btnCol:hover{
    color:#10b85a;
  }
</style>
