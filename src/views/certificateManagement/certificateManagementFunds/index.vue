<template>
  <el-tabs v-model="activeName" @tab-click="handleClick"  type="border-card">
    <el-tab-pane label="收款" name="first"><business-receive></business-receive></el-tab-pane>
    <el-tab-pane label="付款" name="second"><business-payment></business-payment></el-tab-pane>
    <el-tab-pane label="差异调整(付款)" name="third"><other-payment></other-payment></el-tab-pane>

  </el-tabs>
</template>
<script>
import businessPayment from './businessPayment.vue'
import businessReceive from './businessReceive.vue';
import otherPayment from './otherPayment.vue';
export default{
  name: 'CertificateManagementFunds',
  components: { businessPayment,businessReceive,otherPayment },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
