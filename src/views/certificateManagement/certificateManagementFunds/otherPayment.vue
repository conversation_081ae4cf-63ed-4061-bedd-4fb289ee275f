<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="付款类型" style=" align-items:center;">
        <!-- <SimplePanelSelection
          class="width"
          @setVal="costTypeQuery"
          :allData="typeList"
          modeName="收款类型"
          keyStr="code"
          valStr="value"
          ref="costType"
          componentsWidth="600"
        ></SimplePanelSelection> -->
        <div  style="position: relative;bottom: 6px;"><FcItem :list="fundsClassList" @clkItem="clkQuery" v-model="fundsQueryId" ></FcItem></div>
      </el-form-item>
      <el-form-item label="支付方式">
        <div class="width">
          <el-tag style="margin-right: 10px;cursor: pointer;" :type="queryParams.paymentType==item.code?'success':'info'"
                  v-for="(item,index) in  paymentTypeList" :key="index" @click="paymentypeQuery(item)"
          >{{item.value}}</el-tag>
        </div>
      </el-form-item>
      <el-form-item label="付款单位">
        <el-input
          v-model="queryParams.paymentCompany"
          placeholder="付款单位"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="收款客户">
        <el-input
          v-model="queryParams.receiveCompany"
          placeholder="收款客户"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="topage">搜索</el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="addReceiveWater" v-if="ifrole(chuna)">新增</el-button>
      </el-form-item>
    </el-form>
     <vxe-toolbar
      export
      custom
      print
      ref="xToolbar"
      :buttons="toolbarButtons"
    >
    </vxe-toolbar>
    <vxe-table
     ref="xTable"
      class="el-table-info"
      v-loading="loading"
      :data="dataList"
        stripe
      size="small"
      border
      align="center"
      :print-config="{}"
      max-height="800"
      highlight-current-row
      :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '差异调整（付款）', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
      resizable
    >
      <!-- <vxe-table-column label="#" align="center" type="index"/> -->
      <vxe-table-column title="付款时间" align="center" field="occurDate" :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"/>
        <!-- :formatter="({row, column, cellValue, index}) => { return costTypeFmt(row, column, cellValue, index)}" -->
      <vxe-table-column title="付款类型" align="center" field="costType" >
          <template slot-scope="scope">
            <span v-if="scope.row.fundsClassName">{{ scope.row.fundsClassName }}</span>
            <span v-else>
              {{ costTypeFmt(null,null,scope.row.costType,null) }}
            </span>
          </template>
      </vxe-table-column>
      <vxe-table-column title="支付方式" align="center" field="paymentType" :formatter="({row, column, cellValue, index}) => { return receviceTypeFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="付款金额" align="center" field="money" :formatter="({row, column, cellValue, index}) => { return NumFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="付款单位" align="center" field="paymentCompanyName" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="收款客户" align="center" field="receiveCompany" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="收款银行" align="center" field="receiveBank" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="收款账户" align="center" field="receiveAccount" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="流程状态" align="center" field="applyStatus" :formatter="({row, column, cellValue, index}) => { return applyStatusTypeFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="凭证号" align="center" field="voucherNum" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}"/>
      <vxe-table-column title="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="openShowAccount(scope.row)">付款账户详情</el-button>
<!--          <el-button type="text"  @click="showPic(scope.row)">查看财务截图</el-button>-->
<!--          <el-button type="text" v-if="scope.row.applyStatus !== 2 && scope.row.applyStatus !== 4 &&ifrole(chuna)" @click="editInfo(scope.row)">修改</el-button>-->
<!--          <el-button type="text" v-if="scope.row.applyStatus == 1 && ifrole(zongjian)" @click="confirmMoney(scope.row)">审批通过</el-button>-->
<!--          <el-button type="text" v-if="scope.row.applyStatus == 1&& ifrole(zongjian)" @click="refuseApply(scope.row)" >审批驳回</el-button>-->
          <!-- <el-button type="text" v-if="scope.row.applyStatus == 2&& ifrole(caiwu)" @click="saveVoucherReceive(scope.row)">录入凭证号</el-button> -->
          <el-button type="text" v-if="receListData[scope.row.id] && receListData[scope.row.id].count==0" @click="createReceCertById(scope.row)">生成凭证</el-button>
          <el-button type="text" v-if="receListData[scope.row.id] && receListData[scope.row.id].count" @click="showTranByCode(scope.row,receListData[scope.row.id].code)">查看凭证</el-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryParams.pageSize"
      :current-page="queryParams.pageNo"
      @current-change="eventPage"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
    >
    </el-pagination>

    <el-dialog
      class="xuanxiang2"
      :title="isAdd?'新增付款':'修改付款'"
      :visible.sync="showApply"
      width="60%"
      :before-close="ApplyhandleClose"
      :close-on-click-modal=false
    >
      <div>
        <div style="font-size: 16px;margin-bottom: 10px;">付款基本信息</div>
        <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款类型：</div>
          <div class="width">
            <FcItem :list="fundsClassList" @clkItem="clkFundsItem" v-model="fundsSelItemId" ></FcItem>
            <!-- <SimplePanelSelection
              class="width"
              @setVal="costType"
              :allData="typeList"
              modeName="付款类型"
              keyStr="code"
              valStr="value"
              ref="costType"
              componentsWidth="600"
            ></SimplePanelSelection> -->
          </div>
        </div>
        <div style="margin: 10px 0"></div>
        <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 100px;font-size: 14px;color: #6A6C70;">支付方式：</div>
          <div class="width" style="display: flex;align-items: center;">
            <el-tag style="margin-right: 10px;cursor: pointer;" :type="switchComp.paymentType==item.code?'success':'info'"
                    v-for="(item,index) in  paymentTypeList" :key="index" @click="clkPaymentType(item)"
            >{{item.value}}</el-tag>
             <div style="display: flex;align-items: center;margin-left: 20px;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">发生时间</div>
              <el-date-picker
                v-model="switchComp.occurDate"
                style="width: 100%;"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              />
            </div>
          </div>
        </div>
        <div v-if="switchComp.paymentType == 2">
          <div style="margin: 10px 0"></div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款公司：</div>
            <div class="width">
              <el-tag style="margin-right: 10px;cursor: pointer;" :type="switchComp.paymentCompany==item.deptId?'success':'info'"
                      v-for="(item,index) in  allCompanyList" :key="index" @click="selectPaymentCompany(item)"
              >{{item.name}}</el-tag>
            </div>
          </div>
          <div>
            <el-table
              v-if="switchComp.paymentType && switchComp.costType && switchComp.paymentCompany"
              ref="singleTable"
              :data="accountlist"
              highlight-current-row
              @current-change="handleCurrentChange"
              style="width: 100%">
              <el-table-column
                key="0"
                width="30"
                fixed
              >
                <template slot-scope="scope"><img :class="!scope.row.checked?'showpic':''" style="width: 14px;vertical-align: middle;" src="../../../assets/images/yixuanze.png"></template>
              </el-table-column>
              <el-table-column fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}"/>
              <el-table-column fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" ></el-table-column>
              <el-table-column align="center" property="accountName" label="账户名称" width="120px"></el-table-column>
              <el-table-column align="center" property="bankName" label="银行名称"></el-table-column>
              <el-table-column align="center" property="accountNumber" label="账户号"></el-table-column>
              <el-table-column align="center" property="initializationMoney" label="余额" :formatter="NumFmt2"></el-table-column>
            </el-table>
          </div>
        </div>
        <div v-if="switchComp.paymentType == 3">
          <div style="margin: 10px 0"></div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款公司：</div>
            <div class="width">
              <el-tag style="margin-right: 10px;cursor: pointer;" :type="switchComp.paymentCompany==item.deptId?'success':'info'"
                      v-for="(item,index) in  allCompanyList" :key="index" @click="selectPaymentCompany(item)"
              >{{item.name}}</el-tag>
            </div>
          </div>
          <div>
            <el-table
              v-if="switchComp.paymentType && switchComp.costType && switchComp.paymentCompany"
              ref="singleTable"
              :data="accountlist"
              highlight-current-row
              @current-change="handleCurrentChange"
              style="width: 100%">
              <el-table-column
                key="0"
                width="30"
                fixed
              >
                <template slot-scope="scope"><img :class="!scope.row.checked?'showpic':''" style="width: 14px;vertical-align: middle;" src="../../../assets/images/yixuanze.png"></template>
              </el-table-column>
              <el-table-column fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}"/>
              <el-table-column fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" ></el-table-column>
              <el-table-column align="center" property="accountName" label="账户名称" width="120px"></el-table-column>
              <el-table-column align="center" property="bankName" label="银行名称"></el-table-column>
              <el-table-column align="center" property="accountNumber" label="账户号"></el-table-column>
              <el-table-column align="center" property="initializationMoney" label="余额" :formatter="NumFmt2"></el-table-column>
            </el-table>
          </div>
        </div>
        <div v-if="switchComp.paymentType == 1">
          <div style="margin: 10px 0"></div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款公司：</div>
            <div class="width">
              <el-tag style="margin-right: 10px;cursor: pointer;" :type="switchComp.paymentCompany==item.deptId?'success':'info'"
                      v-for="(item,index) in  allCompanyList" :key="index" @click="selectPersonalPaymentCompanyAll(item)"
              >{{item.name}}</el-tag>
            </div>
          </div>
          <div>
            <el-table
              v-if="switchComp.paymentType && switchComp.costType && switchComp.paymentCompany"
              ref="singleTable"
              :data="accountlist"
              highlight-current-row
              @current-change="handleCurrentChange"
              style="width: 100%">
              <el-table-column
                key="0"
                width="30"
                fixed
              >
                <template slot-scope="scope"><img :class="!scope.row.checked?'showpic':''" style="width: 14px;vertical-align: middle;" src="../../../assets/images/yixuanze.png"></template>
              </el-table-column>
              <el-table-column fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}"/>
              <el-table-column fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" ></el-table-column>
              <el-table-column align="center" property="accountName" label="账户名称" width="120px"></el-table-column>
              <el-table-column align="center" property="bankName" label="银行名称"></el-table-column>
              <el-table-column align="center" property="accountNumber" label="账户号"></el-table-column>
              <el-table-column align="center" property="initializationMoney" label="余额" :formatter="NumFmt2"></el-table-column>
            </el-table>
          </div>
        </div>
        <div v-if="switchComp.paymentType == 3">
          <div style="margin: 10px 0"></div>
          <!-- 选择承兑汇票: -->
           <div style="display: flex;justify-content:space-around;align-items: center;">
            <div class="flexleft">
              <span style="flex-shrink:0;">票号</span>  <el-input placeholder="多个票号使用|分隔"  v-model="cdquery.number" clearable></el-input>
            </div>
            <div class="flexleft">
              <span style="flex-shrink:0;">最小金额</span> <el-input placeholder="最小金额"  v-model="cdquery.balance" oninput="value=value.replace(/[^\d\.]/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')" clearable></el-input>
            </div>
            <div class="flexleft">
              <span style="flex-shrink:0;">到期日</span> <el-date-picker v-model="cdquery.expireDate" @change="loadCdAndQuery" clearable type="daterange"  range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" ></el-date-picker>
            </div>
            <el-button @click="loadCdAndQuery" type="primary">搜索</el-button>
           </div>


            <!-- <div style="display: flex;justify-content: left;align-items: center;"> -->
                <el-table ref="cdtable"  :data="cdList" @row-click="selRowCdToggle" @selection-change="calculateMoney">
                  <el-table-column
                  type="selection"
                  width="55">
                </el-table-column>
                  <el-table-column label="票号" property="ticketNumber">
                  </el-table-column>
                    <el-table-column label="金额" property="balance" :formatter="NumFmtMoney">
                  </el-table-column>
                    <el-table-column label="到期日" property="expireDate"   :formatter="(row, column, cellValue, index) => { return myFmtDateTime(cellValue,'YYYY-MM-DD')}">
                  </el-table-column>
                    <el-table-column label="承兑人" property="acceptorName">
                  </el-table-column>
                </el-table>

                <el-pagination
                  background
                  hide-on-single-page
                  layout="prev, pager, next"
                  :total="cdtotal"
                  :page-size="cdquery.pageSize"
                  :current-page="cdquery.pageNo"
                  @current-change="cdListPage"
                  style="text-align: right;padding: 10px 0px;background-color: #fff"
                >
                </el-pagination>
            <!-- </div> -->
          </div>
        <div style="margin: 10px 0"></div>
        <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款金额：</div>
          <el-input
            class="width"
            type="text"
            clearable
            placeholder="输入付款金额"
            label="付款金额"
            @input="NumberToChinese(switchComp.money)"
            v-model="switchComp.money"></el-input>
        </div>
        <div style="margin: 10px 0"></div>
        <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 100px;font-size: 14px;color: #6A6C70;">大写：</div>
          <el-input
            class="width"
            clearable
            disabled
            v-model="switchComp.moneyC"></el-input>
        </div>
      </div>
      <div style="margin: 10px 0"></div>
      <div style="margin-top: 20px;">
        <div style="font-size: 16px;margin-bottom: 10px;">收款账户信息</div>
          <div style="margin: 10px 0"></div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">客户名称：</div>
            <el-input
              class="width"
              clearable
              placeholder="输入客户名称"
              label="客户名称"
              v-model="switchComp.receiveCompany"></el-input>
          </div>
        <div v-if="switchComp.paymentType != 1">
          <div style="margin: 10px 0"></div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">收款银行：</div>
            <el-input
              class="width"
              clearable
              placeholder="输入收款银行"
              label="收款银行"
              v-model="switchComp.receiveBank"></el-input>
          </div>
          <div style="margin: 10px 0"></div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">收款账号：</div>
            <el-input
              class="width"
              clearable
              placeholder="输入收款账号"
              label="收款账号"
              v-model="switchComp.receiveAccount"></el-input>
          </div>
        </div>
        <div style="margin: 10px 0"></div>
        <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款说明：</div>
          <el-input
            placeholder="输入付款说明（选填）"
            label="付款说明"
            type="textarea"
            :rows="2"
            maxlength="255"
            show-word-limit
            v-model="switchComp.remark"></el-input>
        </div>
        <div v-if="switchComp.applyStatus == 3">
          <div style="margin: 10px 0"></div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">驳回意见：</div>
            <el-input type="textarea" disabled v-model="switchComp.refuseOpinion" placeholder="暂无说明"></el-input>
          </div>
        </div>
<!--        <div style="margin: 10px 0"></div>-->
<!--        <div style="display: flex;justify-content: left;align-items: center;">-->
<!--          <div style="width: 90px;font-size: 14px;color: #6A6C70;">上传截图：</div>-->
<!--          <div>-->
<!--            <el-upload-->
<!--              ref="upload"-->
<!--              accept=".jpg,.png,.gif,.jpeg,.bmp"-->
<!--              :file-list="fileList"-->
<!--              :action="uploadUrl"-->
<!--              list-type="picture-card"-->
<!--              :limit="1"-->
<!--              :before-remove="handleRemove"-->
<!--              :on-error="uploadError"-->
<!--              :on-success="uploadSuccess"-->
<!--              :before-upload="beforeAvatarUpload"-->
<!--              :on-preview="handlePictureCardPreview"-->
<!--              :class="picList.length>=1?'hiddenClass':''"-->
<!--            >-->
<!--              <i class="el-icon-plus"></i>-->
<!--            </el-upload>-->
<!--            <el-dialog :visible.sync="dialogVisible">-->
<!--              <img width="100%" :src="dialogImageUrl" alt="">-->
<!--            </el-dialog>-->
<!--          </div>-->
<!--        </div>-->
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="subAddLoading" @click="submit" v-if="isAdd">提交</el-button>
        <el-button type="primary" @click="updateObj" v-if="!isAdd">保存</el-button>
      </span>
    </el-dialog>

    <el-dialog
      class="xuanxiang2"
      title="付款账户详情"
      :visible.sync="showAccount"
      width="60%"
      :before-close="showAccounthandleClose"
      :close-on-click-modal=false>
<!--      <div style="font-size: 20px;font-weight: 500;margin-bottom: 10px">付款单编号：{{waterDetail.waterNo}}</div>-->
      <div style="display: flex;margin-bottom: 10px;width: 100%">
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>付款类型：</div>
          <div>{{ waterDetail.fundsClassName || costTypeFmt(null,null,waterDetail.costType,null)}}</div>
        </div>
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>支付方式：</div>
          <div>{{receviceTypeFmt(null,null,waterDetail.paymentType,null)}}</div>
        </div>
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>付款金额：</div>
          <div>{{NumFmt(null,null,waterDetail.money,null)}}元</div>
        </div>
      </div>
      <el-table
        ref="singleTable"
        :data="showAccountlist"
        highlight-current-row
        style="width: 100%">
        <el-table-column fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}"/>
        <el-table-column fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" ></el-table-column>
        <el-table-column align="center" property="accountName" label="账户名称" width="120px"></el-table-column>
        <el-table-column align="center" property="bankName" label="银行名称"></el-table-column>
        <el-table-column align="center" property="accountNumber" label="账户号"></el-table-column>
        <el-table-column align="center" property="initializationMoney" label="余额" :formatter="NumFmt2"></el-table-column>
      </el-table>
        <div v-if="waterDetail.paymentType == 3" style="display: flex;margin-bottom: 10px;width: 100%;">
        <el-table
          border
          :data="cdPaymentList"
          style="width: 100%"
        >
          <el-table-column align="center" property="ticketNumber" label="票号" />
          <el-table-column align="center" property="ticketType" label="票种" :formatter="(row, column, cellValue, index) => { return ticketTypeFmt(null,null,cellValue,null)}" />
          <el-table-column align="center" property="balance" label="金额" :formatter="(row, column, cellValue, index) => { return NumFmt(null,null,cellValue,null)+'元'}" />
          <el-table-column align="center" property="expireDate" label="到期时间" :formatter="(row, column, cellValue, index) => { return myFmtDateTime(cellValue,'YYYY-MM-DD') }" />
          <el-table-column align="center" property="picList" label="明细">
            <template slot-scope="scope">
              <span v-show="!scope.row.picList">-</span>
              <el-button v-show="scope.row.picList" type="text" @click="showPic(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="margin-top: 10px">
        <div style="font-size: 20px;font-weight: 500;margin-bottom: 5px">付款说明：</div>
        <el-input type="textarea" disabled v-model="waterDetail.remark" placeholder="暂无说明"></el-input>
      </div>
    </el-dialog>

    <el-dialog
      class="xuanxiang"
      title="查看财务截图"
      :visible.sync="showPicApply"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal=false
    >
      <img style="width: 100%;height: 100%;" :src="picUrl"/>
    </el-dialog>

    <!-- 生成凭证对话框 -->
    <el-dialog
      title="生成凭证"
      :visible.sync="dialogYacc"
      width="80%"
      :before-close="() => dialogYacc = false"
      :close-on-click-modal="false"
    >
      <Subacc :tableData="tableDataAcc"
      :paymentType="paymentType"
      :accounts="ledgerAccountList"
       @update:tableData="v=> tableDataAcc = v"
      @ledgerAccount="v=> ledgerAccount = v"
      :classList="kjClassList"></Subacc>
       <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogYacc = false">取消</el-button>
          <el-button type="primary" @click="saveYacc()">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看凭证对话框 -->
    <el-dialog
      title="查看凭证"
      :visible.sync="dialogTarnShow"
      width="80%"
      :before-close="() => dialogTarnShow = false"
      :close-on-click-modal="false"
    >
      <resultTrans v-for="(item,idx) in showTableTrans" :key="idx" :tableData="item" :classList="kjClassList"></resultTrans>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogTarnShow = false">关闭</el-button>
        </div>
    </template>
    </el-dialog>
  </div>
</template>

<script>
import 'vxe-table/lib/style.css'
import {getAcctgTransListByCode, getHaokjSub, getPaymentVoucher, getVoucherStatusByPaymentIds, saveVoucher } from '@/api/business/processapi'
import { onlyTreeList } from '@/api/system/fundsClassification'
    import { UPLOAD_URL } from '@/utils/config'
import FcItem from '@/views/components/fcitem/fcitem'
import resultTrans from '@/views/system/businessPayment/resultTrans.vue'
import Subacc from '@/views/system/businessPayment/subacc.vue'
    import currencyjs from 'currency.js'
    import dayjs from "dayjs";
    import Cookies from "js-cookie";
import VXETable from 'vxe-table'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx';
    import {getPaymentNum} from "../../../api/business/wxDepartmentapi";
    import {getDictionaryList} from "../../../api/system/baseInit";
    import {userRole} from "../../../api/system/jurisdiction";
    import {
      add,
      addPaymentWaterOther,
      edit,
      getPaymentWaterList,
      getTicketsByPaymentId,
      isRepeatPaymentByCompanyAndPrice,
      updatePaymentById
    } from "../../../api/system/paymentWater"
    import {saveVoucherOtherPayment, saveVoucherReceive} from "../../../api/system/processVoucherRecord";
    import {getReceiveWaterListByAccountId} from '../../../api/system/receiveWater'
    import {selectPersonalAccount, selectbyDeptId, selectbyId } from "../../../api/system/sysAccount";
    import PanelSelection from '../../../components/PanelSelection/panelSelection'
    import SimplePanelSelection from '../../../components/PanelSelection/simplePanelSelection'
    import {fmtDictionary} from "../../../utils/util";
    VXETable.use(VXETablePluginExportXLSX)
    export default {
        name: "OtherPayment",
        components: {PanelSelection, SimplePanelSelection, FcItem, Subacc, resultTrans},
        data() {
            return {
              cdtotal:0,
              cdList:[],
              cdPaymentList:[],
              cdquery: {
                number:'',
                accountId:'',
                pageNo:1,
                pageSize:30,
                expireDate:'',
                balance:''
              },
              subAddLoading:false, // 提交按钮加载状态
              upload: UPLOAD_URL,
              toolbarButtons:[],
              applyStatusList:[
                {
                  code:0,
                  value:"出纳付款"
                },
                {
                  code:1,
                  value:"出纳付款"
                },
                {
                  code:2,
                  value:"财务记账"
                },
                {
                  code:3,
                  value:"审批驳回"
                },
                {
                  code:4,
                  value:"记账完成"
                }
              ],
              queryParams:{
                paymentType:null,
                paymentCompany:null,
                receiveCompany:null,
                pageSize:10,
                pageNo:1,
                costType: null,
                fundsClassId: null,
              },
              ticketTypeList:[
                {code:1,value:'银行承兑'},
                {code:2,value:'商业承兑'},
              ],
              typeList:[],
              total:0,
              loading:false,
              dataList:[],
              showApply:false,
              isAdd:true,
              switchComp: {
                costType:null,
                show: true,
                money: 0,
                loading: false,
                remark: '',
                receiveCompany: undefined,
                paymentCompany:null,
                paymentCompanyName:null,
                paymentType: undefined,
                ticketNumber:null,
                receiveBank:null,
                receiveAccount:null,
                receiveBank:null,
                receiveAccount:null,
                ticketType:null,
                expireDate:null,
                waterNo:null,
                moneyC:null,
                occurDate:dayjs(new Date()).format('YYYY-MM-DD'),
              },
              allCompanyList:[],
              accountlist:[],
              customerList:[],
              supplierList:[],
              paymentTypeList:[],
              currentRow:null,
              dictionaryLists:[],
              submitObj:{},
              tableData:[],
              applicationForm:{},
              receiveWater:{},
              showAccount:false,
              waterDetail:{},
              showAccountlist:[],
              picList:[],
              fileList:[],
              uploadUrl: UPLOAD_URL,
              dialogImageUrl: '',
              dialogVisible: false,
              showPicApply:false,
              picUrl:null,
              chuna:["chuna","waizhangchuna"],
              zongjian:["caiwuzhuguan"],
              caiwu: ["neizhangkuaiji", "waizhangkuaiji"],
              fundsQueryId:[],
              fundsClassList: [],
              fundsSelItemId:[],
              receListData: {},
              tableDataAcc: [],
              tableProcessIdAcc: null,
              monthDateAcc: dayjs().format('YYYYMM'),
              dialogYacc: false,
              dialogTarnShow: false,
              showTableTrans: [],
              kjClassList: [],
              ledgerAccount: '',
              isCertModel: '1',
              ctypeCode: '',
              paymentType: null,
              ledgerAccountList:[]
            };
        },
      created() {
        this.loadFundsClassList()
        var list = Cookies.get("deptIds")
        if(list){
          this.userCompanyIds = JSON.parse(Cookies.get("deptIds"))
          console.info(this.userCompanyIds)
        }
        var getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuex')
        var getCustomer = this.$store.dispatch('data/getCustomerListSaveInVuex')
        var getCompany = this.$store.dispatch('data/getCompanyListSaveInVuex')
        var dic = getDictionaryList("settlement_type,fdbiz_payment_type")
        Promise.all([getSupplier,getCustomer,dic,getCompany]).then(values => {
          let res  = values[0];
          if(res){
            this.supplierList = res.supplierList
          }
          res  = values[1];
          if(res){
            this.customerList = res.customerList
          }
          res  = values[2];
          if (res) {
            this.dictionaryLists = res.map
            this.paymentTypeList = res.map['settlement_type']
            this.typeList = res.map['fdbiz_payment_type'].filter(item=>item.spare1 == 1)
          }
          res  = values[3];
          if (res) {
            if(this.userCompanyIds && this.userCompanyIds.length>0){
              var companys = res.companyList
              var ids = this.userCompanyIds.map(item=> {return Number(item)})
              this.allCompanyList =companys.filter(tmp=> ids.includes(tmp.deptId))
            }
            if(!this.allCompanyList || this.allCompanyList.length<=0 ){
              this.allCompanyList = res.companyList
            }
          }
          this.topage()
        })

        // 加载科目列表
        this.loadKeMuList()
      },
      mounted() {
        document.addEventListener('paste', this.onPasteUpload)
        // 获取凭证状态
        this.getVoucherStatusByReceIds(this.dataList.map(i=>i.id).join(','))
      },
      beforeDestroy() {
        document.removeEventListener('paste', this.onPasteUpload)
      },
  methods: {
    clkFundsItem(item) {
      const data = {
        code: item.typeCode && item.typeCode == 'wuliu' ? 1 : -1,
      }
      this.costType(data)
    },
    loadFundsClassList() {
      if(this.fundsClassList.length == 0) {
        onlyTreeList('out').then(res => {
          if (res.resultCode == '0' && res.data) {
            this.fundsClassList = res.data
            // console.log('fundsClassList', this.fundsClassList)
          }
        })
      }
    },
    clkQuery() {
      this.queryParams.fundsClassId = this.fundsQueryId && this.fundsQueryId.length > 0 ? this.fundsQueryId[this.fundsQueryId.length - 1] : null
      this.topage()
    },
        selRowCdToggle(row,column,event){
          console.log('table',this.$refs.cdtable)
          this.$refs.cdtable.toggleRowSelection(row)
          // 根据选中 计算金额
          this.calculateMoney()
        },
        calculateMoney(){
          if(!this.$refs.cdtable){
            return
          }
          var rows = this.$refs.cdtable.selection
          var money = 0
          rows.forEach(item=>{
            money = currencyjs(item.balance).add(money).value
          })
          this.switchComp.money = money
          this.NumberToChinese(this.switchComp.money)
        },
        NumberToChinese(money) {
          var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆',
            '柒', '捌', '玖');
          // 基本单位
          var cnIntRadice = new Array('', '拾', '佰', '仟');
          // 对应整数部分扩展单位
          var cnIntUnits = new Array('', '万', '亿', '兆');
          // 对应小数部分单位
          var cnDecUnits = new Array('角', '分', '毫', '厘');
          // 整数金额时后面跟的字符
          var cnInteger = '整';
          // 整型完以后的单位
          var cnIntLast = '元';
          // 最大处理的数字
          var maxNum = 999999999999999.9999;
          // 金额整数部分
          var integerNum;
          // 金额小数部分
          var decimalNum;
          // 输出的中文金额字符串
          var chineseStr = '';
          // 分离金额后用的数组，预定义
          var parts;
          if (money == '') {
            return '';
          }
          money = Number.parseFloat(money);
          if (money >= maxNum) {
            // 超出最大处理数字
            return '';
          }
          if (money == 0) {
            chineseStr = cnNums[0] + cnIntLast + cnInteger;
            return chineseStr;
          }
          // 转换为字符串
          money = money.toString();
          if (money.indexOf('.') == -1) {
            integerNum = money;
            decimalNum = '';
          } else {
            parts = money.split('.');
            integerNum = parts[0];
            decimalNum = parts[1].substr(0, 4);
          }
          // 获取整型部分转换
          if (Number.parseInt(integerNum, 10) > 0) {
            var zeroCount = 0;
            var IntLen = integerNum.length;
            for (var i = 0; i < IntLen; i++) {
              var n = integerNum.substr(i, 1);
              var p = IntLen - i - 1;
              var q = p / 4;
              var m = p % 4;
              if (n == '0') {
                zeroCount++;
              } else {
                if (zeroCount > 0) {
                  chineseStr += cnNums[0];
                }
                // 归零
                zeroCount = 0;
                chineseStr += cnNums[Number.parseInt(n)]
                  + cnIntRadice[m];
              }
              if (m == 0 && zeroCount < 4) {
                chineseStr += cnIntUnits[q];
              }
            }
            chineseStr += cnIntLast;
          }
          // 小数部分
          if (decimalNum != '') {
            var decLen = decimalNum.length;
            for (var i = 0; i < decLen; i++) {
              var n = decimalNum.substr(i, 1);
              if (n != '0') {
                chineseStr += cnNums[Number(n)] + cnDecUnits[i];
              }
            }
          }
          if (chineseStr == '') {
            chineseStr += cnNums[0] + cnIntLast + cnInteger;
          } else if (decimalNum == '') {
            chineseStr += cnInteger;
          }
          this.switchComp.moneyC =  chineseStr;
        },
        ifrole(list){
          return userRole(list)
        },
        onPasteUpload(e) {
          console.log('--------')
          console.log(e)
          const upload = this.$refs.upload
          if (!upload) {
            return
          }
          const items = e.clipboardData.items
          for (const item of items) {
            if (item.type === 'image/png') {
              const file = new File([item.getAsFile()], new Date().getTime() + '.png')
              upload.handleStart(file)
            }
          }
          upload.submit()
        },
        handleClose(){
          this.showPicApply = false
        },
        showPic(item){
          if(!item.picList){
            this.$message.warning('该流水暂无财务截图!')
            return
          }
          this.picUrl = null
          this.showPicApply = true
          this.picUrl = item.picList
        },
        toremove(file, fileList){
          if(file.response != undefined && file.response != null && file.response != ""){
            console.info(file,fileList)
            var list = Object.assign([],this.picList)
            this.picList = []
            for(var i=0;i<list.length;i++){
              if(list[i]!= file.response.url){
                this.picList.push(list[i])
              }
            }
          } else {
            console.info(file,fileList)
            var list = Object.assign([],this.picList)
            this.picList = []
            for(var i=0;i<list.length;i++){
              if(list[i]!= file.url){
                this.picList.push(list[i])
              }
            }
          }
        },
        handlePictureCardPreview(file) {
          this.dialogImageUrl = file.url
          this.dialogVisible = true
        },
        // 上传前对文件的大小的判断
        beforeAvatarUpload (file) {
          const isLt5M = file.size / 1024 / 1024 < 5
          if (!isLt5M) {
            this.$message.warning('上传图片大小不能超过 5 MB!')
          }
          return isLt5M
        },
        // 上传成功后的回调
        uploadSuccess (response, file, fileList) {
          this.picList.push(response.url)
          console.info(this.picList.toString())
        },
        uploadError (response, file, fileList) {
          this.$message.error(`上传失败，请重试！`)
        },
        handleRemove(file, fileList) {
          return this.$confirm("此操作将删除该截图, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.toremove(file, fileList)
            return true
          });
        },
        showAccounthandleClose(){
          this.showAccount = false
        },
        openShowAccount(obj){
          console.info(obj)
          this.waterDetail = obj
          if(obj.sysAccountId){
            selectbyId(obj.sysAccountId).then(res =>{
              if (res) {
                this.showAccountlist = res.list
              }
            })
          }
          this.cdPaymentList = []
          if(obj.paymentType == '3'){
            // 加载汇票单列表
            getTicketsByPaymentId(obj.id).then(res=>{
              if(res){
                this.cdPaymentList = res.data
              }
            })
          }
          this.showAccount = true
        },
        refuseApply(obj){
          this.$prompt('请输入驳回意见', '提示', {
            confirmButtonText: '提交',
            cancelButtonText: '取消',
          }).then(({ value }) => {
            var data = {
              id:obj.id,
              applyStatus:3,
              opinion:value,
            }
            updatePaymentById(data).then(res=>{
              if(res &&res.resultCode == '0'){
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                });
                this.topage()
              }
            })
          }).catch(()=>{
            this.$message({
              type: 'info',
              message: '已取消'
            });
          })
        },
        confirmMoney(obj){
          this.$confirm("此操作将通过该次付款审批, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            var data = {
              id:obj.id,
              applyStatus:2,
              opinion:null,
            }
            updatePaymentById(data).then(res=> {
              if (res && res.resultCode == '0') {
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                });
                this.topage()
              }
            })
          }).catch(()=>{
            this.$message({
              type: 'info',
              message: '已取消'
            });
          })
        },
        editInfo(obj){
          this.showApply = true
          this.isAdd = false
          this.switchComp = Object.assign({},obj)
          var type = 0
          if(this.switchComp.paymentType == 3){
            type = 1
          } else if(this.switchComp.paymentType == 1) {
            type = 2
          }
          selectbyDeptId(this.switchComp.paymentCompany,type).then(res =>{
            if (res) {
              for (var i = 0; i < res.list.length; i++) {
                var tmp = res.list[i]
                tmp['checked'] = false
              }
              this.accountlist = res.list
              for(var i=0;i<res.list.length;i++){
                if(res.list[i].id ==this.switchComp.sysAccountId){
                  this.currentRow = res.list[i];
                  res.list[i].checked = true
                  this.$refs.singleTable.setCurrentRow(this.currentRow);
                  break;
                }
              }
            }
          })
          this.$nextTick(function () {
            this.$refs.costType.choseKey(obj.costType)
          })
        },
        saveVoucherReceive(obj){
          this.$prompt('请输入凭证号', '提示', {
            confirmButtonText: '提交',
            cancelButtonText: '取消',
          }).then(({ value }) => {
            var data = {
              id:obj.id,
              applyStatus:4,
              opinion:null,
            }
            var save = saveVoucherOtherPayment(value,obj.id)
            var update = updatePaymentById(data)
            Promise.all([save,update]).then(values => {
              const res1  = values[0];
              const res2 = values[1];
              if(res1 && res2){
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                });
                this.topage()
              }
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
        },
        addReceiveWater(){
          this.fileList = []
          this.picList = []
          this.cdList = []
          this.cdtotal = 0;

          this.isAdd = true
          this.showApply = true
          this.currentRow = null
          this.submitObj={},
          this.fundsSelItemId = []
          this.switchComp= {
            costType:null,
            show: true,
            money: 0,
            loading: false,
            remark: '',
            receiveCompany: undefined,
            paymentCompany:null,
            paymentCompanyName:null,
            paymentType: undefined,
            receiveBank:null,
            receiveAccount: null,
            fundsClassId: null,
            occurDate:dayjs(new Date()).format('YYYY-MM-DD'),
          }
          // this.$nextTick(function () {
          //   this.$refs.costType.clean()
          // })
        },
        submit: function () {
          this.subAddLoading=true
          if (!this.switchComp.costType) {
            this.$message.error('请选择付款类型')
            this.subAddLoading=false
            return;
          }
          if (!this.switchComp.paymentType) {
            this.$message.error('请选择支付方式')
            this.subAddLoading=false
            return;
          }
          if (!this.currentRow || !this.currentRow.id) {
            this.$message.error('请选择付款账户')
            this.subAddLoading=false
            return;
          }
          if (!this.switchComp.money) {
            this.$message.error('请输入金额')
            this.subAddLoading=false
            return;
          }
          if (!this.switchComp.receiveCompany) {
            this.$message.error('请输入收款客户')
            this.subAddLoading=false
            return;
          }
          // 根据收款方、金额验证是重复
          var data = {
            receiveCompany:this.switchComp.receiveCompany,
            money:this.switchComp.money,
            day:7,
          }
          isRepeatPaymentByCompanyAndPrice(data).then(res=>{
              if(res && res.resultCode == '0' && res.data){
                // 提重复
                this.$confirm('该收款方下已存在相同金额的付款申请，请确认是否再次提交','提示',{
                  confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(()=>{
                  this.addPush()
                }).catch(()=>{
                  this.subAddLoading=false
                })
              }else{
                this.addPayFuc()
              }
          }).catch(()=>{
            this.addPayFuc()
          })


        },
        addPush(){
          this.subAddLoading = false
           const loading = this.$loading({
              lock: true,
              text: '数据提交中，请稍后！',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            this.submitObj.type = 0 //类型:付款
            this.submitObj.money = this.switchComp.money
            this.submitObj.remark = this.switchComp.remark
            if (!!this.currentRow) {
              this.submitObj.sysAccountId = this.currentRow.id
            }
            this.submitObj.paymentType = this.switchComp.paymentType
            this.submitObj.paymentCompany = this.switchComp.paymentCompany
            this.submitObj.receiveCompany = this.switchComp.receiveCompany
            // this.submitObj.picList = this.picList.toString()
            this.submitObj.receiveBank = this.switchComp.receiveBank
            this.submitObj.receiveAccount = this.switchComp.receiveAccount
            this.submitObj.costType = this.costTypeFmt(null,null,this.switchComp.costType,null)
            this.submitObj.paymentCompanyName = this.switchComp.paymentCompanyName
            this.submitObj.waterNo = this.switchComp.waterNo
            this.submitObj.occurDate = this.switchComp.occurDate
            this.submitObj.fundsClassId = this.fundsSelItemId && this.fundsSelItemId.length>0 ? this.fundsSelItemId[this.fundsSelItemId.length-1] : null
            if(this.$refs.cdtable && this.$refs.cdtable.selection && this.$refs.cdtable.selection.length>0){
              this.submitObj.ticketNumbers = this.$refs.cdtable.selection.map(item=>item.ticketNumber).join(',')
            }
            addPaymentWaterOther(this.submitObj).then(res => {
              this.$message.success('保存成功')
              this.ApplyhandleClose()
            }).finally(()=>{
              loading.close()
              this.subAddLoading=false
            })
        },
        addPayFuc(){
          this.$confirm(`此操作将保存该费用信息且无法修改，是否继续?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.addPush()
          }).catch((e) => {
            this.subAddLoading=false
            this.$message({
              type: 'info',
              message: '错误：' + e
            });
          });
        },
        updateObj(){
          this.submitObj = this.switchComp
          this.submitObj.remark = this.switchComp.remark
          this.submitObj.applyStatus = 2
          if (!!this.currentRow) {
            this.submitObj.sysAccountId = this.currentRow.id
          }
          edit(this.submitObj).then(res=>{
            this.$message.success('修改成功')
          }).finally(() => {
            this.ApplyhandleClose()
          })
        },
        topage(){
          this.loading = true;
          var query = {
            paymentCompany:  this.queryParams.paymentCompany,
            receiveCompany: this.queryParams.receiveCompany,
            costType:this.queryParams.costType,
            paymentType:this.queryParams.paymentType,
            pageNo: this.queryParams.pageNo,
            pageSize: this.queryParams.pageSize,
            fundsClassId:this.queryParams.fundsClassId,
          }
          getPaymentWaterList(query).then(response => {
            const data = response.page;
            this.dataList = data.records;
            this.total = data.total;

            // 获取凭证状态
            if (this.dataList && this.dataList.length > 0) {
              this.getVoucherStatusByReceIds(this.dataList.map(i=>i.id).join(','))
            }
          }).finally(() => {
            this.loading = false;
          });
        },
        eventPage(e){
          this.queryParams.pageNo = e
          this.topage()
        },
        ApplyhandleClose(){
            this.showApply = false
            this.topage()
        },
        selectTicketType(id){
          this.switchComp.ticketType = id
        },
        selectPersonalPaymentCompanyAll(obj){
          if(this.switchComp.paymentCompany == obj.deptId){
            this.switchComp.paymentCompany=null
            this.switchComp.paymentCompanyName=null
            this.accountlist = []
          } else {
            this.switchComp.paymentCompany = obj.deptId
            this.switchComp.paymentCompanyName=obj.name
            // selectPersonalAccount(obj.deptId).then(res =>{
            //   if (res) {
            //     for (var i = 0; i < res.list.length; i++) {
            //       var tmp = res.list[i]
            //       tmp['checked'] = false
            //     }
            //     this.accountlist = res.list
            //   }
            // })
            var type = 0
            if(this.switchComp.paymentType == 3){
              type = 1
            } else if(this.switchComp.paymentType == 1) {
              type = 2
            }
            selectbyDeptId(this.switchComp.paymentCompany,type).then(res =>{
              if (res) {
                for (var i = 0; i < res.list.length; i++) {
                  var tmp = res.list[i]
                  tmp['checked'] = false
                }
                this.accountlist = res.list
              }
            })
            getPaymentNum(this.switchComp.paymentCompany).then(res=>{
              if(res){
                this.switchComp.waterNo = res.num
              }
            })
          }
          this.switchComp.receiveCompany = null
        },
        selectPaymentCompany(obj){
          this.cdList=[]
          this.cdtotal = 0;
          if(this.switchComp.paymentCompany == obj.deptId){
            this.switchComp.paymentCompany=null
            this.switchComp.paymentCompanyName=null
            this.accountlist = []
          } else {
            this.switchComp.paymentCompany = obj.deptId
            this.switchComp.paymentCompanyName=obj.name
            var type = 0
            if(this.switchComp.paymentType == 3){
              type = 1
            } else if(this.switchComp.paymentType == 1) {
              type = 2
            }
            selectbyDeptId(this.switchComp.paymentCompany,type).then(res =>{
              if (res) {
                for (var i = 0; i < res.list.length; i++) {
                  var tmp = res.list[i]
                  tmp['checked'] = false
                }
                this.accountlist = res.list
              }
            })
            getPaymentNum(this.switchComp.paymentCompany).then(res=>{
              if(res){
                this.switchComp.waterNo = res.num
              }
            })
          }
          this.switchComp.receiveCompany = null
        },
        handleCurrentChange(val){
          this.currentRow = val;
          if(val && val.id){
              console.log('选择账户',val.id)
            // 根据账户id 加载承兑列表
            this.loadCdListByAccountId(val.id)
          }

          for(var i=0;i<this.accountlist.length;i++){
            var tmp = this.accountlist[i]
            if(tmp.id == val.id){
              tmp.checked = true
            } else {
              tmp.checked = false
            }
          }

        },
        loadCdListByAccountId(id){
          this.cdList=[]
          this.cdtotal = 0;

          // 判断 是否 承兑账户
          if(this.switchComp.paymentType == 3 && id){
            this.cdquery.accountId = id
            this.cdquery.pageNo = 1
            this.cdquery.number=''
            this.loadCdList()
          }
        },
        loadCdAndQuery(){
           this.cdquery.pageNo = 1
           this.loadCdList()
        },
        cdListPage(e){
          console.log(e,typeof e)
          // this.cdquery.pageNo = e
          this.cdquery.pageNo = e

          this.loadCdList()
        },
        loadCdList(){

          const queryData = {
            accountId:this.cdquery.accountId,
            number:this.cdquery.number,
            balance:this.cdquery.balance,
            pageNo:this.cdquery.pageNo,
            pageSize:this.cdquery.pageSize
          }
           if(this.cdquery.expireDate && this.cdquery.expireDate.length > 0){
             queryData.startTime = dayjs(this.cdquery.expireDate[0]).format('YYYY-MM-DD')
             queryData.endTime = dayjs(this.cdquery.expireDate[1]).format('YYYY-MM-DD')
          }

           getReceiveWaterListByAccountId(queryData).then(res=>{
              if(res && res.page){
                this.cdList = res.page.records
                this.cdtotal = res.page.total
                // this.cdquery.pageNo= res.page.current
              }
            })
        },
        costTypeQuery(item){
          this.queryParams.costType = item.code
        },
        costType(item){
          const id = item.code
          // console.log(id)
          // console.log(item.value)
          this.switchComp.costType = id
          this.accountlist = []
          var type = 0
          if(this.switchComp.paymentType == 3){
            type = 1
          } else if(this.switchComp.paymentType == 1) {
            type = 2
          }
          selectbyDeptId(this.switchComp.paymentCompany,type).then(res =>{
            if (res) {
              for (var i = 0; i < res.list.length; i++) {
                var tmp = res.list[i]
                tmp['checked'] = false
              }
              this.accountlist = res.list
            }
          })
        },
        paymentypeQuery(item){
          const id = item.code
          console.log(id)
          console.log(item.value)
          if(this.queryParams.paymentType == id){
            this.queryParams.paymentType=null
          } else {
            this.queryParams.paymentType = id
          }
          // 清空承兑列表
          this.cdList = []
          this.cdtotal = 0;

        },
        clkPaymentType(item){
          const id = item.code
          console.log(id)
          console.log(item.value)
          if(this.switchComp.paymentType == id){
            this.switchComp.paymentType=null
          } else {
            this.switchComp.paymentType = id
          }
          this.accountlist = []
          // if(this.switchComp.paymentType == 1){
          //   selectPersonalAccount(this.switchComp.paymentCompany).then(res =>{
          //     if (res) {
          //       for (var i = 0; i < res.list.length; i++) {
          //         var tmp = res.list[i]
          //         tmp['checked'] = false
          //       }
          //       this.accountlist = res.list
          //     }
          //   })
          // } else {
            var type = 0
            if(this.switchComp.paymentType == 3){
              type = 1
            } else if(this.switchComp.paymentType == 1) {
              type = 2
            }
            selectbyDeptId(this.switchComp.paymentCompany,type).then(res =>{
              if (res) {
                for (var i = 0; i < res.list.length; i++) {
                  var tmp = res.list[i]
                  tmp['checked'] = false
                }
                this.accountlist = res.list
              }
            })
          // }
        },
        myFmtDateTime(cellValue, fmtstr) {
          if (cellValue == undefined || cellValue == null || cellValue == "") {
            return "--";
          }
          return dayjs(cellValue).format(fmtstr);
        },
        costTypeFmt(row,column,cellValue,index) {
          return fmtDictionary(cellValue,this.dictionaryLists['fdbiz_payment_type']);
        },
        receviceTypeFmt(row,column,cellValue,index) {
          return fmtDictionary(cellValue,this.dictionaryLists['settlement_type']);
        },
        publicFmt(row,column,cellValue,index){
          var v = '--'
          if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
            v = cellValue
          }
          return v
        },
        NumFmt(row,column,cellValue,index){
          var v = 0
          if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
            v = Number.parseFloat(Math.abs(cellValue).toFixed(2)) && Number.parseFloat(Math.abs(cellValue).toFixed(2)).toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
          }
          return v
        },
        NumFmt2(row, column, cellValue, index) {
          var v = 0
          if (cellValue != undefined && cellValue != null && cellValue != '') {
            v = Number.parseFloat(Math.abs(cellValue).toFixed(2))
          }
          return v
        },
        NumFmtMoney(row, column, cellValue, index) {
          var v = 0
          if (cellValue != undefined && cellValue != null && cellValue != '') {
            v = currencyjs(cellValue,{ symbol:''}).format()
          }
          return v
        },
        paymentTypeFmt(row,column,cellValue,index){
          return fmtDictionary(cellValue,this.dictionaryLists['settlement_type']);
        },
        ticketTypeFmt(row,column,cellValue,index){
          return fmtDictionary(cellValue,this.ticketTypeList);
        },
        applyStatusTypeFmt(row,column,cellValue,index){
          return fmtDictionary(cellValue,this.applyStatusList);
        },

        // 凭证相关方法
        getVoucherStatusByReceIds(ids) {
          if (!ids) return
          getVoucherStatusByPaymentIds(ids).then(res => {
            if (res && res.resultCode === '0') {
              this.receListData = res.data || {}
            }
          })
        },
        // 生成凭证
        createReceCertById(row) {
          this.tableDataAcc = []
          this.tableProcessIdAcc = row.id
          this.paymentType = row.paymentType || 2
          this.ctypeCode = this.receListData[row.id].code
          this.dialogYacc = true
          this.getPaymentVoucher(row.id)
        },
        // 获取凭证数据
        getPaymentVoucher(id) {
          getPaymentVoucher(id).then(res => {
            if (res && res.resultCode === '0') {
              this.tableDataAccCode(res)
            }
          })
        },
        // 处理凭证数据
         tableDataAccCode(res) {
          console.log('1541',res)
        const list = res && res.data ? res.data : []
        // findSub
        this.tableDataAcc = list || []
        this.ledgerAccountList = res && res.accounts
        // this.tableDataAcc = list.map(item => {
        //   return {
        //     // summary: '计提'+this.monthDateStr+'工资及社保公积金',
        //     ...item
        //   }
        // })
        // this.dialogYacc = true
        this.upSubIdByFind()
    },
    upSubIdByFind() {
      if (this.tableDataAcc && this.tableDataAcc.length > 0 && this.kjClassList && this.kjClassList.length > 0) {
        // tableDataAcc 中 findSub 获取 kjClassList glAccountName 相等 ，treePath(2241^********^) = subject (2241)^
        //tableDataAcc -> findSub,subject
        // kjClassList -> glAccountName,treePath, glAccountCode
           // 承兑 现金
           if (3 == this.paymentType) {
           this.tableDataAcc[0].findSubId = '1121'
          this.tableDataAcc[0].subject = '1121'
          this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1122'
          this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1122'
         }
          if (1 == this.paymentType) {
            this.tableDataAcc[0].findSubId = '1001'
            this.tableDataAcc[0].subject = '1001'
             this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '5001'
            this.tableDataAcc[this.tableDataAcc.length - 1].subject = '5001'
          }

        for (let i = 0; i < this.tableDataAcc.length; i++) {
          if (this.tableDataAcc[i].findSub && !this.tableDataAcc[i].findSubId) {
            try {
              const fundsCode = this.kjClassList.find(item => item.treePath.startsWith(this.tableDataAcc[i].subject + '^')  && item.glAccountName == this.tableDataAcc[i].findSub)
              if (fundsCode) {
                this.tableDataAcc[i].findSubId = fundsCode.glAccountCode
                this.tableDataAcc[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }



      }
    },
        // 查看凭证
        showTranByCode(row,code) {
          // this.dialogTarnShow = true
          this.loadTranListByProcessIdAndCode(row.id,code)
        },
        // 加载交易数据
        loadTranListByProcessIdAndCode(id,code) {
            const loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.2)'
            })
          this.showTableTrans = []
          getAcctgTransListByCode(id,code).then(res => {
            this.showTableTrans = res && res.result
            this.dialogTarnShow = true
          }).finally(()=>{
            loading.close()
          })
        },
        // 保存凭证
         saveAccTable(type = '1', func = undefined) {
            // 验证数据，
            if (this.totalCredit != this.totalDebit) {
              this.$message.error('借方金额与贷方金额不一致')
              return
            }
            // if (type == '-1' && this.totalCredit > this.paymentTotal) {
            //   this.$message.error('借贷金额大于付款金额')
            //   return
            // }
            const loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.2)'
            })

            saveVoucher(this.tableProcessIdAcc, type, this.monthDateAcc,this.tableDataAcc,'',this.ledgerAccount).then((data) => {
              if (data.result) {
                this.$message.success('保存成功')
                func && func()
              } else {
                this.$message.error(data.msg || '保存失败')
              }
              getVoucherStatusByPaymentIds(this.tableProcessIdAcc).then(res => {
                if (res && res.data) {
                  this.receListData = Object.assign({}, this.receListData, res.data)
                }
              })

              loading.close()
            }).catch((err) => {
              this.$message.error('保存失败')
              loading.close()
            })
          },
        // 加载科目列表
        loadKeMuList() {
          getHaokjSub().then(res => {
            if (res && res.resultCode === '0') {
              this.kjClassList = res.data || []
            }
          })
        },
         handleYacc() {
      this.dialogYacc=false
    },
         saveYacc() {
            // save
            // this.saveAccTable('1', () => {
            this.saveAccTable(this.ctypeCode, () => {
              this.handleYacc()
            })
          },
        // 保存凭证并关闭对话框
      }
}
</script>

<style scoped>
  .width{
    width: 100%;
  }
  .query-form{
    text-align: left;
    padding-top: 10px;
  }
  .sp_add_left{
    width: 45%;
    margin-right: 5%;
  }
  .sp_add_right{
    width: 45%;
  }
  .el-table-info >>> .cell{
    text-align: center;
  }
  .el-table-info >>> th {
    background: #EDF5FF;
  }
  .hiddenClass >>> .el-upload {
    display: none;
  }
  .showpic{
    display: none;
  }
  .flexcenter{
  display: flex;
  justify-content: center;
}
.flexleft{
  display: flex;
  justify-content: left;
  align-items: center;
}
</style>
