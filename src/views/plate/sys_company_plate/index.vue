<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="id" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="wxDepartmentId" prop="wxDepartmentId">
            <el-input v-model="form.wxDepartmentId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="dictId" prop="dictId">
            <el-input v-model="form.dictId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="dictName" prop="dictName">
            <el-input v-model="form.dictName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="createBy" prop="createBy">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="createDate" prop="createDate">
            <el-input v-model="form.createDate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="updateBy" prop="updateBy">
            <el-input v-model="form.updateBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="updateDate" prop="updateDate">
            <el-input v-model="form.updateDate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="delFlag" prop="delFlag">
            <el-input v-model="form.delFlag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="spare1" prop="spare1">
            <el-input v-model="form.spare1" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="spare2" prop="spare2">
            <el-input v-model="form.spare2" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="spare3" prop="spare3">
            <el-input v-model="form.spare3" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="spare4" prop="spare4">
            <el-input v-model="form.spare4" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="spare5" prop="spare5">
            <el-input v-model="form.spare5" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="wxDepartmentId" label="wxDepartmentId" />
        <el-table-column prop="dictId" label="dictId" />
        <el-table-column prop="dictName" label="dictName" />
        <el-table-column prop="createBy" label="createBy" />
        <el-table-column prop="createDate" label="createDate">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateBy" label="updateBy" />
        <el-table-column prop="updateDate" label="updateDate">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="delFlag" label="delFlag" />
        <el-table-column prop="spare1" label="spare1" />
        <el-table-column prop="spare2" label="spare2" />
        <el-table-column prop="spare3" label="spare3" />
        <el-table-column prop="spare4" label="spare4" />
        <el-table-column prop="spare5" label="spare5" />
        <el-table-column v-permission="['admin','sysCompanyPlate:edit','sysCompanyPlate:del']" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudSysCompanyPlate from '@/api/business/sysCompanyPlate'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, wxDepartmentId: null, dictId: null, dictName: null, createBy: null, createDate: null, updateBy: null, updateDate: null, delFlag: null, spare1: null, spare2: null, spare3: null, spare4: null, spare5: null }
export default {
  name: 'SysCompanyPlate',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'sys_company_plate', url: 'api/sysCompanyPlate', idField: 'id', sort: 'id,desc', crudMethod: { ...crudSysCompanyPlate }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'sysCompanyPlate:add'],
        edit: ['admin', 'sysCompanyPlate:edit'],
        del: ['admin', 'sysCompanyPlate:del']
      },
      rules: {
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
