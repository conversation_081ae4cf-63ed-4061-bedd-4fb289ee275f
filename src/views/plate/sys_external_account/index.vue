<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">公司名</label>
        <el-input v-model="query.companyName" clearable placeholder="公司名" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">开户行</label>
        <el-input v-model="query.bankName" clearable placeholder="开户行" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">账号</label>
        <el-input v-model="query.bankAccount" clearable placeholder="账号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="公司名" prop="companyName">
            <el-input v-model="form.companyName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开户行" prop="bankName">
            <el-input v-model="form.bankName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="账号" prop="bankAccount">
            <el-input v-model="form.bankAccount" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="companyName" label="公司名" />
        <el-table-column prop="bankName" label="开户行" />
        <el-table-column prop="bankAccount" label="账号" />
        <el-table-column v-permission="['admin','sysExternalAccount:edit','sysExternalAccount:del']" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudSysExternalAccount from '@/api/business/sysExternalAccount'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, companyName: null, bankName: null, bankAccount: null, createBy: null, createDate: null, updateBy: null, updateDate: null, delFlag: null, spare1: null, spare2: null, spare3: null, spare4: null, spare5: null }
export default {
  name: 'SysExternalAccount',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '外部公司', url: 'api/sysExternalAccount', idField: 'id', sort: 'id,desc', crudMethod: { ...crudSysExternalAccount }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'sysExternalAccount:add'],
        edit: ['admin', 'sysExternalAccount:edit'],
        del: ['admin', 'sysExternalAccount:del']
      },
      rules: {
        companyName: [
          { required: true, message: '公司名不能为空', trigger: 'blur' }
        ],
        bankName: [
          { required: true, message: '开户行不能为空', trigger: 'blur' }
        ],
        bankAccount: [
          { required: true, message: '账号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'companyName', display_name: '公司名' },
        { key: 'bankName', display_name: '开户行' },
        { key: 'bankAccount', display_name: '账号' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
