<template>
  <div class="app-container">
    <el-button @click="pallPathByFunds">批量更新菜单路径</el-button>
    <!--工具栏 default-expand-all-->
    <div class="head-container">
      <el-tree
        :data="treeData"
        :expand-on-click-node="false"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <el-popover
            placement="right"
            trigger="click"
            :content="'id:'+data.id+'，释义：'+(data.explanation||'-')+'，现金：'+(data.flowName||'-')"
          >
            <span slot="reference"
              >{{ node.label
              }}
              <span v-if="data.isHidden==1">(隐藏)</span>
              <span v-if="data.code">{{ data.code }}</span>
              <span v-if="data.remark">[{{ data.remark }}]</span>
              <span v-if="data.explanation" style="font-size:12px">{{data.explanation.substr(0,3)}}</span>
              <span v-if="data.flowName" style="font-weight: bold;color: cadetblue;">{{ data.flowName.substr(0,3) }}</span>
            </span>
          </el-popover>
          <span @click="toggoleIsSel(data)">
            <i
              class="el-icon-turn-off"
              v-show="data.isSel != 1"
              style="color:cadetblue;"
            ></i>
            <span
              v-show="data.isSel != 1"
              style="font-size:12px;color:cadetblue;"
              >不能选</span
            >
            <i
              class="el-icon-open"
              v-show="data.isSel == 1"
              style="color:cornflowerblue;"
            ></i>
            <span
              v-show="data.isSel == 1"
              style="font-size:12px;color:cornflowerblue;"
              >可选</span
            >
          </span>
          &nbsp;&nbsp;
          <span>
            <el-button type="text" size="mini" @click="() => append(data)">
              添加下级
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="() => upedit(node, data)"
            >
              修改
            </el-button>
            &nbsp;&nbsp;
            <span v-for="fitem in data.plateList" :key="fitem.id">
              <el-tag closable @close="() => removePlate(data.id,fitem.id)" >{{ fitem.name.replace('板块','') }}</el-tag>
            </span>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <el-popover
              placement="right"
              trigger="click"
              >
              <el-select v-model="plateId">
                <el-option v-for="fitem in plateData" :key="fitem.value" :label="fitem.label" :value="fitem.value"></el-option>
              </el-select>
              <el-button :loading="loadPlateBtn" type="success" round @click="addPlate(data.id,plateId,data.sortby)">确定</el-button>
              <el-button size="mini" type="info" plain slot="reference">增加板块</el-button>
            </el-popover>
            <el-popover
              placement="right"
              trigger="click"
              >
              <el-select v-model="deptCode">
                <el-option v-for="fitem in deptCodeList" :key="fitem.value" :label="fitem.label" :value="fitem.value"></el-option>
              </el-select>
              <el-button :loading="loadPlateBtn" type="success" round @click="addDeptCode(data.id,data.code,deptCode)">确定</el-button>
              <el-button size="mini" type="info" plain slot="reference">增加部门</el-button>
            </el-popover>

          </span>
        </span>
      </el-tree>
      <!--表格渲染-->
      <el-dialog title="编辑" :visible.sync="diaAdd" width="30%">
        <el-input v-model="fundsDetail.name" placeholder="名称"></el-input>
        <el-input v-model="fundsDetail.sortby" placeholder="排序"></el-input>
        <el-input v-model="fundsDetail.code" placeholder="编码"></el-input>
        <el-input v-model="fundsDetail.model" placeholder="类型"></el-input>
        <el-input v-model="fundsDetail.remark" placeholder="备注"></el-input>
        <el-input
          v-model="fundsDetail.flowId"
          placeholder="流量表id"
        ></el-input>
        <el-input
          v-model="fundsDetail.explanation"
          placeholder="注释说明"
        ></el-input>
        <el-input
          v-model="fundsDetail.parentId"
          placeholder="父级id"
        ></el-input>
        <span slot="footer" class="dialog-footer">
          <el-button @click="diaAdd = false">取 消</el-button>
          <el-button type="primary" @click="saveFd()">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { treeList, edit, add, addPlate,removePlate } from "@/api/system/fundsClassification";
import {list as plateList} from '@/api/system/dict'

export default {
  name: "FundsClassification",
  data() {
    return {
      treeData: [],
      plateData: [],
      loadPlateBtn:false,
      diaAdd: false,
      plateId: '',
      deptCode: '',
      deptCodeList: [{
        value: 'LSHYB',
        label: '航运部'
      },
      {
        value: 'LSHWB',
        label: '海务部'
      },
      {
        value: 'LSJWB',
        label: '机务部'
      },
      {
        value: 'LSZHB',
        label: '综合部'
      },
      {
        value: 'LSCWB',
        label: '财务部'
      }
      ],
      fundsDetail: {
        id: "",
        name: "",
        parentId: "",
        model: "",
        sortby: "",
        code: "",
        remark: "",
        flowId: "",
        explanation: ""
      }
    };
  },
  created() {
    this.loadData();
    this.loadPlateData();
  },
  methods: {
    removePlate(id, plateId) {
      console.log('removePlate',id, plateId)
      // 关联板块
      const rd = {
        fundsClassId: id,
        plateId: plateId
      };
      removePlate(rd).then(res => {
        this.loadData();
      });
    },
    addPlate(id, plateId, sortby) {
      // console.log('addPlate', id, plateId, sortby)
      this.loadPlateBtn=true
      // 关联板块
      const rd = {
        fundsClassId: id,
        plateId: plateId,
        sortby:sortby
      };
      addPlate(rd).then(res => {
        this.loadData();
      }).finally(()=>{
        this.loadPlateBtn=false
      });
    },
    addDeptCode(id,pcode, code) {
      this.loadPlateBtn=true
      // 关联板块
      if (pcode) {
        code = pcode + ',' + code
      }
      const rd = {
        id: id,
        code: code
      };
      edit(rd).then(res => {
        // this.loadData();
      }).finally(()=>{
        this.loadPlateBtn=false
      });
    },
    saveFd() {
      if (this.fundsDetail.id) {
        edit(this.fundsDetail).then(res => {
          // this.loadData();
        });
      } else {
        add(this.fundsDetail).then(res => {
          this.loadData();
        });
      }
      this.diaAdd = false;
    },
    loadPlateData() {
      plateList('plate').then(res => {
        if (res.resultCode == "0" && res.list) {
          this.plateData = res.list;
        }
      })
    },
    loadData() {
      treeList().then(res => {
        if (res.resultCode == "0" && res.data) {
          this.treeData = res.data;
        }
      });
    },
    initDetail() {
      this.fundsDetail = {
        id: "",
        name: "",
        parentId: "",
        model: "",
        sortby: "",
        code: "",
        remark: "",
        flowId: "",
        explanation: ""
      };
    },
    append(data) {
      this.initDetail();
      console.log("添加", data);
      this.fundsDetail.parentId = data.id;
      this.fundsDetail.model = data.model;
      this.fundsDetail.sortby = data.sortby;
      this.diaAdd = true;
      // const rd = {
      //   name: "测试",
      //   parentId: data.id,
      //   model: data.model,
      //   sortby: data.sortby
      // };
      // add(rd).then(res => {
      //   if (res.resultCode == "0") {
      //     this.loadData();
      //   }
      // });
    },
    pallPathByFunds() {
      // 批量修改
      const load = this.$loading({
        lock: true,
        text: "正在处理中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      const data = this.treeData
      data.forEach(item => {
        this.treeDataUpdateBatch(item)
      })
      load.close();
    },
    treeDataUpdateBatch(treeData) {
      const fd = {
        id: treeData.id,
        sortby: treeData.sortby,
        parentId: treeData.parentId,
        model: treeData.model,
        name: treeData.label,
        code: treeData.code,
        remark: treeData.remark,
        flowId: treeData.flowId,
        explanation: treeData.explanation,
        param1: treeData.isHidden
      }
      edit(fd).then(res => {
        treeData.children.forEach(item => {
          this.treeDataUpdateBatch(item)
        })
      });
    },
    upedit(n, data) {
      console.log("修改", n, data);
      this.initDetail();
      this.fundsDetail.id = data.id;
      this.fundsDetail.model = data.model;
      this.fundsDetail.sortby = data.sortby;
      this.fundsDetail.name = data.label;
      this.fundsDetail.parentId = data.parentId;
      this.fundsDetail.explanation = data.explanation;
      this.fundsDetail.code = data.code;
      this.fundsDetail.remark = data.remark;
      this.fundsDetail.flowId = data.flowId;
      this.fundsDetail.param1 = data.isHidden;

      this.diaAdd = true;
    },
    toggoleIsSel(data) {
      console.log("toggoleIsSel", data);
      const rd = {
        id: data.id,
        isSel: data.isSel == 1 ? 0 : 1
      };
      edit(rd).then(res => {
        this.loadData();
      });
    }
  }
};
</script>

<style scoped>
.head-container{
  overflow:auto;
  width: 100%;
}
.custom-tree-node{
}
</style>
