<template>
  <div class="app-container">

    <!--工具栏-->
    <div class="head-container">
      <div>
        <el-input v-model="query.likename" clearable size="small" placeholder="输入客户名称搜索" style="width: 200px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px" @open="openDialog">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="85px">
          <el-form-item label="客户名称" prop="name">
            <!-- <el-input v-model="form.name" style="width: 370px;" /> -->
            <el-autocomplete
              v-model="form.name"
              style="width: 370px;"
              class="inline-input"
              :fetch-suggestions="querySearch"
              placeholder="请输入内容"
              :trigger-on-focus="false"
              @input="handleChange"
              @select="handleSelect"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.name }}</div>
              </template>
            </el-autocomplete>
          </el-form-item>
          <el-form-item label="简称" prop="shortName">
            <el-input v-model="form.shortName" :disabled="form.disableSel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="合同code" prop="spare5">
            <el-input v-model="form.spare5" :disabled="form.disableSel" style="width: 370px;" placeholder="请输入合同code（大写字母）" />
          </el-form-item>
          <el-form-item label="合同名头" prop="contractHeader">
            <el-input v-model="form.contractHeader" :disabled="form.disableSel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="纳税识别号" prop="taxIdNumber">
            <el-input v-model="form.taxIdNumber" :disabled="form.disableSel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="注册地址" prop="registeredAddress">
            <el-input v-model="form.registeredAddress" :disabled="form.disableSel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="电话" prop="spare1">
            <el-input v-model="form.spare1" :disabled="form.disableSel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开户行" prop="spare2">
            <el-input v-model="form.spare2" :disabled="form.disableSel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="银行账号" prop="spare3">
            <el-input v-model="form.spare3" :disabled="form.disableSel" style="width: 370px;" />
          </el-form-item>

          <el-form-item label="母公司" prop="parendId">
            <el-select v-model="form.parendId" :disabled="form.disableSel" clearable filterable placeholder="请选择">
              <el-option
                v-for="item in customerList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column label="客户名称">
          <template slot-scope="scope">
            <el-link @click="$router.push('/sysExternalCustomer/sysExternalCustomerDetailCurrent?id='+scope.row.id)">{{ scope.row.name }}</el-link>
          </template>

        </el-table-column>
        <el-table-column prop="shortName" label="简称" />
        <el-table-column label="业务类型">
          <template slot-scope="scope">
            <template v-for="(item,index) in scope.row.dictr.split(',')">
              <el-tag v-if="item.split('|')[0]!=0" :key="index" class="tagcls" @click="showCustomerSupplierByDict(scope.row.id,item,scope.row.dictr)">
                {{ scope.row.dictr.split(',').length>1? item.split('|')[1] :'' }}
                {{ labelCustomerSupplier(item.split('|')[2],item.split('|')[3],scope.row.dictr.split(',').length>1) }}</el-tag>
            </template>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="contractHeader" label="合同名头" /> -->
        <!-- <el-table-column prop="taxIdNumber" label="纳税识别号" /> -->
        <el-table-column prop="registeredAddress" label="注册地址" />
        <!-- <el-table-column prop="parendId" label="母公司id" />
        <el-table-column prop="createBy" label="createBy" />
        <el-table-column prop="createDate" label="createDate">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateBy" label="updateBy" />
        <el-table-column prop="updateDate" label="updateDate">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="delFlag" label="delFlag" />
        <el-table-column prop="spare1" label="spare1" />
        <el-table-column prop="spare2" label="spare2" />
        <el-table-column prop="spare3" label="spare3" />
        <el-table-column prop="spare4" label="spare4" />
        <el-table-column prop="spare5" label="spare5" /> -->
        <el-table-column v-permission="['admin','sysExternalCustomer:edit','sysExternalCustomer:del']" label="操作" width="220px" align="center">
          <template slot-scope="scope">
            <el-row justify="center" align="middle">
              <el-col :span="12"><el-button @click="showOrgTree(scope.row.id)">公司架构</el-button>
              </el-col>
              <el-col :span="12">
                <el-button size="mini" type="primary" icon="el-icon-edit" @click="$router.push('/sysExternalCustomer/sysExternalCustomerDetailCurrent?id='+scope.row.id)" />
                <!-- <udOperation
                :data="scope.row"
                :permission="permission"
              /> -->
              </el-col>
            </el-row>

          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>

    <el-drawer
      direction="rtl"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <myOrgTree ref="myOrgTreeRef" :data="dialogComponentProps" :selected-key="selKey" @nodeClick="nodeClick" />
    </el-drawer>
    <el-dialog
      :title="dialogCustSuppData.isShowName?dialogCustSuppData.dictName:dialogCustSuppData.title"
      :visible.sync="dialogVisibleCustSupp"
      width="30%"
    >
      <el-switch
        v-model="dialogCustSuppData.isCustomer"
        active-value="1"
        inactive-value="0"
        active-text="客户"
      />
      <el-switch
        v-model="dialogCustSuppData.isSupplier"
        active-value="1"
        inactive-value="0"
        active-text="供应商"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCustSupp = false">取 消</el-button>
        <el-button type="primary" :loading="dialogCustSuppDataLoading" @click="subBtnCustSup">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import crudSysExternalCustomer from '@/api/system/sysExternalCustomer'
import { createAndPlate } from '@/api/system/sysExternalCustomer'

import { saveAndUpdateByCreateBy as addPlate } from '@/api/system/sysExternalCustomerPlate'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import myOrgTree from './orgTree.vue'

const defaultForm = { id: null, name: null, shortName: null, contractHeader: null, taxIdNumber: null, registeredAddress: null, parendId: null, spare5: null, spare1: null, spare2: null, spare3: null, disableSel: false }
export default {
  name: 'SysExternalCustomer',
  components: { pagination, crudOperation, rrOperation, udOperation, myOrgTree },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '客户', url: 'api/sysExternalCustomer/listByCurrentUser', idField: 'id', sort: 'id,desc', crudMethod: { ...crudSysExternalCustomer, add: createAndPlate }})
  },
  data() {
    const checkCode = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      if (!this.form.name) {
        callback(new Error('请输入客户名称'))
        return
      }
      // 根据code查询是否存在
      crudSysExternalCustomer.list({ spare5: value }).then(res => {
        console.log(res, 'code', this.form)
        if (res && res.content && res.content.length > 0) {
          // 判断名称是否一致
          let boo = true
          res.content.every(item => {
            if (item.name != this.form.name) {
              if (this.form.id && item.id == this.form.id) {
                boo = true
                return false
              }
              boo = false
              return false
            }
            if (this.form.id && item.id == this.form.id) {
              boo = true
              return false
            }
            return true
          })
          if (!boo) {
            callback(new Error('合同code已存在，请重新输入'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }).catch(() => {
        callback(new Error('合同code查询失败,请稍后再试'))
      })
    }
    return {

      dialogVisible: false,
      dialogVisibleCustSupp: false,
      dialogComponentProps: {},
      dialogCustSuppData: {
        title: '修改业务类型',
        customerId: '',
        dictId: '',
        dictName: '',
        isShowName: false,
        isCustomer: 0,
        isSupplier: 0
      },
      dialogCustSuppDataLoading: false,
      selKey: 'selected',
      permission: {
        add: ['admin', 'sysExternalCustomer:add'],
        edit: ['admin', 'sysExternalCustomer:edit'],
        del: ['admin', 'sysExternalCustomer:del']
      },
      rules: {
        name: [{ required: true, trigger: 'blur', message: '客户名称不能为空' }
        ],
        spare5: [{ trigger: 'blur', pattern: /^[A-Z]{1,6}$/, message: '合同code不能为空，请输入最多6位大写字母' },
          { validator: checkCode, trigger: 'blur' }]
      },
      customerList: []
    }
  },
  created() {
    // this.init()
    console.log('cerate')
  },
  mounted() {
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      // this.form = { ...defaultForm }
      return true
    },
    // 提交前的验证
    // async checkNameCode() {
    //   if (!this.form.name) {
    //     this.$message.error('请输入客户名称')
    //     return false
    //   }
    //   if (!this.form.spare5) {
    //     this.$message.error('请输入合同code')
    //     return false
    //   }
    //   try {
    //     const res = await crudSysExternalCustomer.list({ spare5: this.form.spare5 })
    //     if (res && res.content && res.content.length > 0) {
    //     // 判断名称是否一致
    //       let boo = true
    //       res.content.every(item => {
    //         if (item.name != this.form.name) {
    //           boo = false
    //           return false
    //         }
    //         return true
    //       })
    //       if (!boo) {
    //         this.$message.error('合同code已存在，请重新输入')
    //         return false
    //       }
    //     }
    //   } catch (e) {
    //     this.$message.error('合同code查询失败,请稍后再试')
    //     return false
    //   }

    //   return false
    // },
    querySearch(queryStr, cb) {
      if (queryStr) {
        crudSysExternalCustomer.customerListAll(queryStr).then(res => {
          cb(res)
        })
      } else {
        cb([])
      }
    },
    handleChange() {
      this.form.disableSel = false
    },
    handleSelect(item) {
      // this.form = JSON.parse(JSON.stringify(item))
      // this.form.id = ''
      // this.form = { ...item }
      this.form.disableSel = true
      this.form.name = item.name
      this.form.shortName = item.shortName
      this.form.contractHeader = item.contractHeader
      this.form.taxIdNumber = item.taxIdNumber
      this.form.registeredAddress = item.registeredAddress
      this.form.parendId = item.parendId
      this.form.spare5 = item.spare5
      this.form.spare1 = item.spare1
      this.form.spare2 = item.spare2
      this.form.spare3 = item.spare3
      this.form.spare4 = item.spare4
    },
    subBtnCustSup() {
      this.dialogCustSuppDataLoading = true
      const params = {
        customerId: this.dialogCustSuppData.customerId,
        dictId: this.dialogCustSuppData.dictId,
        isCustomer: this.dialogCustSuppData.isCustomer,
        isSupplier: this.dialogCustSuppData.isSupplier
      }
      addPlate(params).then(res => {
        this.dialogVisibleCustSupp = false
        this.dialogCustSuppDataLoading = false
        this.crud.refresh()
      }).catch(() => {
        this.dialogCustSuppDataLoading = false
      })
    },
    showCustomerSupplierByDict(customerId, dictItem, dicts) {
      // 显示客户供应商
      this.dialogCustSuppData.customerId = customerId
      this.dialogCustSuppData.dictId = dictItem.split('|')[0]
      this.dialogCustSuppData.dictName = dictItem.split('|')[1]
      this.dialogCustSuppData.isShowName = dicts.split(',').length > 1
      this.dialogCustSuppData.isCustomer = dictItem.split('|')[2]
      this.dialogCustSuppData.isSupplier = dictItem.split('|')[3]
      this.dialogVisibleCustSupp = true
    },
    labelCustomerSupplier(isCustomer, isSupplier, isBoth = true) {
      // console.log('cust and supp', isCustomer, isSupplier)
      let label = '暂无类型'
      if ((isCustomer && isCustomer != 0) || (isSupplier && isSupplier != 0)) {
        if (isBoth) {
          label = '('
        } else {
          label = ''
        }
        if (isCustomer && isCustomer != 0) {
          label += '客户'
          if (isSupplier && isSupplier != 0) {
            label += '、'
          }
        }
        if (isSupplier && isSupplier != 0) {
          label += '供应商'
        }
        if (isBoth) {
          label += ')'
        }
        return label
      }
      return label
    },
    handleClose() {
      console.log('dialog close')
      this.dialogVisible = false
    },
    showOrgTree(id) {
      this.dialogVisible = true
      this.dialogComponentProps = {
        id: id
      }
      // console.log(this.$refs, this.$refs.myOrgTreeRef)
      if (this.$refs.myOrgTreeRef) {
        console.log(this.$refs, this.$refs.myOrgTreeRef)
        this.$refs.myOrgTreeRef.loadData(this.dialogComponentProps.id)
      }
    },
    loadList() {
      this.customerList = []
      crudSysExternalCustomer.listAll().then(res => {
        if (res && res.length > 0) {
          this.customerList = [{
            id: 0,
            name: '请选择'
          }].concat(this.form.id ? res.filter(item => item.id != this.form.id) : res)
        }
      })
    },
    openDialog() {
      this.loadList()
    },
    nodeClick(id) {
      if (id) {
        this.$router.push('/sysExternalCustomer/sysExternalCustomerDetailCurrent?id=' + id)
      }
    }
  }
}
</script>

<style scoped>
.tagcls{
  margin: 3px;
  cursor:pointer;
}
</style>
