<template>
  <div
    style="display: flex; justify-content:center;"
  >
    <el-empty v-show="loading || Object.keys(ds).length==0" :description="loading?'加载中..':'暂无数据'" />
    <Vue2OrgTree v-show="!loading && Object.keys(ds).length>0" :data="ds" :selected-key="selectedKey" :selected-class-name="selectedClassName" @on-node-click="NodeClick" />
  </div>

</template>

<script>
import Vue2OrgTree from 'vue2-org-tree'
import 'vue2-org-tree/dist/style.css'
import { queryTree } from '@/api/system/sysExternalCustomer'
export default {
  components: { Vue2OrgTree },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    selectedKey: {
      type: String,
      default: 'selected'
    },
    selectedClassName: {
      type: String,
      default: '_orgtree_bgred'
    }
  },
  data() {
    return {
      ds: {},
      loading: false
    }
  },
  created() {
    if (this.data && this.data.id) {
      this.loadData(this.data.id)
    }
  },
  mounted() {
    console.log('mounted tree')
  },
  methods: {
    NodeClick(e, data) {
      this.$emit('nodeClick', data.id || '')
    },
    loadData(id) {
      if (id) {
        this.loading = true
        queryTree(id).then(res => {
          this.ds = res
        }).finally(() => {
          this.loading = false
        })
      }
    }
  }

}
</script>

<style>
._orgtree_bgred{
  background-color: #1890ff;
  color: white;
}
</style>
