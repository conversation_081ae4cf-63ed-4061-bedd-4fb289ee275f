<template>
  <div class="app-container">

    <!--工具栏-->
    <div class="head-container">
      <!-- <div class="flexcenter"><h3>客户：{{ data.name }}</h3></div> -->
      <el-card class="box-card">
        <el-descriptions title="详细信息" :colon="true" size="medium" label-class-name="infoItemCls">
          <template slot="extra">
            <el-button class="ml3" @click="showInfoEdit(data)">编辑</el-button>
          </template>
          <el-descriptions-item label="名称">{{ data.name||'--' }}</el-descriptions-item>
          <el-descriptions-item label="简称">{{ data.shortName||'--' }}</el-descriptions-item>
          <el-descriptions-item label="合同名头">{{ data.contractHeader||'--' }}</el-descriptions-item>
          <el-descriptions-item label="合同code">{{ data.spare5||'--' }}</el-descriptions-item>
          <el-descriptions-item label="纳税识别号">{{ data.taxIdNumber||'--' }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ data.registeredAddress||'--' }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ data.spare1||'--' }}</el-descriptions-item>
          <el-descriptions-item label="开户行">{{ data.spare2||'--' }}</el-descriptions-item>
          <el-descriptions-item label="银行账号">{{ data.spare3||'--' }}</el-descriptions-item>
          <el-descriptions-item label="母公司">{{ parentName }}</el-descriptions-item>
          <el-descriptions-item label="业务关联"> <el-tag
                                                v-for="(item,index) in customerData"
                                                :key="index"
                                                closable
                                                class="tagcls"
                                                style="margin: 0 3px;"
                                                @close="handleClose(item)"
                                                @click="showCustomerSupplierByDict(item)"
                                              >
                                                {{ item.dictName }} {{ labelCustomerSupplier(item.isCustomer,item.isSupplier,customerData.length>1) }}<!--
                                              --></el-tag>
            <el-tag type="success" class="tagcls" style="margin: 0 3px;" @click="showCustomerSupplierByDict({})">新增业务</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      <!-- <div> -->
      <!-- <h5 />
        <el-row :gutter="10" justify="space-between" align="middle">
          <el-col :span="12"><h5>详细信息</h5></el-col>
          <el-col :span="12" style="text-align:right;"><el-button class="ml3" @click="showInfoEdit(data)">编辑</el-button></el-col>
        </el-row>
        <template v-if="'测试66'==data.name && false">
          客户名称：{{ customerName }} 客户id {{ customerId }} 开户行 {{ bankNameVal }} 卡号{{ bankAccountVal }} 联系人id:{{ contactId }} 联系人信息:{{ contactSelData }} -->
      <!-- is-customer="1" -->
      <!-- is-supplier="1" -->
      <!-- dict-id="7" -->
      <!-- dept-id="41" -->
      <!-- <CustomerAll
            dict-id="7"
            is-customer="1"
            @customerIdChange="d=>customerId=d"
            @customerNameChange="v=>customerName=v"
            @bankNameChange="v=>bankNameVal=v"
            @bankAccountChange="v=>bankAccountVal=v"
            @contactIdChange="v=>contactId=v"
            @contactDataChange="d=>contactSelData=d"
          />
        </template> -->

      <!-- <el-row :gutter="10">
            <el-col :span="12" class="sitpan">名称：{{ data.name||'--' }}</el-col>
            <el-col :span="12" class="sitpan">简称：{{ data.shortName||'--' }}</el-col>
            <el-col :span="12" class="sitpan">合同名头：{{ data.contractHeader||'--' }}</el-col>
            <el-col :span="12" class="sitpan">纳税识别号：{{ data.taxIdNumber||'--' }}</el-col>
            <el-col :span="12" class="sitpan">地址：{{ data.registeredAddress||'--' }}</el-col>
            <el-col v-show="data.parendId" :span="12" class="sitpan">母公司：{{ parentName }}</el-col>
            <el-col :span="24" class="sitpan">业务关联：
              <el-tag
                v-for="(item,index) in customerData"
                :key="index"
                closable
                class="tagcls"
                @close="handleClose(item)"
                @click="showCustomerSupplierByDict(item)"
              >
                {{ customerData.length>1? item.dictName:'' }} {{ labelCustomerSupplier(item.isCustomer,item.isSupplier,customerData.length>1) }}
              </el-tag>
              <el-button size="mini" @click="showCustomerSupplierByDict({})">新增业务</el-button>
            </el-col>
          </el-row> -->
      <!-- </div> -->
      <el-card class="box-card">
        <div slot="header" class="centerflex">
          <span class="headTitleCls">联系人</span>
          <el-button class="ml3" @click="showContactEdit({})">新增联系人</el-button>
          <!-- <el-row slot="header" :gutter="10" justify="space-between" align="middle" style="align-items:center;">
          <el-col :span="12"><span>联系人</span></el-col>
          <el-col :span="12" style="text-align:right;"><el-button class="ml3" @click="showContactEdit({})">新增联系人</el-button></el-col>
        </el-row> -->
        </div>
        <div>
          <el-table :data="contactDataList" stripe>
            <el-table-column
              prop="department"
              label="部门"
            />
            <el-table-column
              prop="jobTitle"
              label="职务"
            />
            <el-table-column
              prop="contactName"
              label="联系人"
            />
            <el-table-column
              prop="contactMobile"
              label="联系方式"
            />
            <el-table-column
              prop="faxNumber"
              label="传真号"
            />
            <el-table-column
              prop="email"
              label="邮箱"
            />
            <el-table-column
              prop="wxName"
              label="微信名"
            />
            <el-table-column
              prop="wxNumber"
              label="微信号"
            />
            <el-table-column
              prop="addressRecipient"
              label="收件人"
            />
            <el-table-column
              prop="address"
              label="地址"
            />
            <el-table-column
              label="业务"
            >
              <template slot-scope="scope">
                <el-tag
                  v-for="(item,index) in contactLabel(scope.row.id)"
                  :key="index"
                  closable
                  class="tagcls"
                  @close="handleContactClose(item)"
                  @click="showContactByDict(item,contactLabel(scope.row.id))"
                >
                  {{ item.dictName }} {{ labelCustomerSupplier(item.isCustomer,item.isSupplier,contactLabel(scope.row.id).length>1) }}<!--
                   --></el-tag>
                <el-tag class="tagcls" type="success" @click="showContactByDict({contactId:scope.row.id},contactLabel(scope.row.id))">新增业务</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              width="180"
              label="操作"
            >
              <template slot-scope="scope">
                <el-row justify="center" align="middle">
                  <el-col :span="8">&nbsp;</el-col>
                  <el-col :span="8"><el-button @click="showContactEdit(scope.row)">编辑</el-button>
                  </el-col>
                  <el-col :span="8">&nbsp;</el-col>
                </el-row>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <!-- <el-row :gutter="10" justify="space-between" align="middle">
          <el-col :span="12"><h5>联系人</h5></el-col>
          <el-col :span="12" style="text-align:right;"><el-button class="ml3" @click="showContactEdit({})">新增联系人</el-button></el-col>
        </el-row>
        <hr style="opacity:0.5;">
        <el-row :gutter="12">
          <el-col v-for="(item,index) in contactDataList" :key="index" class="sitpan" :span="8">
            <el-card class="cardContact" style="border-radius: 30px" shadow="hover">
              <el-row :gutter="10">
                <el-link type="info" class="editTop" @click="showContactEdit(item)">编辑</el-link>
                <el-col class="sitpan" :span="12">部门： {{ item.department ||'--' }}</el-col>
                <el-col class="sitpan" :span="12">职务： {{ item.jobTitle ||'--' }}</el-col>
                <el-col class="sitpan" :span="12">联系人： {{ item.contactName ||'--' }}</el-col>
                <el-col class="sitpan" :span="12">业务：

                  <el-tag
                    v-for="(pitem,pindex) in contactLabel(item.id)"
                    :key="pindex"
                    closable
                    type="success"
                    class="tagcls"
                    @close="handleContactClose(pitem)"
                    @click="showContactByDict(pitem,contactLabel(item.id))"
                  >
                    {{ pitem.dictName }} {{ labelCustomerSupplier(pitem.isCustomer,pitem.isSupplier,contactLabel(item.id).length>1) }}
                  </el-tag>
                  <el-button size="mini" @click="showContactByDict({contactId:item.id},contactLabel(item.id))">新增</el-button>
                </el-col>
                <el-col class="sitpan" :span="24">联系方式： {{ item.contactMobile ||'--' }}</el-col>
              </el-row>
            </el-card>
          </el-col>

        </el-row> -->
      <el-card class="box-card">
        <div slot="header" class="centerflex">
          <span class="headTitleCls">账户列表</span>
          <el-button class="ml3" @click="addAccountClk()">新增账户</el-button>
        </div>

        <div>
          <!-- <el-row :gutter="10" justify="space-between" align="middle">
          <el-col :span="12"><h5>账户列表</h5></el-col>
          <el-col :span="12" style="text-align:right;"><el-button class="ml3" @click="addAccountClk">新增账户</el-button></el-col>
        </el-row> -->
          <el-table :data="accountList">
            <el-table-column
              prop="spare2"
              label="户名"
            />

            <el-table-column
              prop="bankName"
              label="开户行"
            />
            <el-table-column
              prop="bankAccount"
              label="卡号"
            />
            <el-table-column
              prop="spare3"
              label="省份"
              width="50"
            />
            <el-table-column
              prop="spare4"
              label="市"
              width="50"
            />
            <el-table-column
              label="业务"
            >
              <template slot-scope="scope">
                <el-tag
                  v-for="(aitem,aindex) in accountLabel(scope.row.id)"
                  :key="aindex"
                  closable
                  class="tagcls"
                  @close="accountClose(aitem)"
                  @click="showAccountPlate(aitem,accountLabel(scope.row.id))"
                >
                  {{ aitem.dictName }} {{ labelCustomerSupplier(aitem.isCustomer,aitem.isSupplier,accountLabel(scope.row.id).length>1) }}<!--
                 --></el-tag>
                <el-tag type="success" class="tagcls" @click="showAccountPlate({accountId:scope.row.id},accountLabel(scope.row.id))">新增</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              width="180"
              label="操作"
            >
              <template slot-scope="scope">
                <el-row justify="center" align="middle">
                  <el-col :span="8">&nbsp;</el-col>
                  <el-col :span="8"><el-button @click="upAccountOpen(scope.row)">编辑</el-button>
                  </el-col>
                  <el-col :span="8">&nbsp;</el-col>
                </el-row>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    <el-dialog
      title="编辑联系人"
      :visible.sync="dialogVisible"
      width="580px"
    >
      <div>
        <el-form ref="contactFrom" inline label-position="right" label-width="80px" :rules="rulesContact" :model="formLabelAlign">
          <el-form-item label="联系人" prop="contactName">
            <el-input v-model="formLabelAlign.contactName" />
          </el-form-item>
          <el-form-item label="联系方式" prop="contactMobile">
            <el-input v-model.number="formLabelAlign.contactMobile" />
          </el-form-item>
          <el-form-item label="部门" prop="department">
            <el-input v-model="formLabelAlign.department" />
          </el-form-item>
          <el-form-item label="职务" prop="jobTitle">
            <el-input v-model="formLabelAlign.jobTitle" />
          </el-form-item>
          <el-form-item label="传真号" prop="faxNumber">
            <el-input v-model="formLabelAlign.faxNumber" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formLabelAlign.email" />
          </el-form-item>
          <el-form-item label="微信名" prop="wxName">
            <el-input v-model="formLabelAlign.wxName" />
          </el-form-item>
          <el-form-item label="微信号" prop="wxNumber">
            <el-input v-model="formLabelAlign.wxNumber" />
          </el-form-item>
          <el-form-item label="收件人" prop="address">
            <el-input v-model="formLabelAlign.address" />
          </el-form-item>
          <el-form-item label="收件地址" prop="addressRecipient">
            <el-input v-model="formLabelAlign.addressRecipient" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="contAddLoading" @click="addContact">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="编辑账户"
      :visible.sync="dialogAccount"
      width="430px"
    >
      <div>
        <el-form ref="accountForm" label-position="right" label-width="80px" :rules="rulesAccount" :model="formAccount">
          <el-form-item label="户名" prop="spare2">
            <el-input v-model="formAccount.spare2" />
          </el-form-item>
          <el-form-item label="开户行" prop="bankName">
            <el-input v-model="formAccount.bankName" />
          </el-form-item>
          <el-form-item label="卡号" prop="bankAccount">
            <el-input v-model="formAccount.bankAccount" />
          </el-form-item>
          <el-form-item label="省" prop="spare3">
            <el-input v-model="formAccount.spare3" />
          </el-form-item>
          <el-form-item label="市" prop="spare4">
            <el-input v-model="formAccount.spare4" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="dialogAccount = false">取 消</el-button>
        <el-button type="primary" :loading="accountLoading" @click="subAccount">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" :visible.sync="infoVisible" title="编辑客户信息" width="530px" @open="openDialog">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px">
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="简称" prop="shortName">
          <el-input v-model="form.shortName" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="合同code" prop="spare5">
          <el-input v-model="form.spare5" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="合同名头" prop="contractHeader">
          <el-input v-model="form.contractHeader" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="纳税识别号" prop="taxIdNumber">
          <el-input v-model="form.taxIdNumber" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="注册地址" prop="registeredAddress">
          <el-input v-model="form.registeredAddress" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="电话" prop="spare1">
          <el-input v-model="form.spare1" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="开户行" prop="spare2">
          <el-input v-model="form.spare2" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="银行账号" prop="spare3">
          <el-input v-model="form.spare3" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="母公司" prop="parendId">
          <el-select v-model="form.parendId" clearable filterable placeholder="请选择">
            <el-option
              v-for="item in customerList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="infoVisible=false">取消</el-button>
        <el-button :loading="loadingInfo" type="primary" @click="updateInfoBtn">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="dialogCustSuppData.isShowName?dialogCustSuppData.dictName:dialogCustSuppData.title"
      :visible.sync="dialogVisibleCustSupp"
      width="30%"
    >
      <el-select v-model="dialogCustSuppData.dictId" placeholder="请选择业务类型">
        <el-option
          v-for="item in dictList"
          :key="item.value"
          :label="item.label"
          :value="item.value+''"
        />
      </el-select>
      <el-switch
        v-model="dialogCustSuppData.isCustomer"
        active-value="1"
        inactive-value="0"
        active-text="客户"
      />
      <el-switch
        v-model="dialogCustSuppData.isSupplier"
        active-value="1"
        inactive-value="0"
        active-text="供应商"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCustSupp = false">取 消</el-button>
        <el-button type="primary" :loading="dialogCustSuppDataLoading" @click="subBtnCustSup">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { detail, addContact, externalAccountList, editContact, contactListAndAllPlateSort,
  queryContactDictByCustomerId, queryAccountDictByCustomerId, queryDictByCustomerId, listAll, edit as editCustomer,
  sysExternalCustomerContactPlateAddAndUpdate, list as customerList,
  sysExternalCustomerContactPlateDel } from '@/api/system/sysExternalCustomer'
import { add as addAccount, edit as editAccount, sysExternalAccountPlateAddAndUpdate, sysExternalAccountPlateDel } from '@/api/business/sysExternalAccount'
// import CustomerAll from '@/components/ExternalCustomerPlate/all'
import { saveAndUpdateByCreateBy, del as delCustomerPlate } from '@/api/system/sysExternalCustomerPlate'
import { list as dictList } from '@/api/system/dict'
export default {
  // components: {
  //   CustomerAll
  // },
  data() {
    const checkCode = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      if (!this.form.id) {
        callback(new Error('客户ID为空'))
        return
      }
      // 根据code查询是否存在
      customerList({ spare5: value }).then(res => {
        console.log(res, 'code')
        if (res && res.content && res.content.length > 0) {
          // 判断名称是否一致
          let boo = true
          res.content.every(item => {
            if (item.id != this.form.id) {
              boo = false
              return false
            }
            return true
          })
          if (!boo) {
            callback(new Error('合同code已存在，请重新输入'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }).catch(() => {
        callback(new Error('合同code查询失败,请稍后再试'))
      })
    }
    return {
      dictList: [],
      contactId: '',
      bankAccountVal: '',
      bankNameVal: '',
      customerId: '',
      customerName: '',
      dialogVisible: false,
      dialogAccount: false,
      infoVisible: false,
      loadingInfo: false,
      data: {},
      customerData: [],
      contactSelData: {},
      contactData: [],
      accountData: [],
      contactDataList: [],
      contAddLoading: false,
      accountLoading: false,
      accountList: [],
      formLabelAlign: {
        contactName: '',
        contactMobile: '',
        department: '',
        jobTitle: '',
        faxNumber: '',
        email: '',
        wxName: '',
        wxNumber: '',
        address: '',
        addressRecipient: ''
      },
      dialogVisibleCustSupp: false,
      dialogCustSuppDataLoading: false,
      dialogCustSuppData: {
        title: '修改业务类型',
        customerId: '',
        dictId: '',
        dictName: '',
        isShowName: false,
        isCustomer: 0,
        isSupplier: 0
      },
      form: {},
      rules: {
        name: [{ required: true, trigger: 'blur', message: '客户名称不能为空' }],
        spare5: [{ trigger: 'blur', pattern: /^[A-Z]{1,6}$/, message: '合同code不能为空，请输入最多6位大写字母' },
          { validator: checkCode, trigger: 'blur' }]
      },
      rulesContact: {
        contactName: [
          { required: true, message: '请输入联系人', trigger: 'blur' }
        ],
        contactMobile: [
          { required: true, message: '请输入联系方式', trigger: 'blur' }
        ]
      },
      rulesAccount: {
        spare2: [
          { required: true, message: '请输入户名', trigger: 'blur' }
        ],
        bankAccount: [
          { required: true, message: '请输入开户行', trigger: 'blur' }
        ],
        bankName: [
          { required: true, message: '请输入卡号', trigger: 'blur' }
        ]
      },
      formAccount: {
        bankAccount: '',
        bankName: '',
        spare2: ''
      },
      customerList: []
    }
  },
  computed: {
    customerLabel() {
      return this.plateToLabel(this.customerData)
    },
    parentName() {
      if (this.data.parendId && this.customerList.length > 0) {
        const parent = this.customerList.find(item => item.id == this.data.parendId)
        if (parent) {
          return parent.name
        }
      }
      return '--'
    }
  },
  created() {
    console.log(this.$route)
    this.loadData(this.$route.query.id)
    this.loadContact(this.$route.query.id)
    this.loadAccount(this.$route.query.id)
    this.loadList()
    this.loadDicts()
  },
  methods: {
    loadDicts() {
      dictList('plate').then(res => {
        this.dictList = res.list
      })
    },
    subBtnCustSup() {
      if (this.dialogCustSuppData.customerId && this.dialogCustSuppData.dictId) {
        this.subCustomerPlae()
        return
      }
      if (this.dialogCustSuppData.contactId && this.dialogCustSuppData.dictId) {
        this.subContactPlate()
        return
      }
      if (this.dialogCustSuppData.accountId && this.dialogCustSuppData.dictId) {
        this.subAccountPlate()
        return
      }
    },
    subAccountPlate() {
      this.dialogCustSuppDataLoading = true
      const params = {
        accountId: this.dialogCustSuppData.accountId,
        dictId: this.dialogCustSuppData.dictId,
        isCustomer: this.dialogCustSuppData.isCustomer,
        isSupplier: this.dialogCustSuppData.isSupplier
      }
      sysExternalAccountPlateAddAndUpdate(params).then(res => {
        this.dialogVisibleCustSupp = false
        this.dialogCustSuppDataLoading = false
        queryAccountDictByCustomerId(this.$route.query.id).then(res => {
          this.accountData = res
        })
      }).catch(() => {
        this.dialogCustSuppDataLoading = false
      })
    },
    subContactPlate() {
      this.dialogCustSuppDataLoading = true
      const params = {
        contactId: this.dialogCustSuppData.contactId,
        dictId: this.dialogCustSuppData.dictId,
        isCustomer: this.dialogCustSuppData.isCustomer,
        isSupplier: this.dialogCustSuppData.isSupplier
      }
      sysExternalCustomerContactPlateAddAndUpdate(params).then(res => {
        this.dialogVisibleCustSupp = false
        this.dialogCustSuppDataLoading = false
        queryContactDictByCustomerId(this.$route.query.id).then(res => {
          this.contactData = res
        })
      }).catch(() => {
        this.dialogCustSuppDataLoading = false
      })
    },
    subCustomerPlae() {
      this.dialogCustSuppDataLoading = true
      const params = {
        customerId: this.dialogCustSuppData.customerId,
        dictId: this.dialogCustSuppData.dictId,
        isCustomer: this.dialogCustSuppData.isCustomer,
        isSupplier: this.dialogCustSuppData.isSupplier
      }
      saveAndUpdateByCreateBy(params).then(res => {
        this.dialogVisibleCustSupp = false
        this.dialogCustSuppDataLoading = false
        queryDictByCustomerId(this.$route.query.id).then(res => {
          this.customerData = res
        })
      }).catch(() => {
        this.dialogCustSuppDataLoading = false
      })
    },
    showCustomerSupplierByDict(dictItem) {
      // 显示客户版块类型
      this.dialogCustSuppData.contactId = ''
      this.dialogCustSuppData.accountId = ''
      this.dialogCustSuppData.customerId = this.$route.query.id
      this.dictDialogDataUp(dictItem, this.customerData.length > 1)
    },
    showContactByDict(dictItem, itemlist) {
      // 显示联系人版块类型
      this.dialogCustSuppData.customerId = ''
      this.dialogCustSuppData.accountId = ''
      this.dialogCustSuppData.contactId = dictItem.contactId
      this.dictDialogDataUp(dictItem, itemlist.length > 1)
    },
    showAccountPlate(dictItem, itemlist) {
      // 显示账户版块类型
      this.dialogCustSuppData.customerId = ''
      this.dialogCustSuppData.contactId = ''
      this.dialogCustSuppData.accountId = dictItem.accountId

      this.dictDialogDataUp(dictItem, itemlist.length > 1)
    },
    dictDialogDataUp(dictItem, isShowName = false) {
      this.dialogCustSuppData.dictId = (dictItem.dictId || '') + ''
      this.dialogCustSuppData.dictName = dictItem.dictName
      this.dialogCustSuppData.isShowName = isShowName
      this.dialogCustSuppData.isCustomer = dictItem.isCustomer ? '1' : '0'
      this.dialogCustSuppData.isSupplier = dictItem.isSupplier ? '1' : '0'
      this.dialogVisibleCustSupp = true
    },
    labelCustomerSupplier(isCustomer, isSupplier, isBoth = true) {
      // console.log('cust and supp', isCustomer, isSupplier)
      let label = '暂无类型'
      if ((isCustomer && isCustomer != 0) || (isSupplier && isSupplier != 0)) {
        if (isBoth) {
          label = '('
        } else {
          label = ''
        }
        if (isCustomer && isCustomer != 0) {
          label += '客户'
          if (isSupplier && isSupplier != 0) {
            label += '、'
          }
        }
        if (isSupplier && isSupplier != 0) {
          label += '供应商'
        }
        if (isBoth) {
          label += ')'
        }
        return label
      }
      return label
    },
    accountClose(item) {
      this.$confirm('此操作将删除该类型, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // del
        sysExternalAccountPlateDel([item.id]).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          queryAccountDictByCustomerId(this.$route.query.id).then(res => {
            this.accountData = res
          })
        })
      })
    },
    handleContactClose(item) {
      this.$confirm('此操作将删除该类型, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // del
        sysExternalCustomerContactPlateDel([item.id]).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          queryContactDictByCustomerId(this.$route.query.id).then(res => {
            this.contactData = res
          })
        })
      })
    },
    handleClose(item) {
      this.$confirm('此操作将删除该类型, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // del
        delCustomerPlate([item.id]).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          queryDictByCustomerId(this.$route.query.id).then(res => {
            this.customerData = res
          })
        })
      })
    },
    updateInfoBtn() {
      this.loadingInfo = true
      this.$refs['form'].validate((valid) => {
        if (valid) {
          editCustomer(this.form).then(() => {
            this.loadingInfo = false
            this.infoVisible = false
            this.data = this.form
            this.$message({
              type: 'success',
              message: '更新成功'
            })
          }).catch(err => {
            this.loadingInfo = false
            this.$message.error(err)
          })
        } else {
          this.loadingInfo = false
          return false
        }
      })
    },
    showInfoEdit(data) {
      this.form = JSON.parse(JSON.stringify(data))
      this.infoVisible = true
    },
    loadList() {
      this.customerList = []
      listAll().then(res => {
        if (res && res.length > 0) {
          this.customerList = [{
            id: 0,
            name: '请选择'
          }].concat(res.filter(item => item.id != this.$route.query.id))
        }
      })
    },
    openDialog() {
      // this.loadList()
    },
    contactLabel(contactId) {
      if (this.contactData && this.contactData.length > 0) {
        const label = this.contactData.filter(item => {
          return item.contactId === contactId
        })
        return label
      } else {
        return []
      }
    },
    accountLabel(accountId) {
      if (this.accountData && this.accountData.length > 0) {
        const label = this.accountData.filter(item => {
          return item.accountId === accountId
        })
        return label
      } else {
        return []
      }
    },
    plateToLabel(list) {
      if (list && list.length > 0) {
        return list.map(item => {
          let label = item.dictName
          if (item.isCustomer || item.isSupplier) {
            label += '('
            if (item.isCustomer) {
              label += '客户'
              if (item.isSupplier) {
                label += ','
              }
            }
            if (item.isSupplier) {
              label += '供应商'
            }
            label += ')'
          }
          return label
        }).join('、')
      } else {
        return '--'
      }
    },
    changeCustomerName(name) {
      console.log('customerName-id', this.customerId)
      console.log('customerName', name)
      this.customerName = name
    },
    addAccountClk() {
      this.formAccount = {
        bankAccount: '',
        bankName: '',
        spare2: ''
      }
      this.dialogAccount = true
    },
    upAccountOpen(item) {
      this.formAccount = JSON.parse(JSON.stringify(item))
      this.dialogAccount = true
    },
    showContactEdit(item) {
      this.formLabelAlign = JSON.parse(JSON.stringify(item))
      this.dialogVisible = true
    },
    subAccount() {
      this.accountLoading = true
      this.$refs['accountForm'].validate(valid => {
        if (!valid) {
          this.accountLoading = false
          return
        }
        if (!this.data.id) {
          return
        }
        this.formAccount.spare1 = this.data.id
        this.formAccount.companyName = this.data.name
        if (this.formAccount.id) {
          // edit
          editAccount(this.formAccount).then(res => {
            this.dialogAccount = false
            this.loadAccount(this.$route.query.id)
          }).finally(() => {
            this.accountLoading = false
          })
        } else {
          addAccount(this.formAccount).then(res => {
            console.log('add acc', res)
            this.dialogAccount = false
            this.loadAccount(this.$route.query.id)
          }).finally(() => {
            this.accountLoading = false
          })
        }
      })
    },
    async loadAccount(id) {
      // externalAccountList({ spare1: id }).then(res => {
      //   console.log('account', res)
      //   this.accountList = res.content
      // })
      // queryAccountDictByCustomerId(id).then(res => {
      //   console.log('accountDict', res)
      //   this.accountData = res
      // })
      const [accountlist, accountDict] = await Promise.all([externalAccountList({ spare1: id }), queryAccountDictByCustomerId(id)])
      this.accountList = accountlist.content
      this.accountData = accountDict
    },
    loadData(id) {
      detail(id).then(res => {
        console.log(res)
        this.data = res
      })
      queryDictByCustomerId(id).then(res => {
        console.log('customer', res)
        this.customerData = res
      })
    },
    async loadContact(id) {
      // contactList(id).then(res => {
      //   console.log('list', res)
      //   this.contactDataList = res.content
      // })
      // queryContactDictByCustomerId(id).then(res => {
      //   console.log('contact', res)
      //   this.contactData = res
      // })
      const [list, dict] = await Promise.all([contactListAndAllPlateSort(id), queryContactDictByCustomerId(id)])
      this.contactDataList = list
      this.contactData = dict
    },

    addContact() {
      this.contAddLoading = true
      console.log('add btn')
      this.$refs['contactFrom'].validate(valid => {
        if (!valid) {
          this.contAddLoading = false
          return
        }
        this.formLabelAlign.customerId = this.$route.query.id
        if (this.formLabelAlign.id) {
          editContact(this.formLabelAlign).then(res => {
            this.dialogVisible = false
            this.loadContact(this.$route.query.id)
          }).finally(() => {
            this.contAddLoading = false
          })
        } else {
          addContact(this.formLabelAlign).then(res => {
            this.dialogVisible = false
            this.loadContact(this.$route.query.id)
          }).finally(() => {
            this.contAddLoading = false
          })
        }
      })
    }
  }
}
</script>
<style>
.infoItemCls{
  font-weight: 500;
  /* background: white; */
}
</style>
</style>

<style scoped>
.flexcenter{
  display: flex;
  justify-content: center;
}
.sitpan{
  margin-bottom: 15px;
}
.editTop{
  position:absolute;
  top:0;
  right: 0;
}
.cardContact .editTop{
  /* visibility: hidden; */
  opacity:0;
  transition:all .3s linear;
  -webkit-transition:all .3s linear;
}
.cardContact:hover .editTop{
  /* visibility:visible; */
  opacity:1;
}
.ml3{
  margin-left: 3px;
}
.tagcls{
  margin: 3px;
  cursor:pointer;
}

.box-card{
  margin-bottom: 25px;
}
.headTitleCls{
  font-size: 16px;
    font-weight: bold;
}
.centerflex{
  display: flex;
  align-items: center;
  justify-content:space-between;
}
</style>
