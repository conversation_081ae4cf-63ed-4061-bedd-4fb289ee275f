<template>
  <section>
  <div class="">
    <el-form :inline="true" :model="params" class="query-form demo-form-inline" style="padding:10px;">
       <el-form-item label="挂账状态" style="text-align: left" >
         <el-tag :type="params.status=='1'?'success':'info'" @click="selstatus('1')">挂账待办</el-tag>
      &nbsp;
      <el-tag :type="params.status=='2'?'success':'info'" @click="selstatus('2')">挂账已办</el-tag>
      </el-form-item>
      <el-form-item label="船舶名称">
        <el-input
          v-model="params.shipName"
          placeholder="船舶名称"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="挂帐号">
        <el-input
          v-model="params.onAccountNum"
          placeholder="挂帐号"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
       <!-- <el-form-item label="船名" style="text-align: left" ><el-input clearable  v-model="params.shipName"></el-input></el-form-item>
      <el-form-item label="离港日期" style="text-align: left" >
       <el-date-picker
      v-model="params.shipExtime"
      @change="loadData(1)"
      type="date"
      placeholder="离港日期"
      format="yyyy-MM-dd"
      value-format="yyyy-MM-dd">
    </el-date-picker>
      </el-form-item> -->

      <!-- <el-form-item label="类型" style="text-align: left" >
      <el-tag :type="params.acctype=='0'?'success':'info'" @click="selAcctype('0')">成功</el-tag>
      &nbsp;
      <el-tag :type="params.acctype=='1'?'success':'info'" @click="selAcctype('1')">山东</el-tag>
    </el-form-item> -->



      <el-button @click="loadData(1)">查询</el-button>
    </el-form>

    <!-- <div style="width:30%;"><el-button @click="batchEdit">批量完善</el-button>
    <el-button @click="batchTij">批量挂账</el-button> -->
    </div>
  </div>
     <el-table
      v-loading="tableLoading"
          ref="multipleTable"
      tooltip-effect="dark"
      stripe
      :data="costList">
      <el-table-column prop="finishTime"  align="center"  label="挂账年月" :formatter="formatDateYYYYMM"></el-table-column>
      <el-table-column prop="globalParam5"  align="center"  label="挂账号" :formatter="publicFmt"></el-table-column>
       <!-- <el-table-column
      type="selection"
      width="55">
    </el-table-column> -->
      <!-- <el-table-column label="离港日期" align="center" prop="epartureTimeDate"/> -->
      <el-table-column label="申请人" align="center" prop="sponsorName" />
      <el-table-column label="摘要" align="center" prop="briefContent"/>
      <!-- <el-table-column label="运价" align="center" prop="goodsFreight"/>
      <el-table-column label="金额" align="center" prop="totalPrice"/>
      <el-table-column label="已收款" align="center" prop="goodsReceived" />
      <el-table-column label="付款日期" align="center" prop="paymentDate" /> -->
      <el-table-column label="状态" align="center"  >
        <template slot-scope="scope">
          <span>{{scope.row.status==1?'审批中':scope.row.status==2?'已通过':'--'}}</span>
        </template>
      </el-table-column>

      <!-- <el-table-column label="一级客户" align="center" prop="oneCustomerName"/>
      <el-table-column label="二级客户" align="center" prop="customerName"/> -->

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="detailHandle(scope.row)">挂账清单</el-button>
          <el-button type="text"  @click="processShow(scope.row)">审批详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页-->
    <div style="text-align: right;">
      <el-pagination
        hide-on-single-page
         background
        :total="total"
        :page-size="pageSize"
        :current-page="pageNum"
        layout="prev, pager, next"
        @current-change="loadData"
      >
      </el-pagination>
    </div>

   <el-dialog
      title="服务费结算单"
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="handleClose"
      append-to-body>
      <div id="apply-print-id" style="display: flex;justify-content: space-between;padding:5px 0;">
        <div>挂账号：{{ dialogData.globalParam5 }} </div>
        <div>核算：{{ areaName||'--' }}</div></div>
      </div>
      <el-table
      class="el-table-info"
      :data="applyObj"
       :span-method="accepSpanMethod"
       id="apply-table-id"
      stripe
      style="width: 100%;"
      :show-overflow-tooltip="true"
      border
      >
      <el-table-column label="离港日期" align="center" prop="epartureTimeDate">
         <template slot-scope="scope">
          <div v-if="scope.row.costType != 100">{{scope.row.epartureTimeDate}} </div>
          <div v-if="scope.row.costType === 100">合计 </div>
        </template>
      </el-table-column>
     <el-table-column label="船舶名称" align="center" prop="shipName"/>
     <el-table-column label="费种" align="center" prop="costTypeName"/>
     <el-table-column label="重量" align="center" prop="goodsTonnage"/>
     <el-table-column label="应付金额" align="center" prop="accepTotalPrice" ></el-table-column>
     <el-table-column label="供应商" align="center" prop="cusSupName" ></el-table-column>
     <el-table-column label="发票" align="center" >
      <template slot-scope="scope">
        <span>增</span>
      </template>
     </el-table-column>
     <el-table-column label="合同公司" align="center" >
       <template slot-scope="scope">
        <span>Y</span>
      </template>
     </el-table-column>
    </el-table>
    <div id="sp-list-id" style="margin-top:10px;display:flex;justify-content: space-around;">
            <div v-for="litem in spLogList" :key="litem.id">
              <span v-if="litem.nodeLabel">{{ litem.nodeLabel }}:{{ litem.userName||'--'}}</span>
            </div>
    </div>
    <div style="text-align: right;">
      <el-button @click="printApply">打印</el-button>
    </div>
     <template v-if="dialogData.status == 1" slot="footer">
        <div style="text-align: right;">
          <el-popconfirm title="确认标记为已挂账吗？" @confirm="markFinish">
            <el-button slot="reference" type="primary">标记为已挂账</el-button>
          </el-popconfirm>
        </div>
      </template>
   </el-dialog>
  <el-dialog
      title="审批详情"
      :visible.sync="showProcess"
      width="50%"
      :before-close="handleClosesshowProcess"
      append-to-body
    >
      <el-divider content-position="left">审批流程</el-divider>
      <el-timeline id="timelineBody">
        <!-- 发起申请 -->
        <el-timeline-item
          :timestamp="timeline.start.content"
          color="#67C23A"
          size="large"
          placement="top">
          <div style="display: flex;justify-content: space-between;">
            <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
              <el-avatar shape="square" size="large" :src="timeline.start.item.avatar"></el-avatar>
              <div style="margin-left: 10px;color: #333333;">{{timeline.start.item.name}}</div>
            </div>
            <div style="display: flex;align-items: flex-end;flex-direction: column">
              <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{timeline.start.item.sponsorTime}}</div>
              <el-tooltip v-if="!!timeline.start.reason && timeline.start.reason.length > 22" effect="dark" placement="top">
                <div slot="content">
                  <div v-html="timeline.start.reason"></div>
                </div>
                <div style="font-size: 12px;color: #909399;cursor: default;">{{timeline.start.reason.substr(0,20)}}...</div>
              </el-tooltip>
              <div v-else style="font-size: 12px;color: #909399">{{timeline.start.reason}}</div>
            </div>
          </div>
        </el-timeline-item>

        <!-- 审批人 -->
        <el-timeline-item
          v-for="(activity, index) in timeline.sp"
          v-if="timeline.status != '4'"
          :timestamp="activity.content"
          :key="index"
          :color="activity.color"
          :size="activity.size"
          placement="top">
          <div style="display: flex;justify-content: space-between;">
            <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
              <!--                <el-avatar shape="square" size="large" :src="activity.item.avatar"></el-avatar>-->
              <el-avatar v-if="activity.item.sp_type=='0'" shape="square" size="large" :src="activity.item.avatar"></el-avatar>
              <span class="diy-avatar" v-if="activity.item.sp_type=='1'">{{activity.item.avatar |strSubFil}}</span>
              <!--                <div style="margin-left: 10px;color: #333333;">{{activity.item.name}}</div>-->
              <div style="margin-left: 10px;color: #333333;">{{activity.item.sp_type=='0'?activity.item.name:activity.item.avatar}}</div>
            </div>
            <div style="display: flex;align-items: flex-end;flex-direction: column">
              <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{activity.commentTime}}</div>
              <el-tooltip v-if="!!activity.comment && activity.comment.length > 22" effect="dark" placement="top">
                <div slot="content">
                  <div v-html="activity.comment"></div>
                </div>
                <div style="font-size: 12px;color: #909399;cursor: default;">{{activity.comment.substr(0,20)}}...</div>
              </el-tooltip>
              <div v-else style="font-size: 12px;color: #909399">{{activity.comment}}</div>
            </div>
          </div>
        </el-timeline-item>

        <!-- 抄送人 -->
        <el-timeline-item
          :timestamp="timeline.ccs.content"
          :color="timeline.ccs.color"
          :size="timeline.ccs.size"
          placement="top">
          <el-row class="timelineContent">
            <div v-for="people in timeline.ccs.items" style="margin-right:10px">
              <el-avatar shape="square" size="large" :src="people.avatar"></el-avatar>
            </div>
          </el-row>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </section>
</template>

<script>
import dayjs from "dayjs";

const ERR_OK = '0'
import { getList,getProcessByAccepCost,processlist,listByIds } from '@/api/system/accepCost.js'
// import WorkFlowComponent from "@/components/workflow/index";
// import OnAccountAccepCost from "@/components/approval/OnAccountOtherCost"
// import OnAccountApprove from "@/components/approval/OnAccountApprove";
// import WorkFlowProcessDrawer from "@/components/workflow/process";
import currency from "currency.js"
import { taskGetNew, updateOnAccountOtherCostProcess } from "@/api/system/onAccount.js";
import {getWuLiuSpPerspnListByProcessId} from '@/api/system/process'
export default {
  components: {
    // WorkFlowComponent,
    //   WorkFlowProcessDrawer,
    // OnAccountApprove
  },
      data() {
        return {
            timeline: {
                status: null,
                start: {
                  content: '发起申请',
                  item: {
                    id: 0,
                    name: '',
                    avatar: ''
                  }
                },
                sp: [],
                ccs: {
                  content: '抄送(0人)',
                  items: []
                }
              },
      showProcess:false,
      onAccountNum:'',
      tableLoading: false,
      dialogVisible: false,
      submitLoading: false,
      selOptions: [
        { label: '全部',value:'' },
        { label: '未挂账',value:0 },
        { label: '审批中',value:1 },
        {label:'已挂账',value:2},
      ],
      formCost: {
        paymentDate:'',
        interestRate: 0.7,
        platformFee: 0.07,
        accepPrice:null,
        isChengdui: false,
        accepMonthRate: 0.22,
        accepDiscount:null
      },
      pageNum: 1,
      pageSize:10,
      params: {
        acctype:'',
        status:'1',
        shipExtime: '',
        shipName:'',
        onAccountNum:'',
        secondLevelId:'76ee5bdac10f4b9cad22fbc73739fea9'
      },
      total:0,
      areaName:'',
      costList: [],
      applyObj: [],
          dialogData: {},
          spLogList:[]
    }
  },
  filters: {
    strSubFil(s) {
      if (!s) {
        return ''
      }
      return s.substring(0, 1)
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    printContentByRef(refName) {
      return this.$refs[refName] && this.$refs[refName].exportData({
        original: false,
        type: 'html',
        download: false,
        remote: false,
        print: true,
        isMerge: true
      })
    },
    printHtmlId(id) {

      const html = document.querySelector('#' + id).innerHTML
      // 新建一个 DOM
      const div = document.createElement('div')
      const printDOMID = 'printDOMElement'
      div.id = printDOMID
      div.innerHTML = html

      // 提取第一个表格的内容 即表头
      const ths = div.querySelectorAll('.el-table__header-wrapper th')
      const ThsTextArry = []
      for (let i = 0, len = ths.length; i < len; i++) {
          if (ths[i].innerText !== '') ThsTextArry.push(ths[i].innerText)
      }

      // 删除多余的表头
      div.querySelector('.hidden-columns').remove()
      // 第一个表格的内容提取出来后已经没用了 删掉
      div.querySelector('.el-table__header-wrapper').remove()

      // 将第一个表格的内容插入到第二个表格
      let newHTML = '<thead><tr>'
      for (let i = 0, len = ThsTextArry.length; i < len; i++) {
          newHTML += '<th style="text-align: center;" >' + ThsTextArry[i] + '</th>'
      }

      newHTML += '</tr></thead>'
      console.log('newHTML', newHTML)
      // 删除 colgroup
      div.querySelector('.el-table__body-wrapper colgroup').remove()
      // div.querySelector('.el-table__body-wrapper table').insertAdjacentHTML('afterbegin', newHTML)
      // table>tbody 插入 thead  变成 table>thead>tbody
      div.querySelector('.el-table__body-wrapper table').insertAdjacentHTML('afterbegin', newHTML)
      // 宽度修改
      div.querySelector('.el-table__body-wrapper table').style.width = '100%'
      // border = 1
      // div.querySelector('.el-table__body-wrapper table').style.border = '1px solid #ebeef5'
      div.querySelector('.el-table__body-wrapper table').setAttribute('border', '1')

      return div.innerHTML
    },
    printApply() {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      let divEl = `<div style="margin-left:180px;font-size:22px;">`
      divEl += document.getElementById('apply-print-id').outerHTML
      divEl += this.printHtmlId('apply-table-id')
      divEl += document.getElementById('sp-list-id').outerHTML
      divEl += `</div>
      <style>
      table {
        border-collapse: collapse;
        border-spacing: 0;
      }
      table td,table th{
        text-align: center;
        padding: 10px;
      }
      </style>
      `
      console.log('pdiv',divEl)
      loading.close()
      this.$XPrint({
        sheetName: '打印合值 挂账号：' + (this.dialogData.globalParam5 || '--'),
        content: divEl
      })

    },
    loadSpListByProcessId(processId) {
      this.spLogList = []
      getWuLiuSpPerspnListByProcessId(processId).then(res => {
        console.log('process',res)
        if (res.data) {
          this.spLogList = res.data
        }
      })
    },
     accepSpanMethod({ row, column, rowIndex, columnIndex }) {
      // return {
      //         rowspan: 1,
      //         colspan: 1
      //       };
      if (columnIndex === 2 || columnIndex === 6 || columnIndex === 7) {
        if (rowIndex == 0) {
          return {
            rowspan: this.applyObj && this.applyObj.length > 0 ? this.applyObj.length : 1,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }

      } else if (columnIndex === 5) {
        // 一样的内容合并 不一样的不合并 cusSupName 上下 一样的合并
        // 后边一样的合并 上边一样的忽略
        let cusSupName = row.cusSupName
        let upCusSupName = ''
        let downCusSupName = ''
        if (rowIndex > 0) {
          upCusSupName = this.applyObj[rowIndex - 1].cusSupName
        }
        if (rowIndex < this.applyObj.length - 1) {
          downCusSupName = this.applyObj[rowIndex + 1].cusSupName
        }
        if (cusSupName === upCusSupName) {
          return {
            rowspan: 0,
            colspan: 0
          };
        } else if (cusSupName === downCusSupName) {
          let i = 1
          for (let index = rowIndex + 1; index < this.applyObj.length; index++) {
            const element = this.applyObj[index];
            if (cusSupName === element.cusSupName) {
              i++
            } else {
              break
            }
          }
          return {
            rowspan: i,
            colspan: 1
          }
        }else{
          return {
            rowspan: 1,
            colspan: 1
          }
        }


      } else {
        return {
          rowspan: 1,
          colspan: 1
        };
      }
    },
     loadAccepCostList(ids) {
      // 加载列表
       this.applyObj = []
      this.areaName=''
      listByIds({ ids }).then(res => {
        console.log(res)
        if (res && res.data && res.data) {
          let dlist = res.data
          this.areaName =res.areaName
          // 增加合计
          var dun = 0
          var jia = 0
          dlist.forEach(res => {
            dun = currency(dun).add(currency(res.goodsTonnage)).value
            jia = currency(jia).add(currency(res.accepTotalPrice)).value
            // if (user != res.yewuId){
            //   i = 1
            // }
          })
          dlist.push({
            costType: 100,
            goodsTonnage: dun,
            accepTotalPrice: jia
          })
          this.applyObj = dlist
        }
      })
    },
    selstatus(type) {
      this.params.status = type
      this.loadData(1)
    },
     refresh() {
        // 审批完成后会调用刷新方法
      },
    processShow(row) {
      // getProcessByAccepCost({id}).then(res => {
      //   if (res && res.data && res.data) {
      // this.handleRowClick(row)
      this.showProcessDetail(row.id)
      //   }
      // })
    },
     showProcessDetail(id){
          console.info(id)
          taskGetNew(id).then(res => {
            this.timeline.status = res.data.statusId
            this.timeline.start = res.data.spFlow.start
            this.timeline.sp = res.data.spFlow.sp
            this.statusId = res.data.nodeId;
            this.timeline.ccs = res.data.spFlow.ccs
            for (var i = 0; i < this.timeline.sp.length; i++) {
              if (this.timeline.sp[i].content.indexOf("审批中") != -1) {
                this.currentSpId = this.timeline.sp[i].item.id
              }
            }
            this.showProcess = true
          })
    },
         handleClosesshowProcess(){
          this.showProcess = false
        },
    selAcctype(type) {
      console.log('type',type)
      if (this.params.acctype === type) {
        this.params.acctype = ''
      } else {
        this.params.acctype=type
      }
      this.loadData(1)
    },

    handleClose() {
      this.dialogVisible=false
    },

    loadData(pageNum = 1) {
      // console.log(pageNum,'loaddata')
      this.tableLoading = true
      // this.total = 0
      this.pageNum = pageNum
      this.costList = []
      this.total= 0
      // this.params.pageNum = this.pageNum
      // this.params.pageSize = this.pageSize
      // this.$set(this.params,'pageNum',pageNum)
      processlist({
        status:this.params.status,
        pageNum:this.pageNum,
        pageSize:this.pageSize,
        onAccountNum:this.params.onAccountNum,
        shipName:this.params.shipName
      }).then(res => {
         this.total = 0
        if (res.resultCode == ERR_OK && res.data) {
          this.costList = res.data.rows
          this.total = res.data.total
        }
      }).finally(() => {
        this.tableLoading=false
      })
      // getList({ secondLevelId:this.params.secondLevelId,pageNum:this.pageNum,pageSize:this.pageSize,...this.params }).then(res => {
      //   this.total = 0
      //   if (res.resultCode == ERR_OK && res.data) {
      //     this.costList = res.data.rows
      //     this.total = res.data.total
      //   }
      // }).finally(() => {
      //   this.tableLoading=false
      // })
    },
    detailHandle(detail) {
      this.dialogData = detail
      this.loadAccepCostList(detail.globalParam2)
      this.loadSpListByProcessId(detail.id)
      this.dialogVisible = true

    },
    formatDateYYYYMM(row, column, cellValue, index) {
      return this.myFmtDateTime(cellValue, 'YYYY-MM')
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue === undefined || cellValue == null || cellValue == '') {
        return '--'
      }
      return dayjs(cellValue).format(fmtstr)
    },
    markFinish() {
      // console.log('-----------')
      this.dialogVisible = true
      updateOnAccountOtherCostProcess(this.dialogData.id, this.dialogData.statusId, '2', '挂账完成').then(res => {
        this.dialogVisible = false
        this.loadData()
      })
    },
    publicValFmt(cellValue) {
      var v = "--";
      if (cellValue != undefined && cellValue != null && cellValue != "") {
        v = cellValue
      }
      return v;
    },
    publicFmt(row, column, cellValue, index) {
      var v = "--";
      if (cellValue != undefined && cellValue != null && cellValue != "") {
        v = cellValue
      }
      return v;
    }

  }
}
</script>

<style scoped>
.flexbetween{
  display: flex;
  justify-content: flex-between;
  align-items:center;
}
.demo-form-inline{
  padding:30px;
}
.demo-form-inline input{
  width: 30%;
}
</style>
