<template>
  <section>
    <!-- 3个tab 收款/付款/往来款 -->
    <ReceiveWater v-if="activeName=='receivewater'" :prop-type="type" :prop-child="child" :prop-company="company" :prop-time="time" />
    <PaymentWater v-if="activeName=='paymentwater'" :prop-type="type" :prop-child="child" :prop-company="company" :prop-time="time" />
    <ComeToTotalForm v-if="activeName=='comeToTotalForm'" :prop-type="type" :prop-company="company" :prop-wl-type="isWangLaiType" :prop-time="time" :prop-nei-wai="isNeiWai" />

    <!-- 3个页面 views/reportform/receivewater.vue views/reportform/paymentwater.vue views/reportform/comeToTotalForm.vue  -->

  </section>
</template>
<script>
import ReceiveWater from '@/views/reportform/receivewater.vue'
import PaymentWater from '@/views/reportform/paymentwater.vue'
import ComeToTotalForm from '@/views/reportform/comeToTotalForm.vue'
export default {
  components: {
    ReceiveWater,
    PaymentWater,
    ComeToTotalForm
  },
  data() {
    return {
      activeName: '',
      time: [],
      company: '',
      isWangLaiType: '',
      type: [],
      child: [],
      isNeiWai: ''
    }
  },
  created() {
    this.loadParams()
  },
  methods: {
    loadParams() {
      const route = this.$route
      const query = route.query
      if (query.activeName) {
        this.activeName = query.activeName
      }
      // 时间、公司、类型
      if (query.time) {
        // this.time = query.time
        this.time = query.time.split(',')
      }
      if (query.company) {
        this.company = query.company
      }
      if (query.isWangLaiType) {
        this.isWangLaiType = query.isWangLaiType
      }
      if (query.type) {
        this.type = query.type.split(',')
      }
      if (query.child) {
        this.child = query.child.split(',')
      }
      if (query.isNeiWai) {
        this.isNeiWai = query.isNeiWai
      }
      // console.log('query', this.company,this.isWangLaiType, this.time, this.type)
    }
  }
}
</script>
<style scoped>

</style>
