<template>
  <section class="app-container" id="dataform" style="margin-top: 20px;">
    <div style="width: 100%;text-align: right;margin-top: -42px;margin-bottom: 10px">
      <el-button type="primary" @click="printApply">打印</el-button>
    </div>
    <table rules="all" frame="box" class="record-table">
      <tr>
        <td style="text-align: left" colspan="6">
          <div style="text-align: center;font-size: 1.4em">
            收款单
          </div>
          <div style="display: flex;">
            <div style="margin-top: -10px;margin-bottom: 5px;text-align: left;width: 50%;">
              <span style="padding-left: 10%">收款时间：{{waterInfo.createTime}}</span>
            </div>
            <div style="margin-top: -10px;margin-bottom: 5px;text-align: right;width: 50%;">
              <span style="padding-right: 10%">收款编号：{{ waterInfo.waterNo }}</span>
            </div>
          </div>
        </td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">收款类型</td>
        <!-- <td style="width: 40%" colspan="2">{{costTypeFmt(null,null,waterInfo.costType,null)}}</td> -->
        <td style="width: 40%" colspan="2">{{waterInfo.fundsClassName || costTypeFmt(null,null,waterInfo.costType,null)}}</td>
        <td style="width: 15%" colspan="1">账户类型</td>
        <td style="width: 30%" colspan="2">{{paymentTypeFmt(null,null,waterInfo.receviceType,null)}}</td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">小写金额</td>
        <td style="width: 30%" colspan="2">{{ waterInfo.balance }}</td>
        <td style="width: 15%" colspan="1">大写金额</td>
        <td style="width: 40%" colspan="2">{{ waterInfo.sumChinese }}</td>

      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">收款单位</td>
        <td style="width: 40%" colspan="2">{{waterInfo.receviceCompanyName}}</td>
        <td style="width: 15%" colspan="1">付款单位</td>
        <td style="width: 30%" colspan="2">{{waterInfo.customerName}}</td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">收款银行</td>
        <td style="width: 40%" colspan="2">{{accountInfo.bankName}}</td>
        <td style="width: 15%" colspan="1">付款银行</td>
        <td style="width: 30%" colspan="2">{{waterInfo.paymentBank}}</td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">收款账号</td>
        <td style="width: 40%" colspan="2">{{accountInfo.accountNumber}}</td>
        <td style="width: 15%" colspan="1">付款账号</td>
        <td style="width: 30%" colspan="2">{{waterInfo.paymentAccount}}</td>
      </tr>
      <!-- <template v-if="waterInfo.receviceType==3">
           <tr style="height: 40px">
        <td style="width: 15%" colspan="1">票号</td>
        <td style="width: 40%" colspan="2">{{waterInfo.ticketNumber}}</td>
        <td style="width: 15%" colspan="1">出票人</td>
        <td style="width: 30%" colspan="2">{{waterInfo.drawerName}}</td>
      </tr>
         <tr style="height: 40px">
        <td style="width: 15%" colspan="1">到期日</td>
        <td style="width: 40%" colspan="2">{{waterInfo.expireDate}}</td>
        <td style="width: 15%" colspan="1">票种</td>
        <td style="width: 30%" colspan="2">{{waterInfo.ticketType==1?'银行承兑':'商业承兑'}}</td>
      </tr>
        <tr style="height: 40px">
        <td style="width: 15%" colspan="1">出票日</td>
        <td style="width: 40%" colspan="2">{{waterInfo.votingDate}}</td>
        <td style="width: 15%" colspan="1">能否转让</td>
        <td style="width: 30%" colspan="2">{{waterInfo.isTransferred}}</td>
      </tr>
      </template> -->

      <!--    <div style="width: 100%;text-align: left">-->
      <tr>
        <td style="width: 100%;height: 80px;text-align: left" colspan="6">
          <div style="margin-top: -30px">
            备注：
            <span class="pt-show">{{ waterInfo.remark }}</span>
            <span class="pt-print" >{{ waterInfo.remark && waterInfo.remark.replace(/\#.*\#/g, '') }}</span>
          </div>
        </td>
      </tr>
      <tr>
        <td style="width: 100%;text-align: right;" colspan="6">
          <span style="margin-right: 5%;">制单人：{{userName}}</span>
        </td>
      </tr>
    </table>
  </section>
</template>

<script>

import {getReceiveWaterById} from "../../../api/system/receiveWater";
import {getDictionaryList} from "../../../api/system/baseInit";
import {fmtDictionary} from "../../../utils/util";

export default {
  name: 'ReceiveOrder',
  data() {
    return {
      moneydetail: [],
      allmoney: [],
      splist: [],
      filelength: 0,
      PaidMoney: 0,
      allpayMoney: 0,
      companylist: [],
      yongtu: '',
      ExternalAccount: [],
      dicData: [
        {
          label: '现金',
          value: 1
        }, {
          label: '网银',
          value: 0
        }, {
          label: '银行承兑',
          value: 2
        }],
      unit: new Array('仟', '佰', '拾', '', '仟', '佰', '拾', '', '角', '分'),
      voucher:null,
      waterInfo:{},
      accountInfo:{},
      dictionaryLists:[],
      receiveWaterId:null,
      userName:null,
    }
  },
  created() {
    // getAllCompany().then(res => {
    //   this.companylist = res.data
    // })
    // getExternalAccount().then(res => {
    //   this.ExternalAccount = res.data
    // })
    // getProcessVoucher(this.datas.processid).then(res=>{
    //   this.voucher =res.data
    // })
    // this.chuli()
    if (this.$route.query.receiveWater) {
      this.receiveWaterId = this.$route.query.receiveWater
    }
    getDictionaryList("settlement_type,fdbiz_cost_type").then(res=>{
      this.dictionaryLists = res.map
    })
    this.chuliReceive()
  },
  methods: {
    printApply() {
      var str = window.document.getElementById('dataform').innerHTML
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    },
    chuliReceive() {
      getReceiveWaterById(this.receiveWaterId).then(res=>{
        if(res && res.resultCode==='0'){
          this.waterInfo = res.data
          this.accountInfo = res.account
          this.userName = res.user.nickName
          this.NumberToChinese(this.waterInfo.balance)
        }
      })
      // this.datas.remake = ""
      // this.allmoney.sum = this.datas.sumNum
      // this.quzheng(this.allmoney)
      //
      // this.datas.year = this.datas.date.slice(0, 4)
      // this.datas.month = this.datas.date.slice(5, 7)
      // this.datas.ri = this.datas.date.slice(8, 10)
    },
    toDx(n) { // 阿拉伯数字转换函数
      switch (n) {
        case '0':
          return '零'
        case '1':
          return '壹'
        case '2':
          return '贰'
        case '3':
          return '叁'
        case '4':
          return '肆'
        case '5':
          return '伍'
        case '6':
          return '陆'
        case '7':
          return '柒'
        case '8':
          return '捌'
        case '9':
          return '玖'
      }
    },
    NumberToChinese(money) {
      var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆',
        '柒', '捌', '玖');
      // 基本单位
      var cnIntRadice = new Array('', '拾', '佰', '仟');
      // 对应整数部分扩展单位
      var cnIntUnits = new Array('', '万', '亿', '兆');
      // 对应小数部分单位
      var cnDecUnits = new Array('角', '分', '毫', '厘');
      // 整数金额时后面跟的字符
      var cnInteger = '整';
      // 整型完以后的单位
      var cnIntLast = '元';
      // 最大处理的数字
      var maxNum = 999999999999999.9999;
      // 金额整数部分
      var integerNum;
      // 金额小数部分
      var decimalNum;
      // 输出的中文金额字符串
      var chineseStr = '';
      // 分离金额后用的数组，预定义
      var parts;
      if (money == '') {
        return '';
      }
      money = parseFloat(money);
      if (money >= maxNum) {
        // 超出最大处理数字
        return '';
      }
      if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger;
        return chineseStr;
      }
      // 转换为字符串
      money = money.toString();
      if (money.indexOf('.') == -1) {
        integerNum = money;
        decimalNum = '';
      } else {
        parts = money.split('.');
        integerNum = parts[0];
        decimalNum = parts[1].substr(0, 4);
      }
      // 获取整型部分转换
      if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0;
        var IntLen = integerNum.length;
        for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1);
          var p = IntLen - i - 1;
          var q = p / 4;
          var m = p % 4;
          if (n == '0') {
            zeroCount++;
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0];
            }
            // 归零
            zeroCount = 0;
            chineseStr += cnNums[parseInt(n)]
              + cnIntRadice[m];
          }
          if (m == 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q];
          }
        }
        chineseStr += cnIntLast;
      }
      // 小数部分
      if (decimalNum != '') {
        var decLen = decimalNum.length;
        for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1);
          if (n != '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i];
          }
        }
      }
      if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger;
      } else if (decimalNum == '') {
        chineseStr += cnInteger;
      }

      this.waterInfo["sumChinese"] =  chineseStr;
    },
    fukuanFmt(id) {
      for (var a = 0; a < this.dicData.length; a++) {
        var item = this.dicData[a]
        if (item.value === id) {
          return item.label
        }
      }
      return ''
    },
    CompanyNameFmt2(id) {
      for (var a = 0; a < this.companylist.length; a++) {
        var item = this.companylist[a]
        if (item.id === id) {
          return item.label
        }
      }
      return ''
    },
    ordercompanynameFmt(id) {
      if (!id) {
        return ' '
      }
      for (var a = 0; a < this.ExternalAccount.length; a++) {
        var item = this.ExternalAccount[a]
        if (item.id === Number(id)) {
          return item.companyName
        }
      }
      return ' '
    },
    orderbanknameFmt(id) {
      if (!id) {
        return ' '
      }
      for (var a = 0; a < this.ExternalAccount.length; a++) {
        var item = this.ExternalAccount[a]
        if (item.id === Number(id)) {
          return item.bankName
        }
      }
      return ' '
    },
    orderbankaccountFmt(id) {
      if (!id) {
        return ' '
      }
      for (var a = 0; a < this.ExternalAccount.length; a++) {
        var item = this.ExternalAccount[a]
        if (item.id === Number(id)) {
          return item.bankAccount
        }
      }
      return ' '
    },
    quzheng(item) {
      item.wan = Math.floor(item.sum / 10000)
      item.qian = Math.floor((item.sum - item.wan * 10000) / 1000)
      item.bai = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000) / 100)
      item.shi = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100) / 10)
      item.yuan = Math.floor(item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10)
      item.mao = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10 - item.yuan) * 10)
      item.fen = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10 - item.yuan - item.mao / 10) * 100)
      if (item.wan > 0) {
        return
      } else if (item.wan === 0 && item.qian > 0) {
        item.wan = '￥'
      } else if (item.qian === 0 && item.bai > 0) {
        item.wan = ''
        item.qian = '￥'
      } else if (item.bai === 0 && item.shi > 0) {
        item.wan = ''
        item.qian = ''
        item.bai = '￥'
      } else if (item.shi === 0 && item.yuan > 0) {
        item.wan = ''
        item.qian = ''
        item.bai = ''
        item.shi = '￥'
      }
    },
    paymentTypeFmt(row,column,cellValue,index) {
      return fmtDictionary(cellValue,this.dictionaryLists['settlement_type']);
    },
    costTypeFmt(row,column,cellValue,index) {
      return fmtDictionary(cellValue,this.dictionaryLists['fdbiz_cost_type']);
    },
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
@media print {
  #dataform{
    padding: 0px;
  }
  .record-table {
    margin-top: 30px;
    margin-left: 90px;
    width:calc(100vw - 90px) !important;
    font-size: 13px;
    line-height: 1.5em;
  }
}
</style>
