<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="账户类型">
        <div class="width">
          <el-tag style="margin-right: 10px;cursor: pointer;" :type="queryParams.receiveType==item.code?'success':'info'"
                  v-for="(item,index) in  paymentTypeList" :key="index" @click="receiveTypeQuery(item)"
          >{{item.value}}</el-tag>
        </div>
      </el-form-item>
      <el-form-item label="付款客户">
        <el-input
          v-model="queryParams.customer"
          placeholder="付款客户"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="核销状态">
        <el-tag style="margin-right: 10px;cursor: pointer;" :type="queryParams.receiveStatus==item.code?'success':'info'"
                v-for="(item,index) in  applyStatusList" :key="index" @click="applyStatusQuery(item)"
        >{{item.value}}</el-tag>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="topage">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table
      class="el-table-info"
      v-loading="loading"
      :data="dataList"
    >
      <el-table-column label="#" align="center" type="index"/>
      <el-table-column label="收款时间" align="center" prop="createTime" :formatter="(row, column, cellValue, index) => { return myFmtDateTime(cellValue,'MM/DD')}"/>
      <el-table-column label="收款类型" align="center" prop="costType" :formatter="costTypeFmt"/>
      <el-table-column label="账户类型" align="center" prop="receviceType" :formatter="receviceTypeFmt"/>
      <el-table-column label="收款金额（元）" align="center" prop="balance" :formatter="NumFmt"/>
      <el-table-column label="收款单位" align="center" prop="receviceCompanyName" :formatter="publicFmt"/>
      <el-table-column label="付款客户" align="center" prop="customerName" :formatter="publicFmt"/>
      <el-table-column label="付款银行" align="center" prop="paymentBank" :formatter="publicFmt"/>
      <el-table-column label="付款账户" align="center" prop="paymentAccount" :formatter="publicFmt"/>
      <el-table-column label="核销状态" align="center" prop="applyStatus":formatter="(row, column, cellValue, index) => { return cellValue==0?'未核销':'已核销'}"/>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="openShowAccount(scope.row)">收款账户详情</el-button>
          <el-button type="text"  @click="showPic(scope.row)">查看财务截图</el-button>
          <el-button type="text" v-if="scope.row.applyStatus === 0" @click="showDetail(scope.row)">核销</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryParams.pageSize"
      :current-page="queryParams.pageNo"
      @current-change="eventPage"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
    >
    </el-pagination>


    <el-dialog
      class="xuanxiang2"
      title="核销详情"
      :visible.sync="showDetailApply"
      width="60%"
      :before-close="detailApplyhandleClose"
      :close-on-click-modal=false>
      <div style="font-size: 16px;font-weight: 500;">付款客户信息：</div>
      <el-form :model="receiveWater" class="query-form demo-form-inline">
        <div style="width: 95%;">
          <el-form-item label="客户公司:" label-width="90px">
            <el-input  v-model="receiveWater.customerName" clearable disabled>
            </el-input>
          </el-form-item>
          <el-form-item label="开户行:" label-width="90px">
            <el-input  v-model="receiveWater.paymentBank"  clearable disabled >
            </el-input>
          </el-form-item>
          <el-form-item label="客户账号:" label-width="90px">
            <el-input v-model="receiveWater.paymentAccount"  clearable disabled></el-input>
          </el-form-item>
        </div>
        <div style="display: flex;">
          <div class="sp_add_left">
            <el-form-item label="收款金额:" label-width="90px">
              <el-input  v-model="receiveWater.balance" clearable disabled>
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="sp_add_right">
            <el-form-item label="账户类型:" label-width="90px">
              <el-input  :value="receviceTypeFmt(null,null,receiveWater.receviceType,null)" clearable disabled>
              </el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div>
        <div style="font-size: 16px;font-weight: 500;">核销信息：</div>
        <el-form :model="verificationInfo" class="query-form demo-form-inline" ref="addVerificationInfo" :rules="rules">
          <div style="display: flex;">
            <div class="sp_add_left">
              <el-form-item label="船名:" label-width="90px" prop="shipName">
                <el-input  v-model="verificationInfo.shipName" placeholder="请输入船名" clearable>
                </el-input>
              </el-form-item>
              <el-form-item label="结算吨位:" label-width="90px">
                <el-input  v-model="verificationInfo.settleTonnage" placeholder="请输入结算吨位" clearable>
                  <template slot="append">吨</template>
                </el-input>
              </el-form-item>
              <el-form-item label="滞期费:" label-width="90px">
                <el-input  v-model="verificationInfo.demurrage" placeholder="请输入滞期费" clearable>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="sp_add_right">
              <el-form-item label="航次号:" label-width="90px" prop="voyageNum">
                <el-input  v-model="verificationInfo.voyageNum" placeholder="请输入航次号" clearable>
                </el-input>
              </el-form-item>
              <el-form-item label="单价:" label-width="90px">
                <el-input  v-model="verificationInfo.price" placeholder="请输入单价" clearable>
                  <template slot="append">元/吨</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div>
        <div style="font-size: 16px;font-weight: 500;">合同信息：</div>
        <div style="padding: 20px 0;">
          <el-switch
            @change="updataFileList"
            v-model="isAddContract"
            active-color="#13ce66"
            inactive-color="#00A7FF"
            active-text="新增合同"
            inactive-text="选择历史合同">
          </el-switch>
        </div>
        <div style="display: flex;">
          <div style="height:40px;line-height: 30px">合同编号:</div>
          <div style="margin-left: 10px;">
            <el-select v-if="!isAddContract" v-model="contractSelect" placeholder="请选择" style="width: 300px;" filterable :filter-method="getContractList" @change="selectContract">
              <el-option
                v-for="item in contractsList"
                :key="item.value"
                :label="item.label"
                :value="item"
              >
              </el-option>
            </el-select>
            <el-input v-if="isAddContract" v-model="contract.contractname" placeholder="请输入合同编号" style="width: 300px;"></el-input>
          </div>
          <div>
            <el-upload
              :disabled="!isAddContract"
              class="upload-demo"
              :action="upload"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :on-success="savefileList"
              :limit="1"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button :style="isAddContract?'margin-left: 5px':'margin-left: 5px;display: none;'" size="small" type="primary">点击上传</el-button>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible" append-to-body>
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">提交</el-button>
      </span>
    </el-dialog>

    <el-dialog
      class="xuanxiang2"
      title="收款账户详情"
      :visible.sync="showAccount"
      width="60%"
      :before-close="showAccounthandleClose"
      :close-on-click-modal=false>
      <div style="font-size: 20px;font-weight: 500;margin-bottom: 10px">收款单编号：{{waterDetail.waterNo}}</div>
      <div style="display: flex;margin-bottom: 10px;width: 100%">
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>收款类型：</div>
          <div>{{costTypeFmt(null,null,waterDetail.costType,null)}}</div>
        </div>
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>账户类型：</div>
          <div>{{receviceTypeFmt(null,null,waterDetail.receviceType,null)}}</div>
        </div>
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>收款金额：</div>
          <div>{{NumFmt(null,null,waterDetail.balance,null)}}元</div>
        </div>
      </div>
      <div style="display: flex;margin-bottom: 10px;width: 100%">
        <div style="display: flex;font-size: 20px;width: 30%" v-if="waterDetail.receviceType == 3">
          <div>票种：</div>
          <div>{{ticketTypeFmt(null,null,waterDetail.ticketType,null)}}</div>
        </div>
        <div style="display: flex;font-size: 20px;width: 30%" v-if="waterDetail.receviceType == 3">
          <div>到期时间：</div>
          <div>{{myFmtDateTime(waterDetail.expireDate,"YYYY-MM-DD")}}</div>
        </div>
      </div>

      <el-table
        ref="singleTable"
        :data="showAccountlist"
        highlight-current-row
        style="width: 100%">
        <el-table-column fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':'承兑账户'}"/>
        <el-table-column align="center" property="accountName" label="账户名称" width="120px"></el-table-column>
        <el-table-column align="center" property="bankName" label="银行名称"></el-table-column>
        <el-table-column align="center" property="accountNumber" label="账户号"></el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <div style="font-size: 20px;font-weight: 500;margin-bottom: 5px">收款说明：</div>
        <el-input type="textarea" disabled v-model="waterDetail.remark" placeholder="暂无说明"></el-input>
      </div>
    </el-dialog>

    <el-dialog
      class="xuanxiang"
      title="查看财务截图"
      :visible.sync="showPicApply"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal=false
    >
      <img style="width: 100%;height: 100%;" :src="picUrl"/>
    </el-dialog>
  </div>
</template>

<script>
    import PanelSelection from '../../../components/PanelSelection/panelSelection'
    import SimplePanelSelection from '../../../components/PanelSelection/simplePanelSelection'
    import {
      getReceive,
      getReceiveWaterBusiness, updateReceiveWater,
    } from "../../../api/system/receiveWater";
    import {selectbyId} from "../../../api/system/sysAccount";
    import {getDictionaryList} from "../../../api/system/baseInit";
    import dayjs from "dayjs";
    import {fmtDictionary} from "../../../utils/util";
    import { UPLOAD_URL } from '@/utils/config'
    import {getContract, saveContract} from "../../../api/business/sysContractapi";
    export default {
        name: "businessReceiveHesheng",
        components: {PanelSelection,SimplePanelSelection},
        data() {
            return {
              isAddContract: false,
              upload: UPLOAD_URL,
              applyStatusList:[
                {
                  code:0,
                  value:"未核销"
                },
                {
                  code:1,
                  value:"已核销"
                },
              ],
              queryParams:{
                receiveType:null,
                receiveCompany:"海南和盛",
                customer:null,
                receiveStatus:0,
                createTime:null,
                pageSize:10,
                pageNo:1,
                costType:1,
              },
              ticketTypeList:[
                {code:1,value:'银行承兑'},
                {code:2,value:'商业承兑'},
              ],
              typeList:[],
              total:0,
              loading:false,
              dataList:[],
              showApply:false,
              switchComp: {
                costType:null,
                show: true,
                balance: 0,
                customerId: undefined,
                loading: false,
                topUpRemark: undefined,
                customerName: undefined,
                typeSelect:0,
                receiveCompany:null,
                receviceCompanyName:null,
                receiveType: undefined,
                ticketNumber:null,
                paymentBank:null,
                paymentAccount:null,
                receiveBank:null,
                receiveAccount:null,
                ticketType:null,
                expireDate:null,
              },
              receiveCompanyList:[],
              allCompanyList:[],
              plateList:[],
              accountlist:[],
              customerList:[],
              supplierList:[],
              fdbizCustomerList:[],
              paymentTypeList:[],
              currentRow:null,
              dictionaryLists:[],
              submitObj:{},
              tableData:[],
              applicationForm:{},
              showDetailApply:false,
              receiveWater:{},
              verificationInfo:{
                shipName:null,
                voyageNum:null,
                settleTonnage:null,
                price:null,
                demurrage:null,
              },
              rules:{
                shipName:[
                  {required: true,  message: '请输入船名', trigger: 'blur'},
                ],
                voyageNum:[
                  {required: true,  message: '请输入航次号', trigger: 'blur'},
                ],
              },
              showAccount:false,
              waterDetail:{},
              showAccountlist:[],
              picList:[],
              fileList:[],
              uploadUrl: UPLOAD_URL,
              dialogImageUrl: '',
              dialogVisible: false,
              showPicApply:false,
              picUrl:null,
              contractsList:[],
              contractSelect:{},
              contract:{},
            };
        },
      created() {
        var getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuex')
        var getCustomer = this.$store.dispatch('data/getCustomerListSaveInVuex')
        var getFdbizCustomer = this.$store.dispatch('data/getFdbizCustomerListSaveInVuex')
        var getCompany = this.$store.dispatch('data/getCompanyListSaveInVuex')
        var getPlate = this.$store.dispatch('data/getPlateListSaveInVuex')
        var dic = getDictionaryList("contract_company,settlement_type,fdbiz_cost_type")
        Promise.all([getSupplier,getCustomer,dic,getCompany,getFdbizCustomer,getPlate]).then(values => {
          let res  = values[0];
          if(res){
            this.supplierList = res.supplierList
          }
          res  = values[1];
          if(res){
            this.customerList = res.customerList
          }
          res  = values[2];
          if (res) {
            this.dictionaryLists = res.map
            this.receiveCompanyList = res.map['contract_company']
            this.paymentTypeList = res.map['settlement_type']
            this.typeList = res.map['fdbiz_cost_type']
          }
          res  = values[3];
          if (res) {
            this.allCompanyList = res.companyList
          }
          res  = values[4];
          if (res) {
            this.fdbizCustomerList = res.fdbizCustomerList
          }
          res  = values[5];
          if (res) {
            this.plateList = res.plateList
          }
          this.topage()
        })
      },
      methods: {
        updataFileList(){
          this.fileList = []
        },
        selectContract(){
          var obj = JSON.parse(this.contractSelect.id)
          this.fileList = obj
        },
        saveInfo(){
          console.info(this.contract)
          if(this.isAddContract){
            if(!this.contract.contractname){
              this.$message.error('请输入合同编号')
              return
            }
            console.info(this.fileList)
            if(!this.fileList || this.fileList.length<=0){
              this.$message.error('请上传合同')
              return
            }
            var str = JSON.stringify(this.fileList)
            saveContract(this.contract.contractname,1,this.receiveWater.sysCustomerId,"1456194134325854235",str).then(res =>{
              this.contract.contractId = res.id.toString()
              this.updateReceiveWater()
            })
          } else {
            this.contract.contractId = this.contractSelect.value
            if(!this.contract.contractId){
              this.$message.error('请选择合同')
              return
            }
            this.updateReceiveWater()
          }
        },
        submit(){
          this.$refs["addVerificationInfo"].validate((valid) => {
            if (valid) {
              this.saveInfo();
            } else {
              console.log("error submit!!");
              return false;
            }
          });
        },
        updateReceiveWater(){
          const loading = this.$loading({
            lock: true,
            text: '数据提交中，请稍后！',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          var str = JSON.stringify(this.verificationInfo)
          updateReceiveWater(str,this.receiveWater.id,this.contract.contractId).then(res=>{
            if(res && res.resultCode === '0'){
              loading.close()
              this.$message.success('提交成功')
              this.detailApplyhandleClose()
            }
          })
        },
        handlePreview(file) {
          // let { href } = this.$router.resolve({ path: file.response.url })
          if (file.response.url.indexOf('.png') !== -1 || file.response.url.indexOf('.jpeg') !== -1 || file.response.url.indexOf('.jpg') !== -1) {
            this.dialogImageUrl = file.response.url
            this.dialogVisible = true
          } else {
            window.open(file.response.url, '_blank')
          }
        },
        handleRemove(file, fileList) {
        },
        savefileList(response, file, fileList) {
          console.info(fileList)
          this.fileList = fileList
          if (!response || response.resultCode != 0 || !response.url) {
        // if (!this.fileListg[this.groupindex]) {
        //   this.fileListg[this.groupindex] = []
        // }
        // this.fileListg[this.groupindex].push(file)
              this.fileList.pop()
              this.$message.error('上传失败，请重新上传')
            }
        },
        handleExceed(files, fileList) {
          this.$message.warning(`当前限制选择 1 个文件`)
        },
        beforeRemove(file, fileList) {
          return this.$confirm(`确定移除 ${file.name}？`)
        },
        handleClose(){
          this.showPicApply = false
        },
        showPic(item){
          if(!item.picList){
            this.$message.warning('该流水暂无财务截图!')
            return
          }
          this.picUrl = null
          this.showPicApply = true
          this.picUrl = item.picList
        },
        showAccounthandleClose(){
          this.showAccount = false
        },
        openShowAccount(obj){
          console.info(obj)
          this.waterDetail = obj
          if(obj.accountId){
            selectbyId(obj.accountId).then(res =>{
              if (res) {
                this.showAccountlist = res.list
              }
            })
          }
          this.showAccount = true
        },
        showDetail(obj){
          console.info(obj)
          this.tableData = []
          this.receiveWater = obj
          this.showDetailApply = true
          this.getContractList('')
        },
        getContractList(name){
          getContract(name,1,this.receiveWater.sysCustomerId,"1456194134325854235").then(res=>{
            this.contractsList = res.data
            console.info(this.contractsList)
          })
        },
        detailApplyhandleClose(){
          this.showDetailApply = false
          this.receiveWater = {}
          this.topage()
        },
        topage(){
          this.loading = true;
          var query = {
            receiveCompany:this.queryParams.receiveCompany,
            customer: this.queryParams.customer,
            createTime: this.queryParams.createTime,
            receiveStatus:this.queryParams.receiveStatus,
            receiveType:this.queryParams.receiveType,
            pageNo: this.queryParams.pageNo,
            pageSize: this.queryParams.pageSize
          }
          getReceiveWaterBusiness(query).then(response => {
            let data = response.page;
            this.dataList = data.records;
            this.total = data.total;
          }).finally(() => {
            this.loading = false;
          });
        },
        eventPage(e){
          this.queryParams.pageNo = e
          this.topage()
        },
        ApplyhandleClose(){
          this.showApply = false
          this.topage()
        },
        handleCurrentChange(val){
          this.currentRow = val;
        },
        costTypeQuery(item){
          this.queryParams.costType = item.code
        },
        applyStatusQuery(item){
          let id = item.code
          if(this.queryParams.receiveStatus == id){
            this.queryParams.receiveStatus=null
          } else {
            this.queryParams.receiveStatus = id
          }
        },
        receiveTypeQuery(item){
          let id = item.code
          console.log(id)
          console.log(item.value)
          if(this.queryParams.receiveType == id){
            this.queryParams.receiveType=null
          } else {
            this.queryParams.receiveType = id
          }
        },
        myFmtDateTime(cellValue, fmtstr) {
          if (cellValue == undefined || cellValue == null || cellValue == "") {
            return "--";
          }
          return dayjs(cellValue).format(fmtstr);
        },
        costTypeFmt(row,column,cellValue,index) {
          return fmtDictionary(cellValue,this.dictionaryLists['fdbiz_cost_type']);
        },
        receviceTypeFmt(row,column,cellValue,index) {
          return fmtDictionary(cellValue,this.dictionaryLists['settlement_type']);
        },
        receviceCompanyFmt(row,column,cellValue,index) {
          return fmtDictionary(cellValue,this.dictionaryLists['contract_company']);
        },
        publicFmt(row,column,cellValue,index){
          var v = '--'
          if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
            v = cellValue
          }
          return v
        },
        NumFmt(row,column,cellValue,index){
          var v = 0
          if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
            v = parseFloat(Math.abs(cellValue).toFixed(2)) && parseFloat(Math.abs(cellValue).toFixed(2)).toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
          }
          return v
        },
        paymentTypeFmt(row,column,cellValue,index){
          return fmtDictionary(cellValue,this.dictionaryLists['settlement_type']);
        },
        ticketTypeFmt(row,column,cellValue,index){
          return fmtDictionary(cellValue,this.ticketTypeList);
        },
      }
    }
</script>

<style scoped>
  .width{
    width: 100%;
  }
  .query-form{
    text-align: left;
    padding-top: 10px;
  }
  .sp_add_left{
    width: 45%;
    margin-right: 5%;
  }
  .sp_add_right{
    width: 45%;
  }
  .el-table-info >>> .cell{
    text-align: center;
  }
  .el-table-info >>> th {
    background: #EDF5FF;
  }
  .hiddenClass >>> .el-upload {
    display: none;
  }
  .upload-demo{
    display: flex;
  }
</style>
