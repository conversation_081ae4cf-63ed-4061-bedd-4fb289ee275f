<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px;height: calc(100vh - 85px);">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      船舶业务付款台账
    </div>
    <el-divider/>
    <vxe-toolbar
      export
      custom
      print
      ref="xToolbar"
    >
      <template slot="buttons">
        <div style="text-align: center;">
          <el-form :inline="true"  :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline" >
            <el-form-item label="船舶:" >
              <el-select v-model="queryForm.shipName" placeholder="船舶" @change="onQuery">
                <el-option v-for="(item,index) in shipList" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间:">
              <el-date-picker
                v-model="queryForm.businessTime"
                clearable
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="onQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button @click="onQuery" type="primary">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </vxe-toolbar>
    <!-- @sort-change="sortChangeEvent3"
    :sort-config="{remote:true}" -->
    <div id="yewuzongbiao" style="height: calc(100vh - 250px);">
      <vxe-table
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        ref="xTable"
        border
        align="center"
        :print-config="{}"
        :tree-config="{transform: true, rowField: 'id', parentField: 'parentId',expandAll: true}"
        height="auto"
        auto-resize
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '船舶业务付款台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
      >
        <!-- <vxe-table-column field="shipName" :formatter="publicFmtVxe" title="船舶" align="center" /> -->
        <vxe-table-column field="label" tree-node :formatter="publicFmtVxe" title="费用类型" align="left" />
        <vxe-table-column field="codeName" :formatter="publicFmtVxe" title="部门" align="center" />
        <vxe-table-column field="money" :formatter="publicFmtnumber" title="付款金额" align="center" >
          <template slot-scope="scope">
            <span :class="isSel(scope.row.parentPathNames||scope.row.label)?'':'selTxt'" @click="toDetail(scope.row.parentPathNames||scope.row.label)">{{ publicFmtnumber({cellValue:scope.row.money }) }}</span>
          </template>
        </vxe-table-column>
        <!-- 明细 -->
        <!-- <vxe-table-column field="payee" :formatter="publicFmtVxe" title="付款单位" align="center"/> -->
        <!-- <vxe-table-column field="shipIsPay" :formatter="publicFmtVxe" title="是否船上支付" width="80" align="center"/> -->

        <!-- <vxe-table-column field="createByName" :formatter="publicFmtVxe" title="申请人" width="120" align="center"/> -->

        <!-- <vxe-table-column title="明细" align="center" width="150">
          <template slot-scope="scope">
            <div>
              <span v-for="(sitem,sidx) in scope.row.feeDetail" :key="sidx">{{ sitem.feeType }}<span v-show="sitem.feeRemark">({{ sitem.feeRemark }})</span>/{{ sitem.feeMoney }}</span>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column field="createTime" :formatter="publicFmtVxe"  width="100" title="创建时间" align="center"/>
        <vxe-table-column field="remark" :formatter="publicFmtVxe" title="备注" align="center"/> -->
        <!-- <vxe-table-column title="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button @click="showshenpi(scope.row.processId)" type="text" size="small">查看</el-button>

          </template>
        </vxe-table-column> -->
      </vxe-table>
    </div>
    <!-- <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    /> -->
    <!-- <ProgressComponent ref="processDrawerqingjia" style="z-index:9999"   /> -->
    </section>
</template>
<script>
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
import { getLsShipList,getLsShipPaySumList } from '@/api/system/process'
import dayjs from 'dayjs'
// import ProgressComponent from '@/components/workflow/process'
import currency from 'currency.js';

export default {
  name: 'lsShipPayStatisticsSum',
  data() {
    return {
      tableData: [],
      shipList: [],
      total: 0,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        shipName: '',
        feeType: '',
        // 每月3号后 显示当月数据，3号前显示上月数据
        businessTime: dayjs().date() > 3 ? [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')] : [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')],
        createByName: ''
      },
      tableLoading: true

    }
  },
  created() {
    // 从路由中获取查询条件
    if(this.$route.query.shipName){
      this.queryForm.shipName = this.$route.query.shipName
    }
    if(this.$route.query.startTime){
      this.queryForm.businessTime = [this.$route.query.startTime,this.$route.query.endTime]
    }
    // pageNum

    this.loadShipList()
    // this.getList()
  },
  methods: {
    isSel(typeFee) {
      return typeFee == '合计'
    },
    toDetail(typeFee) {
      if (this.isSel(typeFee)) {
        return
      }
      // 路径区分
      // 如果 lsShipPayStatisticsSumOa 则 跳lsShipPayStatisticsDetail
      // 如果 lsShipPayStatisticsSum 则 跳lsShipPayStatistics
      // 费用类型，船舶，时间
      this.$router.push({

        path: this.$route.name == 'lsShipPayStatisticsSumOa' ? '/lsShipPayStatisticsDetail' : '/lsShipPayStatistics',
        query: {
          typeFee,
          shipName: this.queryForm.shipName,
          startTime: this.queryForm.businessTime[0],
          endTime: this.queryForm.businessTime[1]
        }
      })
    },
    loadShipList() {
      getLsShipList().then(res => {
        this.shipList = res.data
        if (this.shipList && this.shipList.length > 0) {
          this.queryForm.shipName = this.shipList[0].label
          this.getList()
        }
      })
    },
    onQuery() {
      // 保存查询条件
      // queryForm.businessTime
      // queryForm.shipName
      this.$router.replace({
        path: this.$route.path,
        query: {
          shipName: this.queryForm.shipName,
          startTime: this.queryForm.businessTime[0],
          endTime: this.queryForm.businessTime[1]
        }
      })
      this.topage(1)
    },
    eventPage(e) {
      this.topage(e)
    },
    topage(page) {
      this.queryForm.pageNum = page
      this.getList()
    },
    showshenpi(id, name) {
      if (!id){
        this.$message({
          message: '该流程暂无审批详情',
          type: 'warning'
        });
        return;
      }
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.doInit()
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = currency(cellValue,{separator:',',symbol:''}).format()

      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    sortChangeEvent3({ sortList, column, property, order }) {
      console.log('sortList', sortList, column, property, order)
      this.queryForm.sort = property
      this.queryForm.order = order
      this.topage(1)
    },
    getList() {
      this.tableLoading = true
      // getLsProcessList({
      //   shipName: this.queryForm.shipName, feeType: this.queryForm.feeType,
      //   startDate: this.queryForm.businessTime && this.queryForm.businessTime.length === 2 ? this.queryForm.businessTime[0] : undefined,
      //   endDate: this.queryForm.businessTime && this.queryForm.businessTime.length === 2 ? this.queryForm.businessTime[1] : undefined,
      //   createByName: this.queryForm.createByName,
      //   pageNum: this.queryForm.pageNum,
      //   pageSize: this.queryForm.pageSize
      // }).then(res => {
      //   console.log('res', res)
      //   this.tableData = res.data
      //   this.total = res.total
      // }).finally(() => {
      //   this.tableLoading = false
      // })

      getLsShipPaySumList({
        shipName: this.queryForm.shipName,
        feeType: this.queryForm.feeType,
        startDate: this.queryForm.businessTime && this.queryForm.businessTime.length === 2 ? this.queryForm.businessTime[0] : undefined,
        endDate: this.queryForm.businessTime && this.queryForm.businessTime.length === 2 ? this.queryForm.businessTime[1] : undefined }).then(res => {
        if (res.resultCode == 0) {
          this.tableData = res.data
        }
      }).finally(() => {
        this.tableLoading = false
        this.$nextTick(() => {
          this.$refs.xTable.setAllTreeExpand(true)
        })
      })
    }
  }
}
</script>
<style scoped>
.selTxt{
  color:rgb(64, 158, 255);cursor: pointer;
}
</style>
