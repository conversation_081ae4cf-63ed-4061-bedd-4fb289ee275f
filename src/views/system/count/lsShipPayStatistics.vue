<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      船舶业务付款台账
    </div>
    <el-divider/>
    <div>
      <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
        <el-form-item label="船舶:" >
          <el-input v-model="queryForm.shipName" type="text" placeholder="船舶" clearable />
        </el-form-item>
        <el-form-item label="费用类型:">
          <el-input v-model="queryForm.feeType" type="text" placeholder="费用类型" clearable/>
        </el-form-item>
        <el-form-item label="模糊查询">
          <!-- 模糊查询 switch-->
          <el-switch
            v-model="queryForm.isLikeQuery"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="开"
            inactive-text="关"
            @change="onQuery"
          />
        </el-form-item>
        <el-form-item label="时间:" >
        <el-date-picker
          clearable
          v-model="queryForm.businessTime"
          @change="onQuery"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="申请人:">
        <el-input v-model="queryForm.createByName" type="text" placeholder="申请人" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button @click="onQuery" type="primary">查询</el-button>
      </el-form-item>
      </el-form>
    </div>
    <vxe-toolbar
      export
      custom
      print
      ref="xToolbar"
    >
    </vxe-toolbar>
    <!-- @sort-change="sortChangeEvent3"
    :sort-config="{remote:true}" -->
    <div id="yewuzongbiao">
      <vxe-table
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        ref="xTable"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        show-footer
        :footer-method="footerMethod"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '船舶业务付款台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column field="shipName" :formatter="publicFmtVxe" title="船舶" align="center" width="100"/>
        <vxe-table-column field="feeType" :formatter="publicFmtVxe" title="费用类型" align="center"/>

        <!-- 明细 -->
        <vxe-table-column field="payee" :formatter="publicFmtVxe" title="付款单位" align="center"/>
        <vxe-table-column field="shipIsPay" :formatter="publicFmtVxe" title="是否船上支付" width="80" align="center"/>

        <vxe-table-column field="createByName" :formatter="publicFmtVxe" title="申请人" width="120" align="center"/>

        <vxe-table-column title="明细" align="center" width="150">
          <template slot-scope="scope">
            <div>
              <span v-for="(sitem,sidx) in scope.row.feeDetail" :key="sidx">{{ sitem.feeType }}<span v-show="sitem.feeRemark">({{ sitem.feeRemark }})</span>/{{ sitem.feeMoney }}</span>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column field="createTime" :formatter="publicFmtVxe"  width="100" title="创建时间" align="center"/>
        <vxe-table-column field="remark" :formatter="publicFmtVxe" title="备注" align="center"/>
        <vxe-table-column field="money" :formatter="publicFmtnumber" title="付款金额"  align="center" width="100"/>
        <vxe-table-column title="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button @click="showshenpi(scope.row.processId)" type="text" size="small">查看</el-button>

          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />
    <ProgressComponent ref="processDrawerqingjia" style="z-index:9999"   />
    </section>
</template>
<script>
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
import { getLsProcessList,getLsShipPaySumList } from '@/api/system/process'
import dayjs from 'dayjs'
import ProgressComponent from '@/components/workflow/process'
import currency from 'currency.js';

export default {
  name: 'lsShipPayStatistics',
  components: {
    ProgressComponent
  },
  data() {
    return {
      tableData: [],
      total: 0,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        isLikeQuery: true,
        shipName: '',
        feeType: '',
        // 每月3号后 显示当月数据，3号前显示上月数据
        businessTime: dayjs().date() > 3 ? [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')] : [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')],
        createByName: ''
      },
      tableLoading: false,
    }
  },
  created() {
    // 费用类型，船舶，时间
    const route = this.$route
    if (route.query.typeFee) {
      this.queryForm.feeType = route.query.typeFee
      this.queryForm.isLikeQuery = false
    } else {
      this.queryForm.isLikeQuery = true
    }
    if (route.query.shipName) {
      this.queryForm.shipName = route.query.shipName
    }
    if (route.query.startTime) {
      this.queryForm.businessTime[0] = route.query.startTime
    }
    if (route.query.endTime) {
      this.queryForm.businessTime[1] = route.query.endTime
    }
    this.getList()
  },
  methods: {
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count = currency(count).add(item[field]).value
      })
      return count
    },
    footerMethod({ columns, data }) {
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (['money'].includes(column.property)) {
            return this.sumNum(data, column.property)
          }
          return null
        })
      ]
    },
    onQuery() {
      this.topage(1)
    },
    eventPage(e) {
      this.topage(e)
    },
    topage(page) {
      this.queryForm.pageNum = page
      this.getList()
    },
    showshenpi(id, name) {
      if (!id){
        this.$message({
          message: '该流程暂无审批详情',
          type: 'warning'
        });
        return;
      }
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.doInit()
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = currency(cellValue,{separator:',',symbol:''}).format()

      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    sortChangeEvent3({ sortList, column, property, order }) {
      console.log('sortList', sortList, column, property, order)
      this.queryForm.sort = property
      this.queryForm.order = order
      this.topage(1)
    },
    getList() {
      this.tableLoading = true
      getLsProcessList({
        shipName: this.queryForm.shipName, feeType: this.queryForm.feeType,
        startDate: this.queryForm.businessTime && this.queryForm.businessTime.length === 2 ? this.queryForm.businessTime[0] : undefined,
        endDate: this.queryForm.businessTime && this.queryForm.businessTime.length === 2 ? this.queryForm.businessTime[1] : undefined,
        createByName: this.queryForm.createByName,
        pageNum: this.queryForm.pageNum,
        pageSize: this.queryForm.pageSize,
        isLikeQuery: this.queryForm.isLikeQuery
      }).then(res => {
        console.log('res', res)
        this.tableData = res.data
        this.total = res.total
      }).finally(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style scoped>

</style>
