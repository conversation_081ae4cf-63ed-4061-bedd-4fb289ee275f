<template>
  <div>
    <div v-if="accounts && accounts.length">
      <div class="my-10 text-right">
        选择帐套: <el-radio-group v-model="accountRadio" @input="changeLedgerAccount()">
                  <el-radio v-for="item in accounts" :label="item.code">{{item.name}}</el-radio>
              </el-radio-group>
      </div>


      <el-table
        :data="updatedTableData"
        header-cell-class-name="custom-header"
        cell-class-name="custom-cell"
        border
        highlight-current-row
        style="width: 100%">
        <el-table-column
          prop="summary"
          label="摘要"
          >
          <template #default="{ row }">
            <el-input  type="textarea" class="custom-textarea"
            rows="2"
            v-model="row.summary"  placeholder="请输入摘要"></el-input>
          </template>
        </el-table-column>
        <el-table-column
          label="科目"
          width="380"
          >
          <template #default="{ row,$index }">
            <!-- {{ showSelect[$index] }} -->
              <!-- <el-input v-model="row.subject" placeholder="请输入科目"></el-input> -->
              <!-- // classList 是科目列表，row.subject 是当前行的科目值 -->
              <!-- <el-input v-show="!showSelect[$index]" type="textarea" autosize v-model="row.subject" readonly  @focus="toggleSelect($index)"  placeholder="请选择科目">{{ labelSub(row.subject) }}</el-input>

              <el-dialog
                title="提示"
                :visible.sync="showSelect[$index]"
                append-to-body
                width="30%"
                >
                  <el-table
                  :data="classList"
                  style="width: 100%"
                  :row-class-name="tableRowClassName">

                  <el-table-column
                    label="科目"
                    >
                    <template #default="{ row }">
                      {{ row.glAccountCode+' '+row.longName }}
                    </template>
                  </el-table-column>

                </el-table>
                <span slot="footer" class="dialog-footer">
                  <el-button @click="showSelect[$index] = false">取 消</el-button>
                  <el-button type="primary" @click="showSelect[$index] = false">确 定</el-button>
                </span>
              </el-dialog> -->
              <!-- v-show="showSelect[$index]" -->
               <!-- :ref="'elSelect'+$index" -->
                <el-select :ref="codeKey+'elSelect'+$index" :key="codeKey+'elSelect'+$index" :data-key="codeKey+'elSelect'+$index" class="custom-input" v-model="row.subject" style="width: 100%;" default-first-option filterable placeholder="请选择科目">
                  <el-option  v-for="c in classDataList" :value="c.glAccountCode" :label="c.glAccountCode+' '+c.longName">
                  </el-option>
                </el-select>
          </template>
        </el-table-column>

        <el-table-column
          label="借方金额"
          width="208">
          <template #header="{column, $index}">
            <div>
              <div style="border-bottom: 1px solid #dcdfe6;">借方金额</div>
              <div class="flexaround money-cls">
                <span>百</span>
                <span>十</span>
                <span>亿</span>
                <span>千</span>
                <span>百</span>
                <span>十</span>
                <span>万</span>
                <span>千</span>
                <span>百</span>
                <span>十</span>
                <span>元</span>
                <span>角</span>
                <span>分</span>
              </div>
            </div>
          </template>
          <template #default="{ row,$index }">
            <div v-show="!showSelect[$index]" @click="toggleSelect($index)" class="custom-money-fmt money-cls flexaround" v-html="fmtMoneyNoDe(row.debit)"></div>
            <el-input-number v-show="showSelect[$index]"   :ref="'debitRef'+$index"   v-model="row.debit" class="custom-input custom-money" @change="upSumBit(row.debit,row.money,'debit',$index)" @blur="toggleSelect($index)" :step="1" :controls="false" ></el-input-number>
          </template>
        </el-table-column>

        <el-table-column
          label="贷方金额"
          width="208">
          <template #header="{column, $index}">
            <div>
              <div style="border-bottom: 1px solid #dcdfe6;">贷方金额</div>
              <div class="flexaround money-cls">
                <span>百</span>
                <span>十</span>
                <span>亿</span>
                <span>千</span>
                <span>百</span>
                <span>十</span>
                <span>万</span>
                <span>千</span>
                <span>百</span>
                <span>十</span>
                <span>元</span>
                <span>角</span>
                <span>分</span>
              </div>
            </div>
          </template>
          <template #default="{ row,$index }">
            <div v-show="!showCreditSelect[$index]" @click="toggleCreditSelect($index)" class="custom-money-fmt money-cls flexaround" v-html="fmtMoneyNoDe(row.credit)"></div>
            <el-input-number v-show="showCreditSelect[$index]" v-model="row.credit" :ref="'creditRef'+$index" class="custom-input custom-money" @change="upSumBit(row.credit,row.money,'credit',$index)" @blur="toggleCreditSelect($index)" :step="1" :controls="false" ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          header-align="center"
          width="200"
          align="center"
        >
        <template #default="{ row,$index }">
          <el-button @click="delChild($index)" class="el-icon-remove"  size="mini">删除</el-button>
          <el-button @click="addChild(row,$index)" class="el-icon-plus"   size="mini">添加</el-button>
        </template>
      </el-table-column>
        <!-- <el-table-column
          prop="credit"
          label="总金额"
          width="150">
          <template #default="{ row }">
            <span>{{ row.money}}</span>
          </template>
        </el-table-column> -->
      </el-table>

      <div class="total">
        <span>合计:</span>
        <span >借方金额: {{ totalDebit }}</span>
        <span >贷方金额: {{ totalCredit }}</span>
      </div>
    </div>
    <div v-else class="my-10">
      未配置帐套
    </div>
  </div>
</template>

<script>
import {getHaokjSub} from '@/api/business/processapi'
import currency from 'currency.js'
export default {
  props: {
    tableData: {
      type: Array,
      required: true,
    },
    classList: {
      type: Array,
      required: false,
    },
    codeKey: {
      type: String,
      default: '',
      required: false,
    },
    accounts: {
      type: Array,
      default: () => [],
      required: true,
    },
    paymentType: {
      type: Number,
      default: null,
    }
  },
  watch: {
    tableData: {
      handler(newVal) {
         if(this.handlerTable){
          this.handlerTable=false
          return
        }
        this.handlerTable=true
        this.updatedTableData = JSON.parse(JSON.stringify(newVal));
        this.upSubIdByFind()
      },
      deep: true,
    },
    updatedTableData: {
      handler(newVal) {
        if(this.handlerTable){
          this.handlerTable=false
          return
        }
        this.handlerTable=true
        this.$emit('update:tableData', JSON.parse(JSON.stringify(newVal)));
      },
      deep: true,
    },
    accounts: {
      handler(newVal,oldVal) {
        if(newVal && newVal.length){
          this.accountRadio = newVal[0] && newVal[0].code;
          this.changeLedgerAccount()
        }
      },
      deep: true,
      immediate: true,
    },
    classList: {
      handler(newVal) {
        this.classDataList = newVal
      },
      deep: true,
      immediate: true,
    }
    // updatedTableData: {
    //   handler(newVal) {
    //     this.$emit('update:tableData', newVal);
    //   },
    //   deep: true,
    // },
  },
  data() {
    return {
      classDataList:[],
      // tableData: [
      //   { summary: '', subject: '', debit: 0, credit: 0 },
      //   // 可以初始化更多行数据
      // ],
      accountRadio:this.accounts[0] && this.accounts[0].code,
      updatedTableData: JSON.parse(JSON.stringify(this.tableData)) ,
      showSelect: {},
      showCreditSelect: {},
      selectedValue: {},
      inputValue:{},

    };
  },
  computed: {
    totalDebit() {
      // return this.tableData.reduce((sum, row) => sum + Number((row.debit||0)), 0);
      // 财务格式 currency 222,333.00
      return currency(this.updatedTableData.reduce((sum, row) => sum + Number((row.debit||0)), 0),{symbol:''}).format();
    },
    totalCredit() {
      return currency(this.updatedTableData.reduce((sum, row) => sum + Number((row.credit||0)), 0),{symbol:''}).format();
    },
  },
  methods: {
    delChild(index){
      this.updatedTableData = this.updatedTableData.splice(index,1)
    },
    addChild(item,index){
      const n = Object.assign({},item,{
        debit:0,
        credit:0,
      })
      const i = index+1
      this.updatedTableData = this.updatedTableData.splice(i,0,n)
    },
    loadKemuByCode(){

    },
    fmtMoneyNoDe(v) {
      let res = '';
      try {
        if (v) {
          res= currency(v, { separator: '', symbol: '', decimal: '' }).format();
        }
      } catch (error) {
      }
      return this.fmtHtmlMoney(res)
    },
    fmtHtmlMoney(v) {
      const count = 13;
      let res = '';
      if (!v) {
        for (let i = 0; i < count; i++) {
          res += `<span></span>`;
        }
        return res;
      }
      // v = 2399  09 10 11 12
      const vlen = v.length; // 4


      for (let i = 0; i < count; i++) {
        res += `<span>${v.charAt(vlen - count + i) || ''}</span>`;
      }
     return res;

      // return ` <span>百</span>
      //         <span>十</span>

      //         <span>亿</span>
      //         <span>千</span>

      //         <span>百</span>
      //         <span>十</span>

      //         <span>万</span>
      //         <span>千</span>

      //         <span>百</span>
      //         <span>十</span>

      //         <span>元</span>
      //         <span>角</span>
      //         <span>分</span>`
    },
    upSumBit(v, money,key,idx) {
      if (v > money) {
        this.$message.error('金额不能大于' + money);
        // v = money;
        this.updatedTableData[idx][key] = money;
      }
      // if(key === 'debit')
      //   this.toggleSelect(idx)
      // if(key === 'credit')
      //   this.toggleCreditSelect(idx)
    },
    toggleCreditSelect(idx) {
      this.showCreditSelect[idx] = !this.showCreditSelect[idx];
      this.showCreditSelect = Object.assign({}, this.showCreditSelect);
      if (this.showCreditSelect[idx]) {
        this.$refs['creditRef' + idx].focus();
      }
    },
    toggleSelect(idx) {
      this.showSelect[idx] = !this.showSelect[idx];
      this.showSelect = Object.assign({}, this.showSelect);
      if (this.showSelect[idx]) {
        // this.$refs['debitRef'+idx].
        // input focus
        // console.log('f',this.$refs['debitRef'+idx].value,this.$refs['debitRef'+idx])
        this.$refs['debitRef'+idx].focus();
      }
      // console.log(this.$refs['elSelect'+idx])
      // this.$refs['elSelect'+idx].focus();
    },
    labelSub(subid) {
      if (this.classDataList) {
        return subid + ' ' +this.classDataList.find(item => item.glAccountCode === subid).longName
      }
      return subid
    },
    // updateCurrentData() {
    //   this.$emit('update:tableData', this.updatedTableData);
    // },
    changeLedgerAccount() {
      this.$emit('ledgerAccount', this.accountRadio);
      this.classDataList = []
      getHaokjSub(this.accountRadio).then(res=>{
        if (res && res.data) {
          this.classDataList = res.data
          this.upSubIdByFind()
        }
      })
    },
    upSubIdByFind() {
      if (this.updatedTableData && this.updatedTableData.length > 0 && this.classDataList && this.classDataList.length > 0) {
        console.log('353-updata',this.updatedTableData)
        // updatedTableData 中 findSub 获取 classDataList glAccountName 相等 ，treePath(2241^********^) = subject (2241)^
        //updatedTableData -> findSub,subject
        // classDataList -> glAccountName,treePath, glAccountCode
         // 承兑 最后一个科目改成1121
         if (3 == this.paymentType) { // 承兑
          this.updatedTableData[this.updatedTableData.length - 1].findSubId = '1121'
          this.updatedTableData[this.updatedTableData.length - 1].subject = '1121'
         }
          if (1 == this.paymentType) { // 现金
            this.updatedTableData[this.updatedTableData.length - 1].findSubId = '1001'
            this.updatedTableData[this.updatedTableData.length - 1].subject = '1001'
          }

        for (let i = 0; i < this.updatedTableData.length; i++) {
          if(this.updatedTableData[i].querySubject){
            const qySub = this.updatedTableData[i].querySubject
            let fundsCode
            if(this.updatedTableData[i].findSub && qySub.endsWith('_')){
               fundsCode = this.classDataList.find(item => item.longName === qySub+this.updatedTableData[i].findSub)
            }
            if(!fundsCode){
              if(qySub.endsWith('_')){
                const qySubs = qySub.slice(0,-1)
                fundsCode = this.classDataList.find(item => item.longName === qySubs)
              }else{
                fundsCode = this.classDataList.find(item => item.longName === qySub)
              }
            }
            if (fundsCode) {
                this.updatedTableData[i].findSubId = fundsCode.glAccountCode
                this.updatedTableData[i].subject = fundsCode.glAccountCode
                continue
            }

          }
           if (this.updatedTableData[i].findSub && !this.updatedTableData[i].findSubId) {
            try {
              const fundsCode = this.classDataList.find(item => item.treePath.startsWith(this.updatedTableData[i].subject + '^')  && item.glAccountName == this.updatedTableData[i].findSub)
              if (fundsCode) {
                this.updatedTableData[i].findSubId = fundsCode.glAccountCode
                this.updatedTableData[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }



      }
    },
  },
};
</script>
<style>
.custom-cell{
  padding: 0 !important;
}
.custom-cell .cell{
  padding: 0 !important;
}
.custom-header {
    background-color: #3FC8DD !important;
    color: #fff;
    padding: 0 !important;
}
.custom-header .cell {
  text-align: center;
  padding: 0 !important;
}
/* 默认状态下不显示边框 */
.custom-textarea textarea:not(:focus) {
  border: none;
  outline: none;
}

/* 点击后显示边框 */
.custom-textarea textarea:focus {
  border: 1px solid #dcdfe6;
  outline: none;
}
.custom-input input:not(:focus) {
  border: none;
  outline: none;
}
.custom-money{
  width: 100%;
}
.custom-money input{
  padding: 0 !important;
}
.custom-money input:not(:focus){
  font-size: 15px;
  text-align: right;
}
.custom-money input:focus{
  text-align: left;
}
.custom-money-fmt{
  font-size: 15px;
}
.custom-money-fmt span{
  height: 60px;
  line-height: 60px;
}
.flexaround{
  display: flex;
  justify-content: space-between;
}
.money-cls span{
  width: 15px;
  text-align: center;
}
.money-cls span:not(:last-child){
  border-right: 1px solid #dcdfe6;
}
.money-cls span:nth-last-child(3){
  border-right: 1px solid rgba(245, 96, 89, 0.5);
}
</style>
<style scoped>
.total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
}
.total span {
  margin-right: 10px;
  line-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.total .el-tag {
  margin-left: 5px;
}
.flexcenter{
  display: flex;
  align-items: center;
}
.my-10{
  margin-top: 10px;
  margin-bottom: 10px;
}
.text-right{
  text-align: right;
}

</style>

