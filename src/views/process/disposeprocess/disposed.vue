<template>
  <section style="padding-top: 15px;padding-left: 15px;display: inline-block;height: calc(max(100vh,100%) - 84px); width: 100% ">
    <div class="sp-title" style="font-size: 25px">
     已办流程
    </div>
    <el-divider />
    <el-col :span="12" style="width: 15%;height: 100%">
      <el-menu
        style="height: 100%"
        default-active="999"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose">
        <el-menu-item @click="changemeun('all')" index="999">
          <span slot="title">全部</span>
        </el-menu-item>
        <el-submenu v-for="(item,index) in list" :index="String(index)">
          <template slot="title">
            <span>{{item.name}}</span>
          </template>
          <el-menu-item @click="changemeun(items.spare1)" v-for="(items,indexs) in item.sysProcessDetailList"
                        :index="String(index)+'-'+String(indexs)">
            {{ items.name }}
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </el-col>
    <div style="width: 80%;margin-left: 17%">
      <el-form :inline="true" :model="quary" class="query-form demo-form-inline">
        <el-form-item label="审批时间" label-width="70px">
          <el-date-picker style="margin-top:3px;"
                          v-model="quary.shipTime"
                          type="daterange"
                          range-separator="至"
                          value-format="yyyy-MM-dd"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="状态" label-width="70px">
          <el-select v-model="quary.status" style="width:100px;" placeholder="请选择">
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="进行中"
              value="1"
            />
            <el-option
              label="已结束"
              value="2"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="摘要" label-width="100px">
            <el-input clearable v-model="quary.summary" placeholder="请输入摘要"></el-input>
        </el-form-item>
        <el-form-item label="对外付款账户" label-width="100px">
          <el-input v-model="quary.fukuanName" placeholder="请输入付款账户" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="onQuery" type="primary" >查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="tableLoading"
        class="el-table-info mytable"
        :data="tableData"
        stripe
        style="width: 100%;margin-top: 20px"
        :show-overflow-tooltip="true"
        border
      >
        <el-table-column
          prop="processname"
          :formatter="publicFmt"
          label="审批类型"
          align="center"
          width="200"
        >
        </el-table-column>
        <el-table-column
          prop="briefContent"
          :formatter="publicFmt"
          label="流程摘要"
          align="center"
          width="350"
        >
        <template  slot-scope="scope">
          <div class="pointer" @click="showshenpi(scope.row.id,scope.row.zhanshiname)">
            {{scope.row.briefContent||'--'}}
          </div>
        </template>
        </el-table-column>
        <el-table-column
          prop="globalParam9"
          :formatter="ordercompanynameFmt"
          label="往来公司"
          align="center"
          width="200"
        />
        <el-table-column
          prop="time"
          :formatter="publicFmt"
          label="当前节点"
          align="center"
          width="200"

        >
          <template  slot-scope="scope">
            <div v-if="scope.row.status === '4'"  @click="showshenpi(scope.row.id,scope.row.zhanshiname)">已撤回</div>
            <div v-if="scope.row.status === '2'"  @click="showshenpi(scope.row.id,scope.row.zhanshiname)">已完成</div>
            <div v-if="scope.row.status === '3'"  @click="showshenpi(scope.row.id,scope.row.zhanshiname)">已驳回</div>
            <div v-if="scope.row.status === '1'"  @click="showshenpi(scope.row.id,scope.row.zhanshiname)">{{ scope.row.pname }} <!--
             -->{{Math.floor(scope.row.statusId/10) == 8?'会计做账中':'审批中'}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sponsorTime"
          :formatter="publicFmt"
          label="创建日期"
          align="center"
          width="150"
        >
        </el-table-column>
        <el-table-column
          prop="chulitime"
          :formatter="publicFmt"
          label="处理日期"
          align="center"
          width="150"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
        >
          <template slot-scope="scope">
            <el-button  @click="showshenpi(scope.row.id,scope.row.zhanshiname)" type="text" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="quary.pageSize"
        :current-page="quary.pageNum"
        @current-change="eventPage"
        style="text-align: right;padding: 10px 0px;background-color: #fff"
      >
      </el-pagination>
    </div>
    <ProgressComponent @refresh="refresh" @onCallback="handlerGlobalParams" ref="processDrawerqingjia">
    </ProgressComponent>
  </section>
</template>

<script>
import { getProcessList,getMyprocessed } from '@/api/business/processapi'
import dayjs from "dayjs";
import Avue from '@smallwei/avue'
import ProgressComponent from '@/components/workflow/process'
import Vue from 'vue'
import { getExternalAccount } from '@/api/business/sysExternalAccount'
import prepared from '@/mixins/prepared'

Vue.use(Avue)


export default {
  name: 'disposed',
  mixins: [prepared(2)],
  data() {
    return {
      total:20,

      tableData:[
      ],
      tableLoading:false,
      quary:{
        time:"",
        name:"all",
        pageSize:10,
        pageNum: 1,
        summary: '',
        status: '1',
        fukuanName:''
      },
      radio1:"Form",
      form:{},
      list: [],
      obj:{

      },
      ExternalAccount:{},
      sizeValue:'small',
      data: [
        {
          name:'张三',
          sex:'男'
        }, {
          name:'李四',
          sex:'女'
        }, {
          name:'王五',
          sex:'女'
        }, {
          name:'赵六',
          sex:'男'
        }
      ],
    }
  },
  beforeRouteUpdate(to,from){
    console.log(this.$route.query.type)
    this.getPage()
  },
  created() {
    getProcessList().then(res => {
      if (res !== undefined && res.resultCode === '0') {
        this.list = res.data
      }
      this.prepareSubmit()
    })
    getExternalAccount().then(res => {
      for (const comp of res.data) {
        this.ExternalAccount[comp.id + ''] = comp.companyName
      }
      console.log('加载公司完成')
      this.prepareSubmit()
    })
    this.preparedThen(() => {
      console.log('开始获取数据')
      this.getPage()
    })
  },
  methods: {
    showshenpi(id, name) {
      console.log(id)
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    refresh() {
      // 审批完成后会调用刷新方法
    },
    handlerGlobalParams(globalParams) {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      console.log(globalParams)
    },
    eventPage(e){
      this.quary.pageNum = e;
      this.getPage()
    },
    changemeun(code){
      this.quary.name = code
      console.log(code)
      this.getPage()
    },

    getPage(){
      this.tableData = []
      console.log('query',this.quary)
      getMyprocessed(this.quary).then(res => {
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.page
          console.log(this.tableData)
          this.total = res.total
        }
      })
    },

    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue == undefined || cellValue == null || cellValue == "") {
        return "--";
      }
      return dayjs(cellValue).format(fmtstr);
    },
    publicFmt(row,column,cellValue,index){
      var v = '--'
      if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
        v = cellValue
      }
      return v
    },
    ordercompanynameFmt(row,column,cellValue,index) {
      if (!cellValue || row.flowCode=='paymentApprove') {
        return '--'
      }
      return this.ExternalAccount[cellValue] ? this.ExternalAccount[cellValue] : cellValue
    },
    onQuery(){
      if (
        this.quary.shipTime !== undefined &&
        this.quary.shipTime !== null
      ) {
        this.quary.startTime = this.quary.shipTime[0];
        this.quary.endTime = this.quary.shipTime[1];
      } else{
        this.quary.startTime = undefined
        this.quary.endTime = undefined
      }
      this.quary.pageNum = 1
      this.getPage()
    },
    handleOpen(key, keyPath) {
      console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    }
  },
  components: {
    ProgressComponent
  }
}
</script>

<style scoped>
.input-different{
  width: 200px;
  margin-left:60px;

}
.font{
  font-size:14px;
  color:#7E7E7E;
}
.el-table-info >>> .cell{
  text-align: center;
}
.el-table-info >>> th {
  background: #EDF5FF;
}
.mytable td  {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.mytable td .cell {
  padding-left: 0.1rem;
  padding-right: 0.1rem;
}
.el-table-info >>> .warning-cell{
  color: red;
}
.el-table-info >>> .success-cell{
  color: #6DD400;
}
.pointer{
  cursor: pointer;
color:#1890ff;
}
</style>
