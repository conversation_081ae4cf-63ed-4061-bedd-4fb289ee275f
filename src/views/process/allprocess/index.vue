<template>
  <section style="margin-top: 20px;margin-left: 20px">
    <div v-show="showtype === 0" class="sp-title" style="font-size: 25px">
      新建流程
      <el-button class="sp-nav-item" type="text" style="font-size: 16px;margin-left: 15px;color: #00a0e9" disabled>全部流程 </el-button>
      <span class="sp-nav-item-sple" style="font-size: 16px">|</span>
      <el-button type="text" class="sp-nav-item" style="font-size: 16px;color: black" @click="checklist(1)"> 最近使用</el-button>
    </div>
    <div v-show="showtype === 1" class="sp-title" style="font-size: 25px">
      新建流程
      <el-button type="text" class="sp-nav-item" style="font-size: 16px;margin-left: 15px;color: black" @click="checklist(0)"> 全部流程</el-button>
      <span class="sp-nav-item-sple" style="font-size: 16px">|</span>
      <el-button type="text" class="sp-nav-item" style="font-size: 16px;color: #00a0e9" disabled>最近使用 </el-button>

    </div>
    <el-divider />
    <div
      style="display: grid;
    grid-template-columns: repeat(3, 33.33%);
    grid-row-gap: 100px"
    >
      <div v-for="item in showlist" style="width: 70%;">
        <div style="color:black;font-size: 20px;font-weight: 500;">{{ item.name }}</div>
        <div style="height: 3px;margin-top: 10px" :style="item.color" />
        <div v-for="(item1,j) in item.sysProcessDetailList" >
          <div v-if="item1.pname && indexItems[item.name+item1.pname] == j">
            <div class="pname">
              <el-button type="text" style="color:black;font-size: 19px;font-weight: 500;" @click="itemShow(item.name+item1.pname)" >
                <img src="../../../assets/images/u450.png" style="width: 13px;height: 16px;transition:all 0.3s;" :class="showItems[item.name+item1.pname]?'roto90':''">
                <span>{{ item1.pname }}</span>
              </el-button>
              <div class="box1" />
            </div>
          </div>
          <div v-show="!item1.pname || showItems[item.name+item1.pname]">
            <el-button type="text" style="color:black;font-size: 19px;font-weight: 500;" @click="startProcess(item1)">
              <!-- <img src="../../../assets/images/u450.png" style="width: 13px;height: 16px"> -->
            {{ showItems[item.name+item1.pname]?'&nbsp;&nbsp;':'' }}  <span>{{ item1.name }}</span>
            </el-button>

            <div class="box1" />
          </div>

          <!-- <el-button type="text" style="color:black;font-size: 19px;font-weight: 500;" @click="startProcess(item1)">
            <img src="../../../assets/images/u450.png" style="width: 13px;height: 16px">
            <span>{{ item1.name }}</span>
          </el-button>

          <div class="box1" /> -->
        </div>
      </div>
    </div>
    <ProgressComponent ref="processDrawer1" @refresh="refresh" @onCallback="handlerGlobalParams" />

  </section>
</template>

<script>
var DIC = {
  VAILD: [{
    label: '真',
    value: 'true'
  }, {
    label: '假',
    value: 'false'
  }],
  SEX: [{
    label: '男',
    value: 0
  }, {
    label: '女',
    value: 1
  }]
}
import { getProcessList } from '@/api/business/processapi'
import ProgressComponent from '@/components/workflow/process'
import Avue from '@smallwei/avue'
import Vue from 'vue'
import '@smallwei/avue/lib/index.css'
Vue.use(Avue)

export default {
  components: {
    ProgressComponent
  },
  data() {
    return {
      showtype: 0,
      showlist: [],
      showItems: {},
      indexItems:{},
      radio1: 'Form',
      form: {},
      list: [],
      obj: {

      },
      sizeValue: 'small',
      data: [],
      test: []
    }
  },
  created() {
    getProcessList().then(res => {
      if (res !== undefined && res.resultCode === '0') {
        this.list = res.data
        this.showlist = res.data
        this.usedlist = res.used
        this.setIndexItems()
      }
    })
  },
  methods: {
    itemShow(str) {
      this.$set(this.showItems, str, !this.showItems[str])
    },
    setIndexItems() {
      for (let i = 0; i < this.showlist.length; i++) {
        // name
        const name = this.showlist[i].name
        for (let j = 0; j < this.showlist[i].sysProcessDetailList.length; j++) {
          const pname = this.showlist[i].sysProcessDetailList[j].pname
          // pname
          if (!pname) {
            continue
          }
          if (this.indexItems[name+pname] || this.indexItems[name+pname]==0) {
            continue
          }
          this.$set(this.indexItems, name+pname, j)
        }
      }
    },
    checklist(num) {
      if (num === 1) {
        this.showtype = num
        this.showlist = this.usedlist
      } else if (num === 0) {
        this.showtype = num
        this.showlist = this.list
      }
      this.setIndexItems()
    },
    refresh() {
      // 审批完成后会调用刷新方法
    },
    handlerGlobalParams(globalParams) {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      console.log(globalParams)
    },
    startProcess(data) {
      if (data.spare1 === undefined || data.spare1 === '') {
        this.$message({
          showClose: true,
          type: 'warning',
          message: '该流程尚未维护'
        })
        // var z = JSON.stringify(this.test)
        // console.log(z)
        return
      }
      if (data.spare3) {
        var path = '/process/' + data.spare3
        this.$router.push({ path: path, query: { title: data.name, code: data.spare1, type: data.spare4, processJian: data.spare5 }})
      } else {
        this.$router.push({ path: '/process/SubmitProcess', query: { title: data.name, code: data.spare1, type: data.spare4, processJian: data.spare5 }})
      }
      // this.option = JSON.parse(data.processForm)
      // this.option1 = JSON.parse(data.processCrud)
      // this.$refs.processDrawer.title = data.name
      // this.$refs.processDrawer.drawer = true
      // this.$refs.processDrawer.processCode = "onAccount"
      // this.$refs.processDrawer.globalParams = {}
      // this.$refs.processDrawer.briefContent = ""
      // this.$refs.processDrawer.globalParams.params1 =""
      // this.$refs.processDrawer.doInit()
    },
    emptytChange() {
      this.$message.success('清空方法回调')
    },
    submit() {
      this.$message.success('当前数据' + JSON.stringify(this.obj))
    }
    // tip(){
    //   this.$message.success('自定义按钮');
    // }

  }
}
</script>

<style scoped>
@import "../../../assets/css/list.css";
.box1{
  text-align:center;
  border-bottom:1px dashed #000;
}
.roto90{
  transform:rotate(90deg);

}
</style>
