<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <label class="el-form-item-label">离港时间</label>
      <el-date-picker
        v-model="value2"
        class="filter-item"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />

      <label class="el-form-item-label">二级客户名称</label>
      <el-input v-model="queryParams.kehu" style="width: 200px;" class="filter-item" placeholder="二级客户名称" clearable />

      <label class="el-form-item-label">收款日期</label>
      <el-date-picker
        v-model="queryParams.receiveDate"
        class="filter-item"
        style="width: 200px;"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
      />

      <label class="el-form-item-label">开票日期</label>
      <el-date-picker
        v-model="queryParams.billDate"
        class="filter-item"
        style="width: 200px;"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
      />

      <label class="el-form-item-label">挂账月份</label>
      <el-date-picker
        v-model="queryParams.guaMonth"
        class="filter-item"
        style="width: 200px;"
        type="month"
        value-format="yyyy-MM"
        placeholder="选择日期"
      />
      <label class="el-form-item-label">核算</label>
        <div class="filter-item">
        <el-tag
          v-for="(item,index) in accountingTypeList"
          :key="index"
          :type="queryParams.accountingType==item.value?'success':'info'"
          style="margin-right: 10px;cursor: pointer;margin-top: 7px"
          @click="selectAccountingType(item.value)"
        >
          {{ item.label }}
        </el-tag>
      </div>

      <label class="el-form-item-label">开票状态</label>
      <div class="filter-item">
        <el-tag
          v-for="(item,index) in receiveTypelist"
          :key="index"
          :type="queryParams.iftax==item.code?'success':'info'"
          style="margin-right: 10px;cursor: pointer;margin-top: 7px"
          @click="selectPaymentType(item.code)"
        >
          {{ item.name }}
        </el-tag>
      </div>

      <el-button class="filter-item" size="mini" type="success" icon="el-icon-search" @click="getTableData(1)">查询</el-button>

        <el-button v-if="queryParams.pageSize === 999999" class="filter-item" size="mini" type="primary" @click="returnPageSie">分页展示</el-button>

        <el-button v-if="queryParams.pageSize === 20" class="filter-item" size="mini" type="primary" @click="updatePageSie">展示全部（导出用）</el-button>

    </div>

    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <div style="height: calc(100vh - 345px);">
      <vxe-table
        ref="xTable"
        :data="tableData"
        stripe
        size="small"
        border
        class="mytable-scrollbar"
        align="center"
        max-height="100%"
        :print-config="{}"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '商务应收台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column
          field="guatime"
          :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"
          title="挂账月"
          width="80"
        />
        <vxe-table-column
          field="ligang"
          :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"
          title="离港日期"
          width="120"
          sortable
        />
        <vxe-table-column field="onAccountNum" :formatter="publicFmt" title="挂账号" width="120" sortable />
        <vxe-table-column field="shipname" :formatter="publicFmt" title="船名" width="80" />
        <vxe-table-column field="username" :formatter="publicFmt" title="业务员" width="100" sortable />
        <vxe-table-column field="source" :formatter="goods_sourceFmt" title="货源类型" width="100" sortable />
        <vxe-table-column field="loadWharfName" title="装货码头" width="80" />
        <vxe-table-column field="finalWharfName" title="卸货码头" width="80" />
        <vxe-table-column field="endLocal" :formatter="publicFmt" title="流向" width="80" />
        <vxe-table-column field="startportName" title="起运港" width="80" />
        <vxe-table-column field="endportName" title="目的港" width="80" />
        <vxe-table-column field="ocname" :formatter="publicFmt" title="一级客户" width="100" />
        <vxe-table-column field="scname" :formatter="publicFmt" title="二级客户" width="100" />
        <vxe-table-column field="goodsname" :formatter="publicFmt" title="品种" width="80" />
        <vxe-table-column field="tonnage" :formatter="publicFmtnumber3" title="重量" width="100" />
        <vxe-table-column field="freightNo" :formatter="publicFmtnumber2" title="运价" width="80" />
        <vxe-table-column field="yunshou" :formatter="publicFmtnumber2" title="海运费收入" width="100" />
        <vxe-table-column field="dailiNo" :formatter="publicFmtnumber2" title="代理费" width="80" />
        <vxe-table-column field="daishou" :formatter="publicFmtnumber2" title="代理费收入" width="100" />
        <vxe-table-column field="totalprcie" :formatter="publicFmtnumber2" title="合计收入" width="100" />
        <vxe-table-column title="开票" width="80">
          <template slot-scope="scope">
            <div>{{ billTypeFmtn(scope.row.taxFreight)+"/"+billTypeFmtn(scope.row.taxDaili) }}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column field="contractCompany" :formatter="contractCompany" title="公司" width="80" />
        <vxe-table-column field="scname" :formatter="publicFmt" title="受票方" width="100" />
        <vxe-table-column field="scname" :formatter="publicFmt" title="付款方" width="100" />
        <vxe-table-column field="payadble" :formatter="publicFmtnumber2" title="开票金额" width="100" />
        <vxe-table-column field="surplus" :formatter="publicFmtnumber2" title="已收金额" width="100" />
        <vxe-table-column field="shengyu" :formatter="publicFmtnumber2" title="余额" width="100" />
        <vxe-table-column field="shoutime" :formatter="publicFmt" title="收款日期" width="180" />
        <vxe-table-column field="kaitime" :formatter="publicFmt" title="开票日期" width="180" />
        <vxe-table-column field="contractCaseNumber" :formatter="publicFmt" title="合同号" width="80" />
        <vxe-table-column field="zhangling" :formatter="publicFmt" title="账龄" width="80" />
      </vxe-table>
    </div>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryParams.pageSize"
      :current-page="queryParams.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />
  </div>
</template>
<style scoped src="../../assets/css/mytable-scrollbar.css" ></style>

<script>
import { getBusinessReceivables } from '@/api/wuliuReport'
import { getDictionaryList } from '@/api/system/baseInit'
import dayjs from 'dayjs'
import { fmtDictionary } from '../../utils/util'

import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'

VXETable.use(VXETablePluginExportXLSX)
const ERR_OK = '0'

export default {
  components: { },
  data() {
    return {
      accountingTypeList:[
        {
          value:0,
          label:'成功'
        },
        {
          value:1,
          label:'山东'
        }
      ],
      value1: '',
      value2: '',
      tableData: [],
      tableLoading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        accountingType:null,
        kehu: null,
        iftax: null,
        company: null,
        billDate: null,
        receiveDate: null,
        guaMonth: null
      },
      receiveTypelist: [
        {
          name: '已开',
          code: 0
        },
        {
          name: '未开',
          code: 1
        }
      ],
      total: 0,
      toolbarButtons: [],
      wharfList: [],
      dictionaryLists: [],
      portList: []
    }
  },
  computed: { },
  mounted() {
    this.$nextTick(() => {
      // 手动将表格和工具栏进行关联
      this.$refs.xTable.connect(this.$refs.xToolbar)
    })
    getDictionaryList('cost_type,goods_type,goods_source,undertaker,items_occurred,bill_tax,mai_company,dian_company,contract_company').then((res) => {
      if (res !== undefined && res.resultCode === ERR_OK) {
        this.dictionaryLists = res.map
      }
    })
    this.getTableData(1)
  },
  methods: {
    updatePageSie() {
      this.queryParams.pageSize = 999999
      this.getTableData(1)
    },
    returnPageSie() {
      this.queryParams.pageSize = 20
      this.getTableData(1)
    },
    selectPaymentType(id) {
      if (this.queryParams.iftax === id) {
        this.queryParams.iftax = null
      } else {
        this.queryParams.iftax = id
      }
      this.getTableData(1)
    },
    selectAccountingType(id) {
      if (this.queryParams.accountingType === id) {
        this.queryParams.accountingType = null
      } else {
        this.queryParams.accountingType = id
      }
      this.getTableData(1)
    },
    getTableData(number) {
      if (this.value1 != null && this.value1.length > 0) {
        this.queryParams.startTime = dayjs(this.value1[0]).format('YYYY-MM-DD HH:mm:ss')
        this.queryParams.endTime = dayjs(this.value1[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      if (this.value2 != null && this.value2.length > 0) {
        this.queryParams.startTime2 = dayjs(this.value2[0]).format('YYYY-MM-DD HH:mm:ss')
        this.queryParams.endTime2 = dayjs(this.value2[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.queryParams.startTime2 = null
        this.queryParams.endTime2 = null
      }
      this.queryParams.pageNum = number
      this.tableLoading = true
      if (!this.queryParams.kehu) {
        this.queryParams.kehu = this.$route.query.kehu
      }
      this.queryParams.user = this.$route.query.user
      console.log(this.queryParams)
      getBusinessReceivables(this.queryParams).then(res => {
        console.log(res)
        this.tableData = res.list.rows
        this.total = res.list.total
        this.tableLoading = false
      })
    },
    publicFmt({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue !== undefined && cellValue != null && cellValue !== '') {
        v = cellValue
      }
      return v
    },
    goods_typeFmt({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['goods_type'])
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue === undefined || cellValue == null || cellValue === '') {
        return '--'
      }
      return dayjs(cellValue).format(fmtstr)
    },
    contractCompany({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['contract_company'])
    },
    goods_sourceFmt({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['goods_source'])
    },
    fmtWharfName({ row, column, cellValue, index }) {
      var v = '--'
      for (var i = 0; i < this.wharfList.length; i++) {
        var tmp = this.wharfList[i]
        if (cellValue === tmp.id) {
          v = tmp.wharf
          return v
        }
      }
      return v
    },
    fmtPortName({ row, column, cellValue, index }) {
      var v = '--'
      for (var i = 0; i < this.portList.length; i++) {
        var tmp = this.portList[i]
        if (cellValue === tmp.id) {
          v = tmp.name
          break
        }
      }
      return v
    },
    publicFmtnumber2({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue !== undefined && cellValue != null && cellValue !== '') {
        v = cellValue.toFixed(2)
      }
      return v
    },
    publicFmtnumber2n({ cellValue }) {
      var v = 0
      if (cellValue !== undefined && cellValue != null && cellValue !== '') {
        v = cellValue.toFixed(2)
      }
      return v
    },

    publicFmtnumber3({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue !== undefined && cellValue != null && cellValue !== '') {
        v = cellValue.toFixed(3)
      }
      return v
    },
    billTypeFmt({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['bill_tax'])
    },
    billTypeFmtn(cellValue) {
      return fmtDictionary(cellValue, this.dictionaryLists['bill_tax'])
    },
    eventPage(e) {
      this.queryParams.pageNum = e
      this.getTableData(e)
    }
  }
}
</script>
