<template>
  <div class="app-container">
    <!-- <div class="sp-title">应付船上费用台账</div>
    <div class="sp-nav"><span class="sp-nav-item">商务账单管理</span><span class="sp-nav-item-sple">/</span><span class="sp-nav-item">商务应收报表</span></div> -->
    <el-form>
      <div class="input-box2">
        <div class="date-box" style="width: 200px;margin-right: 20px">
          <div class="font2">船名</div>
          <el-input v-model="queryParams.shipName" placeholder="船名" clearable />
        </div>
        <div class="date-box" style="width: 200px;margin-right: 20px">
          <div class="font2">供应商名称</div>
          <el-input v-model="queryParams.gong" placeholder="供应商名称" clearable />
        </div>
        <div class="date-box" style="width: 200px;margin-right: 20px">
          <div class="font2">费用科目：</div>
          <PanelSimpleSelection
            :all-data="dictionaryLists['items_occurred']"
            mode-name="费用科目"
            key-str="code"
            val-str="value"
            @setVal="setItemsOccurred"
          />
        </div>
        <div class="date-box" style="width: 200px;margin-right: 20px">
          <div class="font2">合同公司</div>
          <el-select v-model="queryParams.contractCompany" placeholder="请选择" clearable>
            <el-option
              v-for="item in dictionaryLists['contract_company']"
              :key="item.code"
              :label="item.value"
              :value="item.code"
            />
          </el-select>
        </div>
        <div style="margin-left: 20px">
          <div class="font2">核算</div>
          <el-tag
            v-for="(item,index) in accountingTypeList"
            :key="index"
            style="margin-right: 10px;cursor: pointer;margin-top: 7px"
            :type="queryParams.accountingType==item.value?'success':'info'"
            @click="selectAccountingType(item.value)"
          >{{ item.label }}</el-tag>
        </div>
        <div style="margin-right: 20px">
          <div class="font2">开票状态</div>
          <el-tag
            v-for="(item,index) in receiveTypelist"
            :key="index"
            style="margin-right: 10px;cursor: pointer;margin-top: 7px"
            :type="queryParams.iftax==item.code?'success':'info'"
            @click="selectPaymentType(item.code)"
          >{{ item.name }}</el-tag>
        </div>
        <div style="margin-right:20px;align-self:  flex-end;">
          <el-form-item class="botton1" style="margin-bottom: 0; text-align: left;margin-left: 0;margin-top: 15px;">
            <el-button type="primary" @click="getTableData(1)">查询</el-button>
          </el-form-item>
        </div>
        <div v-if="queryParams.ban === 0" style="margin-top: 15px;align-self: flex-end;margin-right: 20px;">
          <el-form-item class="botton1">
            <el-button type="primary" @click="updateban">办事处付款</el-button>
          </el-form-item>
        </div>
        <div v-if="queryParams.ban === 1" style="margin-top: 15px;align-self: flex-end;margin-right: 20px;">
          <el-form-item class="botton1">
            <el-button type="primary" @click="updateban">全部付款</el-button>
          </el-form-item>
        </div>
        <div v-if="queryParams.pageSize === 5000" style="margin-top: 15px;align-self: flex-end;margin-right: 20px;">
          <el-form-item class="botton1">
            <el-button type="primary" @click="returnPageSie">分页展示</el-button>
          </el-form-item>
        </div>
        <div v-if="queryParams.pageSize === 20" style="margin-top: 15px;align-self: flex-end;margin-right: 20px;">
          <el-form-item class="botton1">
            <el-button type="primary" @click="updatePageSie">展示全部（导出用）</el-button>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <div style="height: calc(100vh - 365px);">
      <vxe-table
        ref="xTable"
        :data="tableData"
        stripe
        size="small"
        border
        class="mytable-scrollbar"
        align="center"
        :print-config="{}"
        max-height="100%"
        highlight-current-row
        resizable
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '船上费用应付台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
      >
        <!--        <vxe-table-column  field="guaTime"-->
        <!--                           :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"-->
        <!--                           title="挂账时间" width="80"></vxe-table-column>-->
        <!--        <vxe-table-column  field="createDate"-->
        <!--                          :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'YYYY/MM')}"-->
        <!--                          title="月份" width="120" sortable></vxe-table-column>-->
        <vxe-table-column field="contractCompany" :formatter="contractCompany" title="合同公司" width="80" />
        <vxe-table-column field="voyageNumber" :formatter="publicFmt" title="航次号" width="120" sortable />
        <vxe-table-column
          field="epartureTimeDate"
          :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}"
          title="离港日期"
          width="120"
          sortable
        />
        <vxe-table-column field="shipName" :formatter="publicFmt" title="船名" width="80" />
        <vxe-table-column field="startPort" :formatter="publicFmt" title="起运港" width="80" />
        <vxe-table-column field="endPort" :formatter="publicFmt" title="目的港" width="80" />
        <vxe-table-column field="costBear" :formatter="costBearFmt" title="承担方" width="80" />
        <vxe-table-column field="costTypeName" :formatter="publicFmt" title="科目" width="80" />
        <vxe-table-column field="cusSupName" :formatter="publicFmt" title="供应商" width="80" />
        <vxe-table-column field="payer" :formatter="payerFmt" title="付款方式" width="100" />
        <vxe-table-column field="tonnesNum" :formatter="publicFmtnumber3" title="吨位" width="100" />
        <vxe-table-column field="unitPrice" :formatter="publicFmtnumber2" title="单价" width="80" />
        <vxe-table-column field="totalPrice" :formatter="publicFmtnumber2" title="发生金额" width="100" />
        <vxe-table-column field="paymentMoney" :formatter="publicFmtnumber2" title="付款金额" width="80" />
        <vxe-table-column field="balance" :formatter="publicFmtnumber2" title="余额" width="80" />
        <vxe-table-column field="paymentTime" :formatter="publicFmt" title="付款日期" width="180" />
        <vxe-table-column field="billMoney" :formatter="publicFmtnumber2" title="发票金额" width="100" />
        <vxe-table-column field="receiveBillTime" :formatter="publicFmt" title="收票日期" width="180" />
        <vxe-table-column field="billNum" :formatter="publicFmt" title="发票号" width="80" />
        <vxe-table-column field="supplierTitle" :formatter="publicFmt" title="发票名头" width="100" />
        <vxe-table-column field="tallyReceiveTotal" :formatter="publicFmtnumber2" title="理货费收入" width="100" />
        <vxe-table-column field="tallyReceive" :formatter="publicFmtnumber2" title="已收金额" width="100" />
        <vxe-table-column field="tallyReceiveBalance" :formatter="publicFmtnumber2" title="余额" width="100" />
        <vxe-table-column field="tallyReciveTime" :formatter="publicFmt" title="收入日期" width="180" />
      </vxe-table>
    </div>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryParams.pageSize"
      :current-page="queryParams.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />
  </div>
</template>

<script>

// import {getShipCostBusinessPayables} from "../../api/businessReport";
import { getShipCostBusinessPayables } from '@/api/wuliuReport'
import { getDictionaryList } from '@/api/system/baseInit'
import dayjs from 'dayjs'

import { fmtDictionary } from '../../utils/util'
// import {getAllSysWharf} from "../../api/sysWharf";
import 'vxe-table/lib/style.css'
// import {getAllShipPort} from "../../api/shipPort";
import VXETable from 'vxe-table'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import PanelSimpleSelection from '../../components/PanelSelection/simplePanelSelection'
VXETable.use(VXETablePluginExportXLSX)
const ERR_OK = '0'
export default {
  components: { PanelSimpleSelection },
  data() {
    return {
      accountingTypeList: [
        {
          value: 0,
          label: '成功'
        },
        {
          value: 1,
          label: '山东'
        }
      ],
      value1: '',
      value2: '',
      tableData: [],
      tableLoading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        costAggregationId: null,
        costName: null,
        gong: null,
        iftax: null,
        ban: 0,
        contractCompany: null,
        shipName: null
      },
      receiveTypelist: [
        {
          name: '开票',
          code: 0
        },
        {
          name: '未开',
          code: 1
        }
      ],
      total: 0,
      toolbarButtons: [],
      wharfList: [],
      dictionaryLists: [],
      portList: []
    }
  },
  created() {
    this.$nextTick(() => {
      // 手动将表格和工具栏进行关联
      this.$refs.xTable.connect(this.$refs.xToolbar)
    })
    // "cost_type,goods_type,goods_source,undertaker,items_occurred,bill_tax,mai_company,dian_company,contract_company,undertaker,Payer"
    getDictionaryList('undertaker,items_occurred,contract_company,Payer').then((res) => {
      if (res != undefined && res.resultCode === ERR_OK) {
        this.dictionaryLists = res.map
      }
    })
    // getAllSysWharf().then(res=>{
    //   if (res!=undefined&&res.resultCode === ERR_OK) {
    //     this.wharfList = res.list
    //   }
    // })
    // getAllShipPort().then(res=>{
    //   if (res!=undefined&&res.resultCode === ERR_OK) {
    //     this.portList = res.list
    //   }
    // })
    this.queryParams.costAggregationId = this.$route.query.costAggregationId
    this.queryParams.costName = this.$route.query.costTypeName
    this.getTableData(1)
  },
  mounted() {
  },
  methods: {
    setItemsOccurred(obj) {
      this.queryParams.costName = obj.value
    },
    updatePageSie() {
      this.queryParams.pageSize = 5000
      this.getTableData(1)
    },
    updateban() {
      if (this.queryParams.ban === 0) {
        this.queryParams.ban = 1
      } else {
        this.queryParams.ban = 0
      }
      this.getTableData(1)
    },
    returnPageSie() {
      this.queryParams.pageSize = 20
      this.getTableData(1)
    },
    selectAccountingType(id) {
      if (this.queryParams.accountingType === id) {
        this.queryParams.accountingType = null
      } else {
        this.queryParams.accountingType = id
      }
      this.getTableData(1)
    },
    selectPaymentType(id) {
      if (this.queryParams.iftax === id) {
        this.queryParams.iftax = null
      } else {
        this.queryParams.iftax = id
      }
      this.getTableData(1)
    },
    getTableData(number) {
      this.queryParams.pageNum = number
      this.tableLoading = true
      getShipCostBusinessPayables(this.queryParams).then(res => {
        console.log(res)
        this.tableData = res.list.rows
        this.total = res.list.total
        this.tableLoading = false
      })
    },
    publicFmt({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    goods_typeFmt({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['goods_type'])
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue == undefined || cellValue == null || cellValue == '') {
        return '--'
      }
      return dayjs(cellValue).format(fmtstr)
    },
    contractCompany({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['contract_company'])
    },
    goods_sourceFmt({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['goods_source'])
    },
    fmtWharfName({ row, column, cellValue, index }) {
      var v = '--'
      for (var i = 0; i < this.wharfList.length; i++) {
        var tmp = this.wharfList[i]
        if (cellValue === tmp.id) {
          v = tmp.wharf
          return v
        }
      }
      return v
    },
    fmtPortName({ row, column, cellValue, index }) {
      var v = '--'
      for (var i = 0; i < this.portList.length; i++) {
        var tmp = this.portList[i]
        if (cellValue == tmp.id) {
          v = tmp.name
          break
        }
      }
      return v
    },
    publicFmtnumber2({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue.toFixed(2)
      }
      return v
    },
    publicFmtnumber2n({ cellValue }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue.toFixed(2)
      }
      return v
    },

    publicFmtnumber3({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue.toFixed(3)
      }
      return v
    },
    billTypeFmt({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['bill_tax'])
    },
    billTypeFmtn(cellValue) {
      return fmtDictionary(cellValue, this.dictionaryLists['bill_tax'])
    },
    costBearFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['undertaker'])
    },
    payerFmt({ row, column, cellValue, index }) {
      return fmtDictionary(cellValue, this.dictionaryLists['Payer'])
    },
    eventPage(e) {
      this.queryParams.pageNum = e
      this.getTableData(e)
    }
  }
}
</script>
<style scoped src="../../assets/css/mytable-scrollbar.css" ></style>
<style scoped>
  @import '../../assets/css/input.css';

  .bottom-btn{
    text-align: right;
    margin-top: 10px;
  }
  /* --------------------------------------------------------------- */
  .showTag{
    height: 120px;
  }
  .el-tag {
    cursor: pointer;
  }

  .choose-contract-type >>> .el-button {
    width: 120px;
  }

  .choose-contract-type >>> .el-form-item {
    margin-left: 1px;
    margin-right: 1px;
  }

  .contracrt-scrollbar {
    height: 100%;
    margin-bottom: 20px;
  }

  .show-contracts-image {
    display: flex;
    flex-wrap: wrap;
    width: 765px;
    height: 55vh;
    margin: 0 auto;
  }

  .show-contract-image {
    width: 220px;
    height: 340px;
    margin: 5px 15px;
    border-radius: 4px;
  }

  .show-contract-image >>> .contract-img {
    width: 210px;
    height: 297px;
    margin-top: 5px;
  }

  .show-contract-image >>> .yixuanze-img {
    width: 20px;
    height: 20px;
    border-radius: 60%;
    position: absolute;
    top: -5px;
    right: -5px;
    display: none;
  }

  .show-contract-image >>> p {
    color: #55575c;
    margin: 7px 0;
  }

  .chakan-img {
    display: none;
    width: 40px;
    height: 40px;
    position: absolute;
    top: 40%;
    right: 40%;
  }

  /* .selected {
    background-color: #5992f5;
  } */

  .selected >>> .yixuanze-img {
    display: block;
  }

  .selected >>> p {
    color: #ffffff;
  }

  .contract-iframe >>> p {
    position: absolute;
    left: 10px;
  }

  .contract-iframe >>> .el-button {
    position: absolute;
    right: 70px;
    margin-top: 10px;
  }

  /* ------------------------------------------------------------------------------------     */

  .hangci-biaoti-yupeizai {
    color: #409eff;
  }

  .hangci-biaoti {
    text-align: left;
    margin: 6px 0px;
  }

  .hangci-biaoti11 {
  }

  .hangci-biaoti12 {
    margin-left: 30px;
  }

  .hangci-biaoti1 {
    font-size: 0.8rem;
  }

  .hangci-biaoti2 {
    font-size: 0.8rem;
    margin-left: 20px;
  }

  .query-form {
    text-align: left;
    padding-top: 10px;
  }

  .sp_add_left {
    width: 45%;
    margin-right: 5%;
  }

  .sp_add_right {
    width: 45%;
  }

  .el-table-info >>> .cell {
    text-align: center;
  }

  .el-table-info >>> th {
    background: #f5f7fa;
  }

  .mytable td {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .mytable td .cell {
    padding-left: 0.1rem;
    padding-right: 0.1rem;
  }

  .el-table-info >>> .warning-cell {
    color: red;
  }

  .el-table-info >>> .success-cell {
    color: #6dd400;
  }

  .dialog-info {
    text-align: left;
    margin-top: -40px;
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 14px;
    width: 100%;
    display: inline-block;
  }

  .info-margin {
    margin-left: 5%;
    margin-right: 5%;
  }

  .info-title {
    display: inline-block;
    width: 5%;
    text-align: left;
  }

  .info-text {
    display: inline-block;
    width: 10%;
    text-align: left;
  }

  .myStep >>> .el-step__main {
    margin-top: 10px;
  }
  .hangci-biaoti2first {
    font-size: 0.8rem;
  }
  .hsbhs{
    margin-left: 20px;
  }
  .global-table-box>table {
    width: 100%;
    table-layout: fixed;
    border-spacing: 0;
    border-collapse: collapse\9;
  }

  .global-table thead td,
  .global-table th {
    line-height: 20px;
    font-size: 13px;
    padding: 15px 0;
    background-color: #F5F6FA;
    text-align: left;
    font-weight: bold;
    font-style: normal;
    margin: 0;
    color: #757b84;
    white-space: nowrap;
    border-bottom: 1px solid #EBEEF5;
  }
  .global-table tbody td {
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: #fff;
    color: #81858e;
    border-bottom: 1px solid #F5F4F5;
    font-size: 13px;
    height:30px;
    white-space: normal;
    word-break: break-all;
  }
  .global-table th {
    padding-left: 15px;
    padding-right: 5px;
  }

  .global-table tbody td.global-strong-td {
    font-weight: bold;
    color: #333;
    border-right: 1px solid #F5F4F5;
    background-color: #FAFBFD;
    padding: 0 20px;
  }

  .global-border-td {
    border-right: 1px solid #F5F4F5;
  }

  .global-table tbody td.globla-orange-td {
    color: #FF8446;
  }

  .global-table tr {
    cursor: default;
  }

  .global-table tr.selected td {
    background-color: #e0f0ff;
  }

  .global-table tr:nth-child(2n):not(.global-total-tr) td {
    background-color: #fbfbfb;
  }

  .global-table tr:hover td {
    background-color: #F5F8FF !important;
  }

  .global-table~.ui-loading {
    height: 300px;
  }

  .global-table .el-button--mini {
    font-size: 13px;
  }

  .global-table a {
    padding-right: 10px;
    font-size: 13px;
  }

  .global-table tbody tr.global-total-tr,
  .global-table tbody .global-total-tr td {
    font-size: 14px;
    font-weight: bold;
    color: #65696d;
  }

  .global-table tbody tr.global-subtotal-tr {
    font-weight: 500;
  }
  .el-input{
    width:100%;
  }
  .input-box2{
    flex-wrap: wrap;
    align-items: center;
  }
  .botton1{
    margin: 0;
  }
</style>

