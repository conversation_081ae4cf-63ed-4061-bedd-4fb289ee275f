<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>凭证科目管理演示</h1>
      <p>基于 getTokenCompanyList, getCertTemplTempList, saveCertTemplTemp, removeCertTemplTemp 接口实现</p>
    </div>
    
    <CertSubject />
  </div>
</template>

<script>
import CertSubject from '@/components/CertTempl/certSubject.vue'

export default {
  name: 'CertSubjectDemo',
  components: {
    CertSubject
  }
}
</script>

<style scoped>
.demo-container {
  min-height: 100vh;
  background-color: #f0f2f5;
}

.demo-header {
  background: white;
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 0;
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.demo-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
