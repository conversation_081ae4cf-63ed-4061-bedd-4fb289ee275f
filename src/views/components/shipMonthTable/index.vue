<template>
   <div class="expense-table-container">
          <div class="expense-table">
            <div class="expense-table-header">
              <div class="expense-category-row">
                <!-- 左侧标题 -->
                <div class="expense-header-cell expense-category-name">项目分类</div>

                <!-- 右侧标题 -->
                <div class="expense-subcategory-container">
                  <div class="expense-header-row">
                    <div class="expense-header-cell expense-id-cell">&nbsp;</div>
                    <div class="expense-header-cell expense-subcategory-name">项目名</div>
                    <div class="expense-header-cell expense-subcategory-amount">金额</div>
                    <div class="expense-header-cell expense-subcategory-name">明细</div>
                    <div class="expense-header-cell expense-remark-cell">备注</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="expense-table-body">
              <!-- 左侧一级分类 -->
              <div class="expense-table-left">
                <div v-for="(category, catIndex) in this.outcomeList" :key="'cat-' + catIndex" class="expense-category-row">
                  <!-- 一级分类名称 -->
                  <div class="expense-cell expense-category-name" :class="{ 'category-highlight-bak': category.typeName.includes('合计') }">
                    {{ category.typeName }}
                  </div>

                  <!-- 右侧二级分类区域 -->
                  <div class="expense-subcategory-container">
                    <!-- 如果有子分类，显示子分类 -->
                    <template v-if="category.children && category.children.length">
                      <div v-for="(subCat, subIndex) in category.children" :key="'subcat-' + subIndex" class="expense-subcategory-row">
                        <div class="expense-cell expense-id-cell" >{{ subIndex + 1 }}</div>
                        <div class="expense-cell expense-subcategory-name">{{ subCat.typeName }}</div>
                        <div class="expense-cell expense-subcategory-amount">{{ formatNumber(subCat._sum || 0) }}</div>
                        <div class="expense-cell expense-subcategory-name flex-row-direct">
                          <div v-for="item in subCat._detail" class="detail-item">
                           <span>{{ item.customerName }}</span>&nbsp;
                           <span>¥{{ formatNumber(item.price) }}</span>
                           <span>({{  getTaxRateLabel(item.priceTaxRate) }})</span>
                          </div>
                        </div>
                        <div class="expense-cell expense-remark-cell"></div>
                      </div>
                      <!-- 子分类合计 -->
                      <div class="expense-subcategory-row expense-subtotal-row">
                        <div class="expense-cell expense-id-cell">&nbsp;</div>
                        <div class="expense-cell expense-subcategory-name category-highlight-bak">合计</div>
                        <div class="expense-cell expense-subcategory-amount category-highlight-bak">{{ formatNumber(category._sum || 0) }}</div>
                        <div class="expense-cell expense-subcategory-name"></div>
                        <div class="expense-cell expense-remark-cell"></div>
                      </div>
                    </template>
                  </div>
                </div>
                <!-- <div class="expense-category-row" v-if="oilMergeType && oilMergeType.length">
                  <div class="expense-cell expense-category-name" >
                    油品费用
                  </div>
                    -- 右侧二级分类区域 --
                    <div class="expense-subcategory-container">
                      -- 如果有子分类，显示子分类 --
                      <template v-if="oilMergeType && oilMergeType.length">
                        <div  v-for="(subCat, oilIndex) in oilMergeType" :key="'oil-' + oilIndex" class="expense-subcategory-row">
                          <div class="expense-cell expense-id-cell" >{{ oilIndex + 1 }}</div>
                          <div class="expense-cell expense-subcategory-name">{{ subCat.type }}</div>
                          <div class="expense-cell expense-subcategory-amount">{{ formatNumber(subCat.waterBalance || 0) }}</div>
                          <div class="expense-cell expense-subcategory-name flex-row-direct">
                            <div v-for="item in subCat._detail" class="detail-item">
                              <span>{{ item.supplierName }}</span>&nbsp;
                              <span>¥{{ formatNumber(item.waterBalance) }}</span>
                              <span>({{  getTaxRateLabel(item.priceTaxRate) }})</span>
                            </div>
                          </div>
                          <div class="expense-cell expense-remark-cell"></div>
                        </div>
                        -- 子分类合计 --
                        <div class="expense-subcategory-row expense-subtotal-row">
                          <div class="expense-cell expense-id-cell">&nbsp;</div>
                          <div class="expense-cell expense-subcategory-name category-highlight-bak">合计</div>
                          <div class="expense-cell expense-subcategory-amount category-highlight-bak">{{ formatNumber(getTotalOil()) }}</div>
                          <div class="expense-cell expense-subcategory-name"></div>
                          <div class="expense-cell expense-remark-cell"></div>
                        </div>
                      </template>
                    </div>
                </div> -->
              <div>
                  <!-- <div v-for="(oilItem, oilIndex) in oilList" :key="'oil-' + oilIndex" class="expense-category-row"
                              :class="['cost-item', oilIndex % 2 === 0 ? 'left-column' : 'right-column']"
                            >



                              <el-row>
                                <el-col :span="12">
                                  <span class="cost-name">{{ oilItem.type }}({{ oilItem.tonnage }}{{ oilItem.unit
                                  }})</span>
                                  <span v-if="oilItem.supplierName" class="supplier-name">(承运商：{{ oilItem.supplierName
                                  }})</span>
                                  <div class="oil-info">
                                    <span class="oil-value">{{ oilItem.voyageNum || '-' }}</span>
                                    <span class="oil-value">{{ oilItem.loadingPortName || '-' }}</span>-<span
                                      class="oil-value"
                                    >{{ oilItem.unloadingPortName || '-' }}</span>
                                  </div>
                                </el-col>
                                <el-col :span="12" class="text-right">
                                  <span class="amount-cell"><span v-if="oilItem.priceTaxRate > 0" class="tax-rate">({{
                                    getTaxRateLabel(oilItem.priceTaxRate) }})&nbsp;</span>¥ {{
                                    formatNumber(oilItem.waterBalance || '0') || '-'
                                  }}</span>
                                </el-col>
                              </el-row>
                    </div> -->
                </div>
              </div>
            </div>

            <!-- 底部总计 -->
            <div class="expense-table-footer">
              <div class="expense-footer-row">
                <div class="expense-cell expense-footer-label">合计</div>
                <div class="expense-cell expense-footer-total">{{ formatNumber(getTotalAll()) }}</div>
              </div>
            </div>
          </div>
        </div>
</template>
<script>
export default {
  name: 'shipMonthTable',
  props: {
    outcomeList: {
      type: Array,
      default: () => []
    },
    oilList:{
      type: Array,
      required: false,
      default: () => []
    }
  },
  data() {
    return {
    }
  },
  computed: {
    oilMergeType() {
      if (!this.oilList || this.oilList.length === 0) {
        return []
      }
      const typeMap = {}
      const root = []
      // biome-ignore lint/complexity/noForEach: <explanation>
      this.oilList.forEach(item => {
        if (!typeMap[item.type]) {
          typeMap[item.type] = {
            sum:0,
            _detail:[]
          }
        }
        // typeMap[item.type] += item.waterBalance
        typeMap[item.type].sum += item.waterBalance
        typeMap[item.type]._detail.push(item)
      })
      if(typeMap){
        for (const key in typeMap) {
          root.push({
            type: key,
            waterBalance: typeMap[key].sum,
            _detail:typeMap[key]._detail
          })
        }
      }
      return root
    }
  },
  methods: {
    formatNumber(num) {
      return new Intl.NumberFormat('zh-CN').format(num)
    },
    getTotalOil() {
      if (!this.oilList || this.oilList.length === 0) {
        return 0
      }
      return this.oilList.reduce((total, item) => {
        return total + (item.waterBalance || 0)
      }, 0)
    },
    getTotalAll(){
      return this.getTotalExpense()
      // + this.getTotalOil()
    },
    getTotalExpense() {
      if (!this.outcomeList || this.outcomeList.length === 0) {
        return 0
      }
      return this.outcomeList.reduce((total, category) => {
        return total + (category._sum || 0)
      }, 0)
    },
    getTaxRateLabel(tax){
      return tax ? `${(tax * 100).toFixed(0)}%` : '无税'
    }
  }
}
</script>
<style lang="scss" scoped>
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;

}
.category-header.expanded {
  border-bottom: 1px solid #ebeef5;
}

.category-name {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #1f2d3d;

  i {
    margin-right: 5px;
    transition: transform 0.3s;

    &.is-expanded {
      transform: rotate(90deg);
    }
  }
}

.category-total {
  font-weight: 500;
  color: #67c23a;
  font-family: Monaco, monospace;
}

.category-children {
  padding-left: 20px;
  margin-top: 8px;
}

.sub-category-item {
  margin-bottom: 8px;
}

.category-details {
  padding-left: 20px;
  margin-top: 4px;
}
.flex-row-direct{
  display: flex;
  flex-direction: column;
}
.detail-item {

  display: block;
  padding: 6px 0;
  border-bottom: 1px dashed #ebeef5;
  line-height: 1.5;

  span {
    display: block;

    &:first-child {
      font-weight: 500;
      padding-bottom: 2px;
    }

    &:nth-child(2) {
      text-align: right;
      white-space: nowrap;
      font-family: Monaco, monospace;
      color: #606266;
    }

    &:last-child {
      text-align: right;
      color: #909399;
      font-size: 12px;
      padding-top: 2px;
    }
  }

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }
}

.detail-name {
  display: flex;
  align-items: center;
  flex: 1;
}

.detail-amount {
  margin-left: 10px;
  white-space: nowrap;
}

/* 新增表格样式 */
.expense-table-container {
  width: 100%;
  overflow-x: auto;
}

.expense-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #dcdfe6;
  font-size: 14px;
}

.expense-table-header {
  background-color: #f5f7fa;
  font-weight: bold;
  border-bottom: 1px solid #dcdfe6;
  width: 100%;
}

.expense-header-cell {
  padding: 8px 12px;
  text-align: center;
  border-right: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
}

.expense-table-body {
  display: flex;
}

.expense-table-left {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.expense-category-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.expense-cell {
  padding: 8px 12px;
  border-right: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  height: 100%;
}

.expense-id-cell {
  width: 40px;
  justify-content: center;
  align-self: stretch;
  text-wrap: nowrap;
  flex-shrink: 0;
}

.expense-category-name {
  width: 150px;
  font-weight: 500;
  flex-shrink: 0;
}

.expense-amount {
  width: 80px;
  text-align: right;
  justify-content: flex-end;
}

.expense-subcategory-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.expense-subcategory-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  align-items: stretch;
}

.expense-subcategory-name {
  width: 180px;
  padding-left: 20px;
  word-break: break-word;
  flex-shrink: 0;

  .detail-item {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
  }
}

.expense-subcategory-amount {
  width: 100px;
  text-align: right;
  justify-content: flex-end;
  flex-shrink: 0;
}

.expense-table-footer {
  border-top: 2px solid #409eff;
  background-color: #f5f7fa;
}

.expense-footer-row {
  display: flex;
  padding: 10px 0;
}

.expense-footer-label {
  font-weight: bold;
  width: 150px;
  justify-content: flex-end;
}

.expense-footer-total {
  width: 120px;
  font-weight: bold;
  color: #f56c6c;
  justify-content: flex-end;
}

.category-highlight {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: bold;
}

.expense-item-col {
  width: 150px;
}

.expense-amount-col {
  width: 80px;
}

.expense-subcategory-col {
  width: 150px;
}

.expense-header-row {
  display: flex;
  width: 100%;
  align-items: center;
}

.expense-voucher-cell {
  width: 60px;
  text-align: center;
}

.expense-remark-cell {
  width: 150px;
  text-align: center;
  flex-shrink: 0;
}

</style>
