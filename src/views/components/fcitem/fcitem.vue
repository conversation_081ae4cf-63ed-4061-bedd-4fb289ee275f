import store from '@/store';
<template>
  <div class="flx" >
    <!-- :class="level<=1?'row':'column'" -->
    <!-- <div v-for="item in list" style="margin-left:10px;" :key="item.id"> -->

      <!-- 是否可选 -->
      <!-- <el-radio v-model="nvalue" v-show="item.isHidden!=1 && item.isSel==1" class="itemflx" :label="item.id">{{ item.label }}</el-radio>
      <span v-show="item.isHidden!=1 && item.isSel!=1" class="itemflx" @click="clkItem(item)" >{{ item.label }}</span>
      <fc-item v-if="item.children && item.children.length"
      v-model="nvalue"
      @clkItem="clkItem" :level="level+1" :list="item.children" /> -->
    <!-- </div> -->
    <el-cascader ref="organizerUnit" v-model="casModel" style="min-width:300px;" placeholder="请选择资金类型" clearable  @change="clkItem" filterable  :options="list" :props="casProps">
      <template slot-scope="{ node, data }">
        <span>{{ data.label }}</span>
        <span v-if="!node.isLeaf && data.children.length"> ({{ data.children.length }}) </span>
      </template>
    </el-cascader>
    <!-- <el-cascader-panel v-model="casModel" @beforeFilter="filterCas" @change="clkItem" :show-all-levels="false"  :options="list" :props="casProps">
      <template slot-scope="{ node, data }">
        <span>{{ data.label }}{{ data.isSel }}</span>
        <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
      </template>
    </el-cascader-panel> -->
  </div>
</template>
<script>

export default {
  name: 'FcItem',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    level: {
      type: Number,
      default: 0
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    value(val) {
      this.casModel = val
    }
  },
  data() {
    return {
      casModel: this.value,
      casProps: {
        value: 'id',
        checkStrictly: true,
        disabled: 'isSelBoo'
      },

    }
  },
  computed: {
    nvalue: {
      get() {
        return this.$store.getters.fcSelId
      },
      set(val) {
        this.$store.commit('setFcSelId', val)
      }
    }
  },
  methods: {
    clkItem(item) {
      this.$emit('input', this.casModel)
      this.$emit('clkItem', this.$refs.organizerUnit.getCheckedNodes() && this.$refs.organizerUnit.getCheckedNodes().length>0 && this.$refs.organizerUnit.getCheckedNodes()[0].data)
      if (this.$refs.organizerUnit) {
        this.$refs.organizerUnit.dropDownVisible = false
      }
    },
  },
}
</script>
<style>
 .el-cascader-panel .el-radio {
    width: 33%;
    height: 100%;
    z-index: 10;
    position: absolute;
    top: 10px;
    /* margin-right: 10px; */
    /* right: 10px; */
  }
.el-cascader-node__label{
  margin-left: 10px;
}

</style>
<style scoped>
.flx{
  display: flex;
  flex-wrap: wrap;
  margin: 5px;
}
.column{
  flex-direction: column;
}
.row{
  flex-direction: row;
}
.itemflx{
  flex-shrink: 0;
  white-space: nowrap;
}

</style>
