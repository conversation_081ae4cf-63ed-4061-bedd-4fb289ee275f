<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">合同号</label>
        <el-input v-model="query.contractNo" clearable placeholder="合同号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">审批状态</label>
        <el-select v-model="query.spStatus" placeholder="请选择审批状态" class="filter-item">
          <el-option
            v-for="item in spStatusEnums"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <label class="el-form-item-label">合同状态</label>
        <el-select v-model="query.status" placeholder="请选择合同状态" class="filter-item">
          <el-option
            v-for="item in contractStatusEnums"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <label class="el-form-item-label">合同类型</label>
        <el-select v-model="query.contractType" placeholder="请选择合同类型" class="filter-item">
          <el-option
            v-for="item in contractTypeEnums"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <label class="el-form-item-label">合同模块</label>
        <el-select v-model="query.contractModule" placeholder="请选择合同模块" class="filter-item">
          <el-option
            v-for="item in contractModuleEnums"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <label class="el-form-item-label">甲方</label>
        <el-input v-model="query.partyAName" clearable placeholder="甲方" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">乙方</label>
        <el-input v-model="query.partyBName" clearable placeholder="乙方" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">选择日期</label>
        <date-range-picker v-model="query.createTime" class="date-item" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="签订日期" prop="signDate">
            <el-date-picker
              v-model="form.signDate" style="width: 370px;"
              type="date"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;">
        <el-table-column prop="contractNo" label="合同号" />
        <el-table-column prop="contractType" label="合同类型" >
          <template slot-scope="scope">
            <span>{{ scope.row.contractType|parseContractType }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="partyAName" label="甲方名称" />
        <el-table-column prop="partyBName" label="乙方名称" />
        <el-table-column prop="standard" label="是否标准合同">
          <template slot-scope="scope">
            <span>{{ scope.row.standard ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="fileUrl" label="文件url" /> -->
        <!-- <el-table-column prop="signedFileUrl" label="签章后的文件url" /> -->

        <el-table-column prop="contractModule" label="合同模块" >
          <template slot-scope="scope">
            <span>{{ scope.row.contractModule|parseContractModuleEnums }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="fileName" label="上传文件名称" /> -->
        <!-- <el-table-column prop="previewFileUrl" label="预览用的文件url" /> -->
        <!-- <el-table-column prop="signedFileName" label="签章后的文件名称" /> -->
        <el-table-column prop="signDate" label="签订日期">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.signDate,'{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="validityDate" label="有效期">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.validityDate,'{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="合同状态" >
          <template slot-scope="scope">
            <span>{{ scope.row.status|parseContractStatus }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="spStatus" label="审批状态" >
          <template slot-scope="scope">
            <span>{{ scope.row.spStatus|parseSpStatus }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column v-permission="['admin','contract:edit','contract:del','contract:upload','contract:verify','contract:archive']" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              :delShow="false"
              :editShow="scope.row.status < 2"
            />
            <el-button @click="uploadSignFileHandle(scope.row)" v-if="checkPer(['admin','contract:upload']) && scope.row.spStatus === 2 && scope.row.status === 1" style="margin-top: 3px;" type="primary" icon="el-icon-upload" size="mini">上传盖章合同</el-button>
            <el-button @click="verifyHandle(scope.row)" v-if="checkPer(['admin','contract:verify']) && scope.row.spStatus === 2 && scope.row.status === 2" style="margin-top: 3px;" type="warning" icon="el-icon-check" size="mini">确定合法性</el-button>
            <el-button @click="archiveHandle(scope.row)" v-if="checkPer(['admin','contract:archive']) && scope.row.spStatus === 2 && scope.row.status === 3" style="margin-top: 3px;" type="success" icon="el-icon-folder-opened" size="mini">归档</el-button>
            <el-button @click="lookContract(scope.row)" v-if="scope.row.spStatus === 2" style="margin-top: 3px;" type="info" icon="el-icon-folder-opened" size="mini">查看合同</el-button>
            <el-button @click="lookContractStatusTimeLog(scope.row)" v-if="scope.row.spStatus > 1" style="margin-top: 3px;" type="info" icon="el-icon-time" size="mini">历史</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>

    <!--上传签章后的合同-->
    <el-dialog :close-on-click-modal="false" :visible.sync="uploadSignFileDialogShow" title="上传签章后的合同" width="500px">
      <div id="upload-signed-file-dialog" style="display: flex;justify-content: center;">
        <el-upload
                drag
                :on-success="suc"
                :limit="1"
                :headers="headers"
                :on-exceed="handleExceed"
                :file-list="fileList"
                :on-remove="handleRemove"
                :before-upload="beforeUploadHandle"
                :on-preview="previewHandle"
                :accept="accept"
                :action="action">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">文件大小不超过{{sizeLimit}}MB</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="uploadSignFileDialogShow=false">取消</el-button>
        <el-button :loading="uploadSignFileLoading" type="primary" @click="uploadSignFileSubmit">确认</el-button>
      </div>
    </el-dialog>

    <!--查看合同文件-->
    <el-dialog :close-on-click-modal="false" :visible.sync="contractFileDialogShow" title="查看合同文件" width="500px">
      <div style="display: flex;justify-content: center;">
        <ul class="el-upload-list el-upload-list--text">
          <li tabindex="0" class="el-upload-list__item is-success" v-if="itemDetail.fileUrl">
            <a class="el-upload-list__item-name" @click="downloadContractFile(itemDetail.fileUrl)"><span style="color: #E6A23C;">合同（未盖章）：</span><i class="el-icon-document"></i>{{itemDetail.fileName}}</a>
          </li>
          <li tabindex="0" class="el-upload-list__item is-success" v-if="itemDetail.signedFileUrl">
            <a class="el-upload-list__item-name" @click="downloadContractFile(itemDetail.signedFileUrl)"><span style="color: #67C23A;">合同（盖章后）：</span><i class="el-icon-document"></i>{{itemDetail.signedFileName}}</a>
          </li>
        </ul>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="contractFileDialogShow=false">关闭</el-button>
      </div>
    </el-dialog>

    <!--查看合同状态时间的变更记录-->
    <el-dialog :close-on-click-modal="false" :visible.sync="contractStatusTimeDialogShow" title="合同历史" width="300px">
      <div v-loading="contractStatusTimeLoading" style="display: flex;justify-content: center;">
        <el-timeline>
          <el-timeline-item v-for="(item,index) of contractStatusTimeLogs" :key="index" :timestamp="item.changeTime" placement="top">
            <p>{{item.status|parseContractStatusShow}}</p>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="contractStatusTimeDialogShow=false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const contractModuleEnums = [
  {label: '钢材',value: 0},
  {label: '散货',value: 1},
  {label: '其他',value: 2},
  // {label: '物流其他',value: 3},
]
const contractTypeEnums = [
  {label: "航次租船合同",value: 1},
  {label: "航次运输合同",value: 2},
  {label: "内部租船合同",value: 3},
  {label: "航次过驳合同",value: 4},
  {label: "运输协议（长期）",value: 5},
  {label: "航次租船合同",value: 10},
  {label: "航次配货合同",value: 11},
  {label: "受托人船舶代理合同",value: 20},
  {label: "委托人船舶代理合同",value: 21},
  {label: "受托人货运代理协议",value: 22},
  {label: "委托人货运代理协议",value: 23},
  {label: "其他",value: 99},
]
const spStatusEnums= [
        {label: '待发起',value: 0},
        {label: '审批中',value: 1},
        {label: '审核通过',value: 2},
        {label: '审核拒绝',value: 3},
        {label: '已撤回',value: 4},
        {label: '已重新提交',value: 5},
      ]
const contractStatusEnums = [
  {label: '审批中',value: 0},
  {label: '待上传签章合同',value: 1},
  {label: '待确定合法性',value: 2},
  {label: '待归档',value: 3},
  {label: '已归档',value: 4},
]
const contractStatusShowEnums = [
  {label: '审批通过',value: 1},
  {label: '已上传签章合同',value: 2},
  {label: '已确定合法性',value: 3},
  {label: '已归档',value: 4}
]
import crudContract from '@/api/business/contract'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import uploadFile from '@/components/UploadFile'
import {mapGetters} from 'vuex'
import {getToken} from '@/utils/auth'
import {downloadFileByUrl,previewPDF} from '@/utils/index'
import DateRangePicker from '@/components/DateRangePicker'

const defaultForm = { id: null, contractNo: null, contractTitle: null, signDate: null, consensusDate: null, validityDate: null, contractType: null, partyAId: null, partyAName: null, partyADepositBank: null, partyABankAccount: null, partyAContact: null, partyBId: null, partyBName: null, partyBDepositBank: null, partyBBankAccount: null, partyBContact: null, useTpl: null, standard: null, fileUrl: null, signedFileUrl: null, spStatus: null, clausesForm: null, createTime: null, createBy: null, updateTime: null, updateBy: null, delFlag: null, tplId: null, contractModule: null, fileName: null, previewFileUrl: null, signedFileName: null, tplSchema: null, source: null, status: null }
export default {
  name: 'Contract',
  components: { pagination, crudOperation, rrOperation, udOperation,uploadFile,DateRangePicker },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '合同', url: 'api/contract', idField: 'id', sort: 'id,desc', crudMethod: { ...crudContract },optShow: {
      add: false,
      edit: false,
      del: false,
      download: true,
      reset: true
    }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'contract:add'],
        edit: ['admin', 'contract:edit'],
        del: ['admin', 'contract:del']
      },
      rules: {
        signDate: [
          { required: true, message: '签订日期不能为空', trigger: 'blur' }
        ],
      },
      spStatusEnums: spStatusEnums,
      contractStatusEnums: contractStatusEnums,
      contractTypeEnums: contractTypeEnums,
      contractModuleEnums: contractModuleEnums,

      uploadSignFileDialogShow: false,
      uploadSignFileLoading: false,
      contractFileDialogShow: false,
      contractStatusTimeDialogShow: false,
      contractStatusTimeLoading: false,
      contractStatusTimeLogs: [],

      fileList: [],
      accept: "image/*,.doc,.docx,application/msword,.pdf",
      sizeLimit: 10,
      headers: {
          'Authorization': getToken()
      },

      itemDetail: {}
    }
  },
  computed: {
      ...mapGetters([
        'baseApi'
      ]),
      action() {
        return this.baseApi + '/api/contract/upload'
      },
  },
  filters: {
    parseContractModuleEnums(v) {
      let t = contractModuleEnums.find(item => item.value === v)
      if(t) {
        return t.label
      }
      return v
    },
    parseSpStatus(v) {
      let t = spStatusEnums.find(item => item.value === v)
      if(t) {
        return t.label
      }
      return v
    },
    parseContractType(v) {
      let t = contractTypeEnums.find(item => item.value === v)
      if(t) {
        return t.label
      }
      return v
    },
    parseContractStatus(v) {
      let t = contractStatusEnums.find(item => item.value === v)
      if(t) {
        return t.label
      }
      return v
    },
    parseContractStatusShow(v) {
      let t = contractStatusShowEnums.find(item => item.value === v)
      if(t) {
        return t.label
      }
      return v
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    uploadSignFileHandle(item) {
      this.uploadSignFileDialogShow = true
      Object.assign(this.form,item)
      this.fileList = []
      if(this.form.signedFileUrl && this.form.signedFileName) {
        this.fileList.push({
          name: this.form.signedFileName,
          url: this.form.signedFileUrl
        })
      }
    },
    verifyHandle(item) {
      this.$confirm(`[${item.contractNo}]上传的签章文件是否合法?`, '校验签章文件合法性', {
        confirmButtonText: '是',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            item.status = 3
            crudContract.edit(item).then(res => {
              done();
              Object.assign(this.form,defaultForm)
              this.$message.success('操作成功')
              this.crud.refresh()
            }).catch(err => {
              this.$message.error(err.message)
            }).finally(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '是';
            })
          } else {
            done();
          }
        }
      }).then(() => {

      }).catch(() => {

      });
    },
    archiveHandle(item) {
      this.$confirm(`确定把[${item.contractNo}]进行归档吗?`, '合同归档', {
        confirmButtonText: '是',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '执行中...';
            item.status = 4
            crudContract.edit(item).then(res => {
              done();
              Object.assign(this.form,defaultForm)
              this.$message.success('操作成功')
              this.crud.refresh()
            }).catch(err => {
              this.$message.error(err.message)
            }).finally(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '是';
            })
          } else {
            done();
          }
        }
      }).then(() => {

      }).catch(() => {

      });
    },
    uploadSignFileSubmit() {
      if(this.fileList.length < 1) {
          this.$message.warning('请上传文件')
          return
      }
      let file = this.fileList[0]
      this.form.signedFileUrl = file.url
      this.form.signedFileName = file.name
      this.form.status = 2

      console.log(file,this.form)
      console.log(crudContract)
      this.uploadSignFileLoading = true
      crudContract.edit(this.form).then(res => {
        Object.assign(this.form,defaultForm)
        this.$message.success('操作成功')
        this.crud.refresh()
      }).catch(err => {
        this.$message.error(err.message)
      }).finally(() => {
        this.uploadSignFileLoading = false
        this.uploadSignFileDialogShow = false
      })
    },
    lookContract(item) {
      this.contractFileDialogShow = true
      this.itemDetail = item
    },
    lookContractStatusTimeLog(item) {
      this.contractStatusTimeDialogShow = true
      this.contractStatusTimeLoading = true
      crudContract.statuslog({id:item.id}).then(res => {
        this.contractStatusTimeLogs = res
      }).finally(() => {
        this.contractStatusTimeLoading = false
      })
    },
    downloadContractFile(url) {
      previewPDF(url)
    },


    suc(res,file,fileList) {
        this.fileList.push({
            name: file.name,
            url: res.url
        })
        console.log(this.fileList)
    },
    handleRemove(file,fileList) {
        this.fileList.splice(this.fileList.findIndex(item => item.url === file.url),1)
    },
    handleExceed(files, fileList) {
        this.$message.warning(`上传文件不能超过1个`);
    },
    /** 上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。 */
    beforeUploadHandle(file) {
        const isLtM = file.size / 1024 / 1024 < this.sizeLimit
        if (!isLtM) {
            this.$message.error('上传文件大小不能超过 ' + this.sizeLimit + 'MB!')
        }
        return isLtM
    },
    /** 点击文件列表中已上传的文件时的钩子 */
    previewHandle(){
      let url = arguments[0].url
      // 下载
      downloadFileByUrl(url)
    },
  }
}
</script>

<style scoped>
 /*去除upload组件过渡效果*/
/deep/#upload-signed-file-dialog .el-upload-list__item {
    transition: none !important;
}
/*解决upload组件会闪一下的问题*/
/deep/#upload-signed-file-dialog .el-upload-list__item.is-ready {
    display: none;
}
</style>
