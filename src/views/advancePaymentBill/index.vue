<template>
  <section style="margin-bottom: 40px;padding-top: 15px;padding-left: 15px;display: inline-block;height: calc(max(100vh,100%) - 84px); width: 100% ">
    <div class="sp-title" style="font-size: 25px">
      我的申请
    </div>
    <el-divider />
    <!-- <el-col :span="12" style="width: 15%;height: 100%">
      <el-menu
        style="height: 100%"
        default-active="999"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
      >
        <el-menu-item index="999" @click="changemeun('all')">
          <span slot="title">全部</span>
        </el-menu-item>
        <el-submenu v-for="(item,index) in list" :index="String(index)" :key="index">
          <template slot="title">
            <span>{{ item.name }}</span>
          </template>
          <el-menu-item
            v-for="(items,indexs) in item.sysProcessDetailList"
            :key="String(index)+'-'+String(indexs)"
            :index="String(index)+'-'+String(indexs)"
            @click="changemeun(items.spare1)"
          >
            {{ items.name }}
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </el-col> -->
    <div style="width: 99%;margin-bottom: 10px;">
      <el-form :inline="true" :model="quary" class="query-form demo-form-inline">
        <el-form-item label="审批时间" label-width="70px">
          <el-date-picker
            v-model="quary.shipTime"
            style="margin-top:3px;"
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="状态" label-width="70px">
          <el-select v-model="quary.ongoing" style="width:100px;" placeholder="请选择">
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="进行中"
              value="1"
            />
            <el-option
              label="已结束"
              value="2"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="摘要" label-width="70px">
          <el-input v-model="quary.summary" placeholder="请输入摘要" clearable ></el-input>
        </el-form-item>
        <!-- <el-form-item label="付款信息" label-width="70px">
          <el-input v-model="quary.paymentSumm" placeholder="请输入付款信息" clearable></el-input>
        </el-form-item> -->
        <el-form-item label="对外付款账户" label-width="100px">
          <el-input v-model="quary.accQuery" placeholder="请输入付款账户" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onQuery">查询</el-button>
        </el-form-item>
      </el-form>
      <el-button style="float:right;margin-bottom: 3px;" @click="toExcel" size="mini"
        >导出Excel</el-button
      >
      <el-table
        v-loading="tableLoading"
        class="el-table-info mytable"
        :data="tableData"
        ref="table"
        stripe
        style="width: 100%;margin-top: 20px"
        :show-overflow-tooltip="true"
        border
      >
        <el-table-column
          prop="processname"
          :formatter="publicFmt"
          label="审批类型"
          align="center"
          width="100"
        />
        <el-table-column
          prop="briefContent"
          :formatter="publicFmt"
          label="流程摘要"
          align="center"
          width="350"
        >
        <template  slot-scope="scope">
          <div class="pointer" @click="showshenpi(scope.row.id,scope.row.zhanshiname)">
            {{scope.row.briefContent||'--'}}
          </div>
        </template>
        </el-table-column>
        <el-table-column
          prop="globalParam9"
          :formatter="ordercompanynameFmt"
          label="往来公司"
          align="center"
          width="100"
        />
        <el-table-column
          prop="globalParam3"
          :formatter="publicFmt"
          label="金额"
          align="center"
          width="100"
        />
        <el-table-column
          v-if="quary.name && showAccNameList.indexOf(quary.name)>-1"
          prop="accName"
          :formatter="publicFmt"
          label="收款账户"
          align="center"
          width="100"
        />
        <el-table-column
          prop="time"
          :formatter="publicFmt"
          label="当前节点"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.status === '4'">已撤回</div>
            <div v-if="scope.row.status === '2'">已完成</div>
            <div v-if="scope.row.status === '3'">已驳回</div>
            <div v-if="scope.row.status === '1'">{{ scope.row.pname !== undefined ? scope.row.pname:scope.row.rname }} <!--
             -->{{Math.floor(scope.row.statusId/10) == 8?'会计做账中':'审批中'}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sponsorTime"
          :formatter="publicFmt"
          label="创建日期"
          align="center"
          width="95"
        />
        <el-table-column
          label="操作"
          class-name="excel-hidden"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showshenpi(scope.row.id,scope.row.zhanshiname)">查看</el-button>
            <el-button type="text" size="small" @click="upload()">上传发票({{ scope.row.invoiceSum||0 }})</el-button>
            <el-button v-if="scope.row.status==='1' && !scope.row.isShenpi " type="text" size="small" @click="cancelProcess(scope.row.id)">撤回申请</el-button>
            <el-button v-if="isShowByCode(scope.row.flowCode) && ( scope.row.status==='4' || scope.row.status==='3' || scope.row.status==='2')" type="text" size="small" @click="replayProcess(scope.row)">重新申请</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        layout="prev, pager, next,sizes"
        :total="total"
        :page-size="quary.pageSize"
        :current-page.sync="quary.pageNum"
        :page-sizes="[10, 100, 200, 300, 500]"
        @size-change="handleSizeChange"
        style="text-align: right;padding: 10px 0px;background-color: #fff"
        @current-change="eventPage"
      />
    </div>
    <ProgressComponent ref="processDrawerqingjia" @refresh="refresh" @onCallback="handlerGlobalParams" />
  </section>
</template>

<script>
import { getProcessList, getMyprocess } from '@/api/business/processapi'
import { detailByCode as processDetails } from '@/api/system/sysProcessDetail'
import { cancelApply } from '@/api/system/process'
import dayjs from 'dayjs'
import Avue from '@smallwei/avue'
import ProgressComponent from '@/components/workflow/process'
import Vue from 'vue'
import prepared from '@/mixins/prepared'
import { saveAs } from "file-saver";
import XLSXStyle from "xlsx-style";
import XLSX from "xlsx";
import uploadFile from '@/components/UploadFile'
Vue.use(Avue)

export default {
  name: 'Myprocess',
  components: {
    ProgressComponent,
    uploadFile
  },
  mixins: [prepared(2)],
  data() {
    return {
      contractCode: 'contractFlow', // 合同code
      specialCode: ['reserveChangeBill', 'reserveApply', 'reserveBill', 'contractFlow'], // 特殊流程code
      showAccNameList:['Reimbursement','TravelReimbursement','CurrentAccountApplication'],
      ExternalAccount: {},
      total: 0,
      tableData: [
      ],
      tableLoading: false,
      quary: {
        time: '',
        name: 'PrepaymentReimbursement',
        summary: '',
        paymentSumm: '',
        accQuery: '',
        pageSize: 10,
        pageNum: 1,
        ongoing: ''
      },
      radio1: 'Form',
      list: [],
      sizeValue: 'small'
    }
  },
  created() {
    getProcessList().then(res => {
      if (res !== undefined && res.resultCode === '0') {
        this.list = res.data
        // console.log(this.list)
      }
      this.prepareSubmit()
    })
    const getFdbizCustomer = this.$store.dispatch('data/getFdbizCustomerListSaveInVuex')
    getFdbizCustomer.then(data => {
      console.log(data.fdbizCustomerList);
      // this.ExternalAccount = res.data
      for (const comp of data.fdbizCustomerList) {
        this.ExternalAccount[comp.id + ''] = comp.companyName
      }
      this.prepareSubmit()
    })
    this.preparedThen(() => {
      this.getPage()
    })
  },
  methods: {
    toExcel() {
      function parseElement(str) {
        var o = document.createElement("thead");
        o.innerHTML = str;
        return o.childNodes[0];
      }
      function lineHeight(sheet, skipFist = false, height = 30) {
        if (sheet["!rows"]) {
          sheet["!rows"] = [sheet["!rows"][0]];
        } else {
          sheet["!rows"] = [];
        }
        let range = XLSX.utils.decode_range(sheet["!ref"]);
        for (var i = skipFist ? 1 : 0; i <= range.e.r; i++) {
          sheet["!rows"].push({ hpt: height });
        }
      }
      function formatCell(worksheel, titleReg) {
        const borderAll = {
          //单元格外侧框线
          top: {
            style: "thin"
          },
          bottom: {
            style: "thin"
          },
          left: {
            style: "thin"
          },
          right: {
            style: "thin"
          }
        };

        let range = XLSX.utils.decode_range(worksheel["!ref"]);

        for (let C = range.s.c; C <= range.e.c; ++C) {
          for (let R = range.s.r; R <= range.e.r; ++R) {
            let cell = { c: C, r: R };
            let i = XLSX.utils.encode_cell(cell);

            if (i == "!ref" || i == "!cols" || i == "!rows") {
            } else {
              if (!worksheel[i]) {
                worksheel[i] = { t: "s", v: "" };
              }
              if (titleReg && titleReg.test(i)) {
                worksheel[i].s = {
                  // border: borderAll,
                  font: {
                    name: "宋体",
                    sz: 12,
                    color: { rgb: "000000" },
                    bold: false,
                    italic: false,
                    underline: false
                  },
                  alignment: {
                    horizontal: "left",
                    vertical: "center",
                    wrapText: true
                  }
                };
              } else {
                worksheel[i].s = {
                  border: borderAll,
                  font: {
                    name: "宋体",
                    sz: 12,
                    color: { rgb: "000000" },
                    bold: false,
                    italic: false,
                    underline: false
                  },
                  alignment: {
                    horizontal: "center",
                    vertical: "center",
                    wrapText: true
                  }
                };
              }

              if (/^(-?\d+)(\.\d+)?$/g.test(worksheel[i].v + "")) {
                worksheel[i].t = "n";
              }
            }
          }
          // //给所以单元格加上边框
          // for (var i in worksheel) {
          //     if (i == '!ref' || i == '!cols' || i == '!rows') {
          //     } else if (i == 'A1') {
          //       worksheel[i + ''].s = {
          //         // border: borderAll,
          //         font: {
          //           name: '宋体',
          //           sz: 12,
          //           color: {rgb: "000000"},
          //           bold: false,
          //           italic: false,
          //           underline: false
          //         },
          //         alignment: {
          //           horizontal: "left",
          //           vertical: "center",
          //           wrapText: true
          //         }

          //       }
          //     } else {

          //        worksheel[i + ''].s = {
          //         // border: borderAll,
          //         font: {
          //           name: '宋体',
          //           sz: 12,
          //           color: {rgb: "000000"},
          //           bold: false,
          //           italic: false,
          //           underline: false
          //         },
          //         alignment: {
          //           horizontal: "center",
          //           vertical: "center"
          //         }

          //       }
          //     }
        }
      }
      function s2ab(s) {
        if (typeof ArrayBuffer !== "undefined") {
          const buf = new ArrayBuffer(s.length);
          const view = new Uint8Array(buf);
          for (let i = 0; i !== s.length; ++i) {
            view[i] = s.charCodeAt(i) & 0xff;
          }
          return buf;
        } else {
          const buf = new Array(s.length);
          for (let i = 0; i !== s.length; ++i) {
            buf[i] = s.charCodeAt(i) & 0xff;
          }
          return buf;
        }
      }

      var workbook = XLSX.utils.book_new();

      // var table = document.querySelector('.excel-main table.el-table__body').cloneNode(true);
      var table = this.$refs.table.$el
        .querySelector("table.el-table__body")
        .cloneNode(true);
      // var thead = document.querySelector('.excel-main table.el-table__header thead').cloneNode(true);
      var thead = this.$refs.table.$el
        .querySelector("table.el-table__header thead")
        .cloneNode(true);
      // var footer = document.querySelector('.excel-main table.vxe-table--footer tfoot').cloneNode(true);

      while (thead.querySelector("th.gutter")) {
        thead.querySelector("th.gutter").remove();
      }

      var cc = 0;
      thead.querySelectorAll("th[colspan]").forEach(element => {
        // console.log(element)
        cc += parseInt(element.getAttribute("colspan"));
      });
      cc +=
        thead.querySelector("tr").querySelectorAll("th").length -
        thead.querySelectorAll("th[colspan]").length;

      while (thead.querySelector("th.excel-hidden")) {
        thead.querySelector("th.excel-hidden").remove();
      }
      while (table.querySelector("td.excel-hidden")) {
        table.querySelector("td.excel-hidden").remove();
      }
      // let headContentStr=`销项发票明细表`
      // // var headContentStr = document.getElementById('peizaidantitle').innerHTML + document.getElementById('peizaidan').innerHTML;
      // // headContentStr = headContentStr.replace(/(\<\/span\>)(\<span\s+)/g, '$1&nbsp;&nbsp;&nbsp;$2').replace(/(\<\/div\>)(\<div\s+)/g, '$1<br/>$2')
      // var headContent = parseElement(`<tr><th colspan='${cc}'>${headContentStr}</th></tr>`);
      // thead.insertBefore(headContent, thead.querySelector('tr'));
      table.insertBefore(thead, table.querySelector("tbody"));
      // table.appendChild(footer);
      var sheet = XLSX.utils.table_to_sheet(table, { raw: true });
      // document.getElementById('pzd-title').innerText.replace(/\//g, '_')
      XLSX.utils.book_append_sheet(workbook, sheet, "我的申请");
      formatCell(sheet, /^[A-Z]+1$/g);

      // sheet['!rows'] = [{hpx: 250}]
      // console.log(sheet)

      sheet["!rows"] = [{ hpt: 65 }];

      lineHeight(sheet, true);

      // workbook.finalize();
      var wbOut = XLSXStyle.write(workbook, {
        bookType: "xlsx",
        bookSST: false,
        type: "binary"
      });

      saveAs(
        new Blob([s2ab(wbOut)], { type: "application/octet-stream" }),
        `我的申请明细表.xlsx`
      );
    },
    isShowByCode(code) {
      return this.specialCode.indexOf(code) === -1
    },
    cancelProcess(id) {
      this.$prompt('请输入审批撤销原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        if (!value) {
            this.$message({
              showClose: true,
              type: 'error',
              message: '请输入撤销原因'
            });
            return
        }
         const loading = this.$loading({
            lock: true,
            text: '撤回中，请稍后！',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
        cancelApply(id, value).then((res) => {
          if (res.resultCode == '0') {
            this.getPage()
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: res.resultMsg||'撤回失败'
            });
          }

        }).finally(() => {
          loading.close()
        })
      })
    },
    showshenpi(id, name) {
      // console.log(id)
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    replayProcess({ id, flowCode, flowName }) {
      let defPage = 'SubmitProcess'
      let defType
      let defJian
      processDetails(flowCode).then(res => {
        if (res && res.length > 0) {
          if (res[0].spare3) {
            defPage = res[0].spare3
          }
          if (res[0].spare4) {
            defType = res[0].spare4
          }
          if (res[0].spare5) {
            defJian = res[0].spare5
          }
        }
        // console.log(defPage)
        var path = '/process/' + defPage
        this.$router.push({ path: path, query: { title: flowName, code: flowCode, type: defType, processJian: defJian, relyProcessId: id }})
      })

      // this.$router.push({
      //   path: '/workflow/process/replay',
      //   query: {
      //     id: id
      //   }
      // })
    },
    refresh() {
      // 审批完成后会调用刷新方法
    },
    handlerGlobalParams(globalParams) {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      console.log(globalParams)
    },
    handleSizeChange(v) {
      this.quary.pageSize = v;
      this.quary.pageNum = 1;
      this.getPage();
    },
    eventPage(e) {
      this.getPage()
    },
    changemeun(code='PrepaymentReimbursement') {
      this.quary.name = code
      console.log(code)
      this.getPage()
    },

    getPage() {
      this.tableData = []
      // this.total = 0
      console.log(this.quary)
      getMyprocess(this.quary).then(res => {
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.page
          this.total = res.total
        } else {
          this.total = 0
        }
      })
    },
    ordercompanynameFmt(row, column, cellValue, index) {
      if (!cellValue|| row.flowCode=='paymentApprove') {
        return '--'
      }
      // for (var a = 0; a < this.ExternalAccount.length; a++) {
      //   var item = this.ExternalAccount[a]
      //   if (item.id === Number(cellValue)) {
      //     return item.companyName
      //   }
      // }
      return this.ExternalAccount[cellValue] ? this.ExternalAccount[cellValue] : cellValue
      // return cellValue
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue == undefined || cellValue == null || cellValue == '') {
        return ' '
      }
      return dayjs(cellValue).format(fmtstr)
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },

    onQuery() {
      if (
        this.quary.shipTime !== undefined &&
        this.quary.shipTime !== null
      ) {
        this.quary.startTime = this.quary.shipTime[0]
        this.quary.endTime = this.quary.shipTime[1]
      } else {
        this.quary.startTime = undefined
        this.quary.endTime = undefined
      }

      this.quary.pageNum = 1
      this.getPage()
    },
    handleOpen(key, keyPath) {
      console.log(key, keyPath)
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath)
    }
  }
}
</script>

<style scoped>
.input-different{
  width: 200px;
  margin-left:60px;

}
.font{
  font-size:14px;
  color:#7E7E7E;
}
.el-table-info >>> .cell{
  text-align: center;
}
.el-table-info >>> th {
  background: #EDF5FF;
}
.mytable td  {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.mytable td .cell {
  padding-left: 0.1rem;
  padding-right: 0.1rem;
}
.el-table-info >>> .warning-cell{
  color: red;
}
.el-table-info >>> .success-cell{
  color: #6DD400;
}
.pointer{
  cursor: pointer;
color:#1890ff;
}
</style>
