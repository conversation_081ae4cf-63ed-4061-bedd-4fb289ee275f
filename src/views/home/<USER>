<template>
  <section style="padding-left: 3%;padding-top: 3vh;padding-bottom:4vh;background-color: #eff1f4">
    <div style="width: 100%;margin-bottom: 3vh;display: flex;">
      <div class="longbox">
        <div style="font-size: 25px;padding: 10px;">资金汇总日报</div>
        <div style="display: flex;padding: 10px;">
          <div style="line-height: 32px;height: 32px;">公司：</div>
          <el-select v-model="deptId" placeholder="请选择" :disabled="!useSelect" @change="changeDept">
            <el-option
              v-for="item in userCompanyIds "
              :key="item.deptId"
              :label="item.name"
              :value="item.deptId">
            </el-option>
          </el-select>
        </div>
        <div style="display: flex;margin: 10px 0;">
          <div style="text-align: center;cursor: pointer;flex-grow: 1;" @click="jumpToSysAccount">
              <div class="money-css">
                <span v-if="bankMoney && bankMoney !=0" style="font-size: 10px;line-height: 30px;">￥</span>
                <span v-if="!bankMoney || bankMoney ==0" style="font-size: 10px;line-height: 30px;"></span>
                {{NumFmt(bankMoney)}}</div>
              <div class="title-css" style="font-size: 10px;line-height: 30px;">银行人民币账户总额</div>
          </div>
          <div style="text-align: center;cursor: pointer;flex-grow: 1;" @click="jumpToSysAccount" v-if="bankMoneyDollar">
            <div class="money-css">
              <span v-if="bankMoneyDollar && bankMoneyDollar !=0" style="font-size: 10px;line-height: 30px;">$</span>
              <span v-if="!bankMoneyDollar || bankMoneyDollar ==0" style="font-size: 10px;line-height: 30px;"></span>
              {{NumFmt(bankMoneyDollar)}}
            </div>
            <div class="title-css" style="font-size: 10px;line-height: 30px;">银行美元账户总额</div>
          </div>
          <div style="text-align: center;cursor: pointer;flex-grow: 1;" @click="jumpToSysAccount">
              <div class="money-css">
                <span v-if="discount && discount !=0" style="font-size: 10px;line-height: 30px;">￥</span>
                <span v-if="!discount || discount ==0" style="font-size: 10px;line-height: 30px;"></span>
                {{NumFmt(discount)}}</div>
              <div class="title-css" style="font-size: 10px;line-height: 30px;">银行承兑总额</div>
          </div>
          <div style="text-align: center;cursor: pointer;flex-grow: 1;" @click="jumpToSysAccount">
              <div class="money-css">
                <span v-if="cash && cash !=0" style="font-size: 10px;line-height: 30px;">￥</span>
                <span v-if="!cash || cash ==0" style="font-size: 10px;line-height: 30px;"></span>
                {{NumFmt(cash)}}</div>
              <div class="title-css" style="font-size: 10px;line-height: 30px;">库存现金</div>
          </div>
        </div>
        <div style="display: flex;margin: 10px 0;">
          <div style="text-align: center;cursor: pointer;flex-grow: 1;" @click="jumpTo('payment')">
            <div class="count-css">{{paymentCount}}</div>
            <div class="count-title-css">待付款</div>
            <div class="bg1-css"></div>
          </div>
          <div style="text-align: center;cursor: pointer;flex-grow: 1;" @click="jumpTo('onAccount')">
            <div class="count-css">{{onAccountCount}}</div>
            <div class="count-title-css">待挂账(物流)</div>
            <div class="bg2-css"></div>
          </div>
        </div>
      </div>
      <div class="shortbox">
        <el-tabs v-model="activeName2" style="margin-left: 14px;margin-top: 10px" @tab-click="handleClick">
          <el-tab-pane label="财务常用流程" name="first" style="font-weight: 400">
<!--            <div style="margin-top:15px;">-->
<!--              <div-->
<!--                class="fontclass"-->
<!--                style="cursor:pointer;display: inline-block;width: 40%;margin-right: 5%;font-weight: 400"-->
<!--                @click="Goprocess(1)"-->
<!--              >-->
<!--                挂帐代办-->
<!--              </div>-->
<!--              <div class="fontclass" style="display: inline-block;width: 40%;text-align: center">财务工作</div>-->
<!--            </div>-->
<!--            <div style="margin-top:15px;">-->
<!--              <div-->
<!--                class="fontclass"-->
<!--                style="cursor:pointer;display: inline-block;width: 40%;margin-right: 5%;font-weight: 400"-->
<!--                @click="Goprocess(2)"-->
<!--              >-->
<!--                付款代办-->
<!--              </div>-->
<!--              <div class="fontclass" style="display: inline-block;width: 40%;text-align: center">财务工作</div>-->
<!--            </div>-->
            <div style="margin-top:15px;">
              <div
                class="fontclass"
                style="cursor:pointer;display: inline-block;width: 40%;margin-right: 5%;font-weight: 400"
                @click="Goprocess(3)"
              >
                差异调整（财务）
              </div>
              <div class="fontclass" style="display: inline-block;width: 40%;text-align: center">财务工作</div>
            </div>
<!--            <div style="margin-top:15px;">-->
<!--              <div-->
<!--                class="fontclass"-->
<!--                style="cursor:pointer;display: inline-block;width: 40%;margin-right: 5%;font-weight: 400"-->
<!--                @click="Goprocess(4)"-->
<!--              >-->
<!--                收款台账-->
<!--              </div>-->
<!--              <div class="fontclass" style="display: inline-block;width: 40%;text-align: center">财务工作</div>-->
<!--            </div>-->

          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div style="width: 100%;display: flex;">
      <div class="longbox">
        <div style="position: relative;float:right; top:15px;right: 40px;z-index:999">
          <el-button type="text" style="color: black;font-size: 14px" @click="toBusinessReceive()">更多</el-button>
        </div>
        <el-tabs v-model="activeName3" style="margin-left: 14px;margin-top: 10px" @tab-click="handleClick1">
          <el-tab-pane label="收款待核销" name="first">
            <div v-for="item in showreceiving" style="margin-top: 15px; ">
              <!--             <div v-if="item.id === 4" style="display: inline-block;width: 40%;margin-right: 5%">-->
              <!--               差旅报销申请-船舶业务部-王新<el-tag style="margin-left: 5px">业务</el-tag>-->
              <!--             </div>-->
              <div
                class="fontclass"
                style="cursor:pointer;display: inline-block;width: 35%;margin-right: 5%"
              >{{ item.customerName }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 15%;margin-right: 5%;text-align: center">{{ item.balance }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 30%;text-align: center">{{ timeFmt(item.createTime,'YYYY/MM/DD HH:mm') }}</div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="收款已核销" name="second">
            <div v-for="item in showreceved" style="margin-top: 15px; ">
              <!--             <div v-if="item.id === 4" style="display: inline-block;width: 40%;margin-right: 5%">-->
              <!--               差旅报销申请-船舶业务部-王新<el-tag style="margin-left: 5px">业务</el-tag>-->
              <!--             </div>-->
              <div
                class="fontclass"
                style="cursor:pointer;display: inline-block;width: 35%;margin-right: 5%"
              >{{ item.customerName }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 15%;margin-right: 5%;text-align: center">{{ item.balance }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 30%;text-align: center">{{ timeFmt(item.createTime,'YYYY/MM/DD HH:mm') }}</div>
            </div>
          </el-tab-pane>
        </el-tabs>

      </div>
      <div class="shortbox">
        <el-tabs v-model="activeName4" style="margin-left: 14px;margin-top: 10px" @tab-click="handleClick">
          <el-tab-pane v-loading="loading" label="通讯录" name="first">
            <el-input
              v-model="souvalue"
              placeholder="请输入手机号/姓名"
              style="width: 90%"
              clearable
              @input="remoteMethod()"
            />
            <!--            <el-select-->
            <!--              v-model="value"-->
            <!--              multiple-->
            <!--              filterable-->
            <!--              remote-->
            <!--              style="width: 90%"-->
            <!--              reserve-keyword-->
            <!--              placeholder="搜索姓名/手机号"-->
            <!--              :remote-method="remoteMethod"-->
            <!--              :loading="loading"-->
            <!--            >-->
            <!--              <el-option v-for="item in options">-->
            <!--                <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">-->
            <!--                  {{ item.name }}-->
            <!--                </div>-->
            <!--                <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">{{ item.dname }}</div>-->
            <!--                <div style="display: inline-block;width: 40%;text-align: center;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">{{ item.phone }}</div>-->
            <!--              </el-option>-->
            <!--            </el-select>-->
            <!--            <div v-if="tongnum === 0">-->
            <!--              <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">-->
            <!--                <span style="color: #00a0e9">最近</span><span> | </span><span @click="tongnum = 1"-->
            <!--              style="cursor:pointer;">同部门</span><span> | </span><span @click="tongnum = 2" style="cursor:pointer;">我的下属</span>-->
            <!--              </div>-->
            <!--              <div style="height: 300px;overflow-y: auto">-->
            <!--                <div v-for="item in list" style="margin-top: 15px">-->
            <!--                  <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">-->
            <!--                    赵子龙-->
            <!--                  </div>-->
            <!--                  <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">行政部</div>-->
            <!--                  <div style="display: inline-block;width: 40%;text-align: center">11223344551</div>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--            </div>-->
            <div v-if="tongnum === 0">
              <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">
                <span style="color: #00a0e9">全部</span><span> | </span>
                <span style="cursor:pointer;" @click="changetongnum(1)">同部门</span><span> | </span>
                <span style="cursor:pointer;" @click="changetongnum(2)">我的下属</span>
              </div>
              <div style="height: calc(39.5vh - 170px);overflow-y: auto">
                <div v-for="item in showmyphonelist" style="margin-top: 15px">
                  <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">
                    {{ item.name }}
                  </div>
                  <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">
                    {{ item.dname }}
                  </div>
                  <div style="display: inline-block;width: 40%;text-align: center">{{ item.phone }}</div>
                </div>
              </div>
            </div>
            <div v-if="tongnum === 1">
              <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">
                <span style="cursor:pointer;" @click="changetongnum(0)">全部</span><span> | </span>
                <span style="color: #00a0e9">同部门</span><span> | </span>
                <span style="cursor:pointer;" @click="changetongnum(2)">我的下属</span>
              </div>
              <div style="height: calc(39.5vh - 170px);overflow-y: auto">
                <div v-for="item in showmydepartment" style="margin-top: 15px">
                  <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;overflow-y:hidden;white-space:nowrap;text-overflow:ellipsis">
                    {{ item.name }}
                  </div>
                  <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;overflow-y:hidden;white-space:nowrap;text-overflow:ellipsis">{{
                    item.dname
                  }}
                  </div>
                  <div style="display: inline-block;width: 40%;text-align: center">{{ item.phone }}</div>
                </div>
              </div>
            </div>
            <div v-if="tongnum === 2">
              <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">
                <span style="cursor:pointer;" @click="changetongnum(0)">全部</span><span> | </span>
                <span style="cursor:pointer;" @click="changetongnum(1)">同部门</span><span> | </span>
                <span style="color: #00a0e9">我的下属</span>
              </div>
              <div style="height: calc(39.5vh - 170px);overflow-y: auto">
                <div v-for="item in showmysubordinates" style="margin-top: 15px">
                  <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">
                    {{ item.name }}
                  </div>
                  <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">{{ item.dname }}</div>
                  <div style="display: inline-block;width: 40%;text-align: center">{{ item.phone }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <ProgressComponent ref="processDrawerqingjia" style="z-index:9999" @refresh="refresh" @onCallback="handlerGlobalParams" />
  </section>
</template>

<script>
import { getHome } from '@/api/business/processintanceapi'
import { getProcessList } from '@/api/business/processapi'
import {getReceiveWater} from '@/api/system/receiveWater'
import Avue from '@smallwei/avue'
import ProgressComponent from '@/components/workflow/process'
import Vue from 'vue'
import dayjs from 'dayjs'
import {getOnAccountCount} from "../../api/system/onAccount";
import {getSysAccountInfo} from "../../api/system/sysAccount";
import {getPaymentCount} from "../../api/system/paymentWater";
import Cookies from "js-cookie";
import {getAllPlateDept, getDeptInfo} from "../../api/business/wxDepartmentapi";
import {userRole} from "../../api/system/jurisdiction";

Vue.use(Avue)

export default {
  name: 'Caiwu',
  components: {
    ProgressComponent
  },
  data() {
    return {
      mysubordinates: [],
      mydepartment: [],
      myphonelist: {},
      showmysubordinates: [],
      showmydepartment: [],
      showmyphonelist: {},
      showreceiving:[],
      showreceved:[],
      my: [],
      tongnum: 0,
      activeName1: 'first',
      activeName2: 'first',
      activeName3: 'first',
      activeName4: 'first',
      list: [
        { id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }
      ],
      options: [],
      value: [],
      list1: [],
      souvalue: '',
      loading: false,
      processing: [],
      processed: [],
      myprocess: [],
      processlist: [],
      query:{
        pageSize:99,
        pageNo:1
      },
      onAccountCount:0,
      paymentCount:0,
      bankMoney:0,
      bankMoneyDollar:0,
      discount:0,
      cash:0,
      deptId:null,
      userCompanyIds:null,
      useSelect:true,
      userRole:["allRole"],
    }
  },
  mounted() {
    if(userRole(this.userRole)){
      getAllPlateDept().then(res=>{
        if(res){
          this.userCompanyIds = res.list
          if(!res.list || res.list.length == 0){
            this.deptId = null
            this.useSelect = false
          } else if(res.list != null && res.list.length == 1){
            this.deptId = res.list[0].deptId
            this.useSelect = false
          } else {
            this.deptId = res.list[0].deptId
            this.useSelect = true
          }
          this.initData()
        }
      })
    } else {
      getDeptInfo().then(res=>{
        if(res){
          this.userCompanyIds = res.list
          if(!res.list || res.list.length == 0){
            this.deptId = null
            this.useSelect = false
          } else if(res.list != null && res.list.length == 1){
            this.deptId = res.list[0].deptId
            this.useSelect = false
          } else {
            this.deptId = res.list[0].deptId
            this.useSelect = true
          }
          this.initData()
        }
      })
    }

    getHome().then(res => {
      if (res !== undefined && res.resultCode === '0') {
        this.processing = res.processing
        this.processed = res.processed
        this.myprocess = res.Myprocess
        this.myphonelist = res.phonelist
        this.showmyphonelist = res.phonelist
        for (var a = 0; a < this.myphonelist.length; a++) {
          var ress = this.myphonelist[a]
          if (ress.deptid === res.myphone.deptid) {
            this.mydepartment.push(ress)
            this.showmydepartment.push(ress)
          }
          if (ress.parentid === res.myphone.deptid) {
            this.mysubordinates.push(ress)
            this.showmysubordinates.push(ress)
          }
        }
        if (this.mydepartment.length === 0) {
          this.tongnum = 2
        }
        if (this.mysubordinates.length === 0) {
          this.tongnum = 1
        }
      }
    })
    getReceiveWater(this.query).then(response => {
      let data = response.page;
      console.log(data)
      this.dataList = data.records;
      this.total = data.total;
      for (var a = 0 ; a < data.records.length; a++){
        var item = data.records[a]
        if (item.applyStatus === 0) {
          if (this.showreceiving.length < 8){
            console.log( this.showreceiving)
            this.showreceiving.push(item)
          }
        } else if (item.applyStatus === 1) {
          if (this.showreceved.length < 8){
            this.showreceved.push(item)
          }
        }

      }
    }).finally(() => {
      this.loading = false;
    });
    getProcessList().then(res => {
      if (res !== undefined && res.resultCode === '0') {
        for (var a = 0; a < res.data.length; a++) {
          var item = res.data[a]
          for (var z = 0; z < item.sysProcessDetailList.length; z++) {
            var items = item.sysProcessDetailList[z]
            if (items.spare2 === '1') {
              items.parent = item.name
              this.processlist.push(items)
            }
          }
        }
      }
    })
  },
  methods: {
    jumpToSysAccount(){
      var path = "/allform"
      this.$router.push({ path: path, query: { deptId: this.deptId}})
    },
    jumpTo(type){
      var paymentPath ="/Payment/BusinessPayment"
      var onAccountPath ="/OnAccount/onAccount"
      var path = null
      if(type == "payment"){
        path = paymentPath
      } else if(type == "onAccount") {
        path = onAccountPath
      }
      this.$router.push({ path: path})
    },
    changeDept(data){
      console.info(data)
      this.deptId = data
      this.initData()
    },
    initData(){
      var onAccount = getOnAccountCount()
      var paymentCount = getPaymentCount(this.deptId)
      var sysAccountInfo = getSysAccountInfo(this.deptId)
      Promise.all([onAccount,paymentCount,sysAccountInfo]).then(ress=>{
        let res1  = ress[0];
        if(res1){
          this.onAccountCount = res1.data
        }
        let res2  = ress[1];
        if(res2){
          this.paymentCount = res2.data
        }
        let res3  = ress[2];
        if(res3){
          this.bankMoney = res3.bankMoney
          this.discount = res3.discount
          this.cash = res3.cash
          this.bankMoneyDollar = res3.bankMoneyDollar
        }
      })
    },
    topage(){
      getHome().then(res => {
        if (res !== undefined && res.resultCode === '0') {
          console.log(res)
          this.processing = res.processing
          this.processed = res.processed
          this.myprocess = res.Myprocess
          this.myphonelist = res.phonelist
          this.showmyphonelist = res.phonelist
          for (var a = 0; a < this.myphonelist.length; a++) {
            var ress = this.myphonelist[a]
            if (ress.deptid === res.myphone.deptid) {
              this.mydepartment.push(ress)
              this.showmydepartment.push(ress)
            }
            if (ress.parentid === res.myphone.deptid) {
              this.mysubordinates.push(ress)
              this.showmysubordinates.push(ress)
            }
          }
          if (this.mydepartment.length === 0) {
            this.tongnum = 2
          }
          if (this.mysubordinates.length === 0) {
            this.tongnum = 1
          }
          console.log(res)
        }
      })
    },

    toBusinessReceive() {
      this.$router.push({ path: '/Receive/BusinessReceive' })
    },
    toallprocess() {
      this.$router.push({ path: '/process/AllProcess' })
    },
    changetongnum(a) {
      this.tongnum = a
      this.remoteMethod()
    },

    handleClick(tab, event) {
      console.log(tab, event)
    },
    handleClick1(tab, event) {
      console.log(tab, event)
    },
    Goprocess(a) {
      if (a === 1) {
        this.$router.push({ path: '/OnAccount/onAccount' })
      } else if (a === 2) {
        this.$router.push({ path: '/Payment/BusinessPayment' })
      } else if (a === 3) {
        this.$router.push({ path: '/otherPayment' })
      } else if (a === 4) {
        this.$router.push({ path: '/Receive/BusinessReceive' })
      }
    },
    showshenpi(id, name) {
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    refresh() {
      this.topage()
    },
    handlerGlobalParams(globalParams) {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      console.log(**********)
    },
    timeFmt(time, fmtstr) {
      if (time === undefined || time === null || time === '') {
        return ' '
      }
      const i = new Date(time)
      return dayjs(i).format(fmtstr)
    },
    remoteMethod() {
      var query = this.souvalue
      this.options = []
      var list = {}
      if (query !== '') {
        if (this.tongnum === 0) {
          list = this.myphonelist
        } else if (this.tongnum === 1) {
          list = this.mydepartment
        } else if (this.tongnum === 2) {
          list = this.mysubordinates
        }
        this.loading = true
        setTimeout(() => {
          for (var a = 0; a < list.length; a++) {
            var item = list[a]
            if (item.name.indexOf(query) !== -1) {
              this.options.push(item)
            }
            if (item.phone.indexOf(query) !== -1) {
              this.options.push(item)
            }
          }
          if (this.tongnum === 0) {
            this.showmyphonelist = this.options
          } else if (this.tongnum === 1) {
            this.showmydepartment = this.options
          } else if (this.tongnum === 2) {
            this.showmysubordinates = this.options
          }
          console.log(this.options)
          this.loading = false
        }, 500)
      } else {
        this.showmyphonelist = this.myphonelist
        this.showmydepartment = this.mydepartment
        this.showmysubordinates = this.mysubordinates
      }
    },
    NumFmt(cellValue){
      var v = 0
      if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
        v = parseFloat(cellValue.toFixed(2)) && parseFloat(cellValue.toFixed(2)).toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
      }
      return v
    },
  }
}
</script>

<style scoped>
.longbox {
  overflow-y: hidden ;
  display: inline-block;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 64%;
  height: 39.5vh;
  margin-right: 3%;
  background-color: white
}

.shortbox {
  overflow-y: hidden ;
  display: inline-block;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 30%;
  height: 39.5vh;
  background-color: white
}
.fontclass{
  overflow-x:hidden;
  overflow-y:hidden;
  white-space:nowrap;
  text-overflow:ellipsis
}
  .money-css{
    font-size: 30px;
    font-weight: 600;
  }
  .title-css{
    font-size: 15px;
    font-weight: 500;
    color: #6A6C70;
  }
  .count-css{
    font-size: 40px;
    font-weight: 600;
  }
  .count-title-css{
    font-size: 20px;
    font-weight: 600;
    color: #6A6C70;
    margin: 5px 0;
  }
  .bg1-css{
    margin:0 25%;
    width: 50%;
    height: 5px;
    border: 1px solid #6AC144;
    border-radius:0 0 90px 90px;
    background-color: #6AC144;
  }

.bg2-css{
  margin:0 25%;
  width: 50%;
  height: 5px;
  border: 1px solid #e6a23c;
  border-radius:0 0 90px 90px;
  background-color: #e6a23c;
}
</style>
