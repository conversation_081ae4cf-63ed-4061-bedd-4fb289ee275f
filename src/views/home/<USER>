<template>
  <section style="width: 100%; padding-left: 3%;padding-top: 3vh;padding-bottom:4vh;background-color: #eff1f4">
    <!-- style="width: 100%;margin-bottom: 3vh;display: flex;" -->
    <div :class="showMoney?'gridcls':'gridNoMoneycls'" class="aniGrid">
      <div class="longbox conlt">
        <div style="position: relative;float:right; top:15px;right: 40px;z-index:999">
          <el-button type="text" style="color: black;font-size: 14px" @click="toMyprocessing()">更多</el-button>
        </div>
        <el-tabs v-model="activeName1" style="margin-left: 14px;margin-top: 10px" @tab-click="handleClick">
          <el-tab-pane label="待办事项" name="first">
            <div v-for="item in processing" style="margin-top: 15px; ">
              <!--             <div v-if="item.id === 4" style="display: inline-block;width: 40%;margin-right: 5%">-->
              <!--               差旅报销申请-船舶业务部-王新<el-tag style="margin-left: 5px">业务</el-tag>-->
              <!--             </div>-->
              <div
                class="fontclass"
                style="cursor:pointer;display: inline-block;width: 35%;margin-right: 5%"
                @click="showshenpi(item.id,item.zhanshiname)"
              >{{ item.zhanshiname }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 15%;margin-right: 5%;text-align: center">{{ item.companyname }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 8%;margin-right: 5%;text-align: center">{{ item.name }}</div>
              <div class="fontclass" style="display: inline-block;width: 20%;text-align: center">{{ timeFmt(item.sponsorTime,'MM/DD HH:mm') }}</div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="已办事项" name="second">
            <div v-for="item in processed" style="margin-top: 15px; ">
              <!--              <div v-if="item.id === 4" style="display: inline-block;width: 40%;margin-right: 5%">-->
              <!--                差旅报销申请-船舶业务部-王新<el-tag style="margin-left: 5px">业务</el-tag>-->
              <!--              </div>-->
              <div
                class="fontclass"
                style="cursor:pointer;display: inline-block;width: 35%;margin-right: 5%"
                @click="showshenpi(item.id,item.zhanshiname)"
              >{{ item.zhanshiname }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 15%;margin-right: 5%;text-align: center">{{ item.companyname }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 8%;margin-right: 5%;text-align: center">{{ item.name }}</div>
              <div class="fontclass" style="display: inline-block;width: 20%;text-align: center">{{ timeFmt(item.sponsorTime,'MM/DD HH:mm') }}</div>
            </div>
          </el-tab-pane>
        </el-tabs>

      </div>
      <div class="shortbox conrt">
        <div v-show="activeName2=='first'" style="position: relative;float:right; top:15px;right: 40px;z-index:999">
          <el-button type="text" style="color: black;font-size: 14px" @click="toallprocess">更多</el-button>
        </div>
        <el-tabs v-model="activeName2" style="margin-left: 14px;margin-top: 10px" @tab-click="handleClick">
          <el-tab-pane v-if="showMoney" label="资金统计" name="deptMoney" style="font-weight: 400;">
            <div style="margin-right: 30px;">
              <div style="display: flex;justify-content: center;">
                <el-select v-model="selIndexDept" placeholder="请选择公司" @change="handleDeptChange">
                  <el-option v-for="(item,index) in deptMoneyDeptNames" :key="index" :label="item.label" :value="index"></el-option>
                </el-select>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <el-date-picker
                  v-model="mdayrange"
                  type="daterange"
                  @change="handleDateChange"
                  size="small"
                  range-separator="至"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </div>
              <div class="gridchartcls">
                <div>
                  <div id="main1" style="width: 100%;height: 230px;"/>
                  <div style="text-align: center;">合计：{{ moneyFmt(deptMoneySelect.incomeMoney) }}</div>
                </div>
                <div>
                  <div id="main2" style="width: 100%;height: 230px;"/>
                  <div style="text-align: center;">合计：{{ moneyFmt(deptMoneySelect.payMoney) }}</div class="cen">
                </div>
                <div>
                  <el-table
                    ref="tabSr"
                    :key="tableKey"
                    v-loading="loadingMap"
                    :data="deptShouRuData2Child"
                    :span-method="arraySpanMethod"
                    border
                    stripe
                    size="mini"
                  >
                  <el-table-column prop="oneLabel" align="center" label="一级分类"  ></el-table-column>
                  <el-table-column prop="label" align="center" label="二级分类" ></el-table-column>
                  <el-table-column prop="money" align="center" label="金额" width="100" :formatter="moneyTbFmt"></el-table-column>
                  <el-table-column prop="percent" align="center" label="占比" :formatter="moneyBfb" ></el-table-column>
                  <el-table-column  align="center" label="明细" >
                      <template slot-scope="scope">
                        <el-button v-show="scope.row.label" type="text" size="mini" @click="showDetail(scope.row,'rec')">查看</el-button>
                      </template>
                    </el-table-column>
                </el-table>
                </div>
                <div>

                  <el-table
                    ref="tabZc"
                    :key="tableKey"
                    v-loading="loadingMap"
                    :data="deptZhiChuData2Child"
                    border
                    stripe
                    :span-method="arrayZhiChuSpanMethod"
                    size="mini"
                  >
                  <el-table-column prop="oneLabel" align="center" label="一级分类" ></el-table-column>
                  <el-table-column prop="label" align="center" label="二级分类" ></el-table-column>
                  <el-table-column prop="money" align="center" label="金额" width="100" :formatter="moneyTbFmt"></el-table-column>
                  <el-table-column prop="percent" align="center" label="占比" :formatter="moneyBfb" ></el-table-column>
                  <el-table-column  align="center" label="明细" >
                      <template slot-scope="scope">
                        <el-button v-show="scope.row.label" type="text" size="mini" @click="showDetail(scope.row,'out')">查看</el-button>
                      </template>
                    </el-table-column>
                </el-table>
                </div>
                <!-- <div> -->


                  <!-- 图形下面用表格的形式按照占比从大到小的顺序列出各费用分类的金额和占比 -->
                  <!-- 收入表格、支出表格 -->
                  <!-- <el-table
                    v-loading="loadingMap"
                    :data="deptShouMoneyList"
                    border
                    stripe
                    size="mini"
                  >
                    <el-table-column prop="fundsName" align="center" label="费用分类" ></el-table-column>
                    <el-table-column prop="money" align="center" label="金额" width="100" :formatter="moneyTbFmt"></el-table-column>
                    <el-table-column prop="percent" align="center" label="占比" :formatter="moneyBfb" ></el-table-column>
                    <el-table-column  align="center" label="明细" >
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="showDetail(scope.row,'rec')">查看</el-button>
                      </template>
                    </el-table-column>
                  </el-table> -->
                <!-- </div> -->
                <!-- <div>
                  <el-table
                    v-loading="loadingMap"
                    :data="deptZhiMoneyList"
                    border
                    stripe
                    size="mini"
                  >
                    <el-table-column prop="fundsName" align="center" label="费用分类" ></el-table-column>
                    <el-table-column prop="money" align="center" label="金额" width="100" :formatter="moneyTbFmt"></el-table-column>
                    <el-table-column prop="percent" align="center" label="占比" :formatter="moneyBfb" ></el-table-column>
                    <el-table-column  align="center" label="明细" >
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="showDetail(scope.row,'out')">查看</el-button>
                      </template>
                    </el-table-column>
                  </el-table>

                </div> -->
              </div>

            </div>



          </el-tab-pane>
          <el-tab-pane label="常用流程" name="first" style="font-weight: 400">
            <div v-for="item in processlist" style="margin-top: 15px">
              <div
                class="fontclass"
                style="cursor:pointer;display: inline-block;width: 40%;margin-right: 5%;font-weight: 400"
                @click="Goprocess(item)"
              >
                {{ item.name }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 40%;text-align: center">{{ item.parent }}</div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    <!-- </div>
    <div style="width: 100%;display: flex;"> -->
      <div class="longbox conlb">
        <div style="position: relative;float:right; top:15px;right: 40px;z-index:999">
          <el-button type="text" style="color: black;font-size: 14px" @click="toMyprocess()">更多</el-button>
        </div>
        <el-tabs v-model="activeName3" style="margin-left: 14px;margin-top: 10px" @tab-click="handleClick">
          <el-tab-pane label="我的申请" name="first">
            <div
              v-for="item in myprocess"
              style="margin-top: 15px;font-weight: 400"
              :style="{color:item.statusId ===999 ? '#797979':'#000000'}"
            >
              <!--              <div v-if="item.id === 1" style="display: inline-block;width: 40%;margin-right: 5%">-->
              <!--                差旅报销申请-船舶业务部-王新<el-tag style="margin-left: 5px">业务</el-tag>-->
              <!--              </div>-->
              <div
                class="fontclass"
                style="cursor:pointer;display: inline-block;width: 35%;margin-right: 5%"
                @click="showshenpi(item.id,item.zhanshiname)"
              >{{ item.zhanshiname }}
              </div>
              <div
                v-if="item.status=== '2' "
                class="fontclass"
                style="display: inline-block;width: 15%;margin-right: 5%;text-align: center"
              >已完成
              </div>
              <div
                v-if="item.status==='3' "
                class="fontclass"
                style="display: inline-block;width: 15%;margin-right: 5%;text-align: center"
              >已驳回
              </div>
                 <div
                v-if="item.status==='4' "
                class="fontclass"
                style="display: inline-block;width: 15%;margin-right: 5%;text-align: center"
              >已撤回
              </div>
              <div v-if="item.status=== '1' " class="fontclass" style="display: inline-block;width: 15%;margin-right: 5%;text-align: center">
               {{ fmtNodeName(item)  }}<!--

                -->{{ fmtIng(fmtNodeName(item),item.statusId ) }}
              </div>
              <div class="fontclass" style="display: inline-block;width: 8%;margin-right: 5%;text-align: center">{{ item.pname||'--' }}</div>
              <div class="fontclass" style="display: inline-block;width: 20%;text-align: center">{{ timeFmt(item.sponsorTime,'MM/DD HH:mm') }}</div>
            </div>

          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="shortbox conrb">
        <el-tabs v-model="activeName4" style="margin-left: 14px;margin-top: 10px" @tab-click="handleClick">
          <el-tab-pane v-loading="loading" label="通讯录" name="first">
            <el-input
              v-model="souvalue"
              placeholder="请输入手机号/姓名"
              style="width: 90%"
              clearable
              @input="remoteMethod()"
            />
            <!--            <el-select-->
            <!--              v-model="value"-->
            <!--              multiple-->
            <!--              filterable-->
            <!--              remote-->
            <!--              style="width: 90%"-->
            <!--              reserve-keyword-->
            <!--              placeholder="搜索姓名/手机号"-->
            <!--              :remote-method="remoteMethod"-->
            <!--              :loading="loading"-->
            <!--            >-->
            <!--              <el-option v-for="item in options">-->
            <!--                <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">-->
            <!--                  {{ item.name }}-->
            <!--                </div>-->
            <!--                <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">{{ item.dname }}</div>-->
            <!--                <div style="display: inline-block;width: 40%;text-align: center;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">{{ item.phone }}</div>-->
            <!--              </el-option>-->
            <!--            </el-select>-->
            <!--            <div v-if="tongnum === 0">-->
            <!--              <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">-->
            <!--                <span style="color: #00a0e9">最近</span><span> | </span><span @click="tongnum = 1"-->
            <!--              style="cursor:pointer;">同部门</span><span> | </span><span @click="tongnum = 2" style="cursor:pointer;">我的下属</span>-->
            <!--              </div>-->
            <!--              <div style="height: 300px;overflow-y: auto">-->
            <!--                <div v-for="item in list" style="margin-top: 15px">-->
            <!--                  <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">-->
            <!--                    赵子龙-->
            <!--                  </div>-->
            <!--                  <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">行政部</div>-->
            <!--                  <div style="display: inline-block;width: 40%;text-align: center">11223344551</div>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--            </div>-->

            <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">
              <span style="cursor:pointer;" :class="tongnum === 0?'tactive':''" @click="changetongnum(0)">全部</span><span> | </span>
              <span style="cursor:pointer;" :class="tongnum === 1?'tactive':''" @click="changetongnum(1)">同部门</span><span> | </span>
              <span style="cursor:pointer;"  :class="tongnum === 2?'tactive':''"@click="changetongnum(2)">我的下属</span>
            </div>
            <!-- height: calc(39.5vh - 170px); -->
            <div style="overflow-y: auto">
              <div v-for="item in contactList" style="margin-top: 15px">
                <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">
                  {{ item.name }}
                </div>
                <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">
                  {{ item.dname }}
                </div>
                <div style="display: inline-block;width: 40%;text-align: center">{{ item.phone }}</div>
              </div>
            </div>
            <!-- <div v-if="tongnum === 1">
              <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">
                <span style="cursor:pointer;" @click="changetongnum(0)">全部</span><span> | </span>
                <span style="color: #00a0e9">同部门</span><span> | </span>
                <span style="cursor:pointer;" @click="changetongnum(2)">我的下属</span>
              </div>
              <div style="height: calc(39.5vh - 170px);overflow-y: auto">
                <div v-for="item in showmydepartment" style="margin-top: 15px">
                  <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;overflow-y:hidden;white-space:nowrap;text-overflow:ellipsis">
                    {{ item.name }}
                  </div>
                  <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;overflow-y:hidden;white-space:nowrap;text-overflow:ellipsis">{{
                    item.dname
                  }}
                  </div>
                  <div style="display: inline-block;width: 40%;text-align: center">{{ item.phone }}</div>
                </div>
              </div>
            </div> -->
            <!-- <div v-if="tongnum === 2">
              <div style="font-size: 13px;margin-top: 15px;margin-bottom: 20px">
                <span style="cursor:pointer;" @click="changetongnum(0)">全部</span><span> | </span>
                <span style="cursor:pointer;" @click="changetongnum(1)">同部门</span><span> | </span>
                <span style="color: #00a0e9">我的下属</span>
              </div>
              <div style="height: calc(39.5vh - 170px);overflow-y: auto">
                <div v-for="item in showmysubordinates" style="margin-top: 15px">
                  <div style="display: inline-block;width: 20%;margin-right: 5%;font-weight: 400;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">
                    {{ item.name }}
                  </div>
                  <div style="display: inline-block;width: 20%;text-align: center;margin-right: 5%;overflow-x:hidden;word-break:keep-all;white-space:nowrap;text-overflow:ellipsis">{{ item.dname }}</div>
                  <div style="display: inline-block;width: 40%;text-align: center">{{ item.phone }}</div>
                </div>
              </div>
            </div> -->
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <ProgressComponent ref="processDrawerqingjia" style="z-index:9999" @refresh="refresh" @onCallback="handlerGlobalParams" />
  </section>
</template>

<script>
import { getHome } from '@/api/business/processintanceapi'
import { getProcessList } from '@/api/business/processapi'
import {fundsAnalysisByDeptPlateAndDate} from '@/api/system/fundsClassification'
import Avue from '@smallwei/avue'
import ProgressComponent from '@/components/workflow/process'
import Vue from 'vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts';
import currency from 'currency.js';
require('echarts/theme/macarons') // echarts theme

Vue.use(Avue)

export default {
  name: 'Home',
  components: {
    ProgressComponent
  },
  data() {
    return {
      tableKey: 0,
      // 日期范围，3号前默认为上月，3号后默认为本月
      // 判断当前日期是否大于3号
      mdayrange: dayjs().date() > 3 ? [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
        : [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')],
      mysubordinates: [],
      mydepartment: [],
      myphonelist: {},
      showmysubordinates: [],
      showmydepartment: [],
      showmyphonelist: {},
      showMoney: false,
      my: [],
      tongnum: 0,
      activeName1: 'first',
      activeName2: 'first',
      activeName3: 'first',
      activeName4: 'first',
      list: [
        { id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }
      ],
      options: [],
      value: [],
      list1: [],
      souvalue: '',
      loading: false,
      processing: [],
      processed: [],
      myprocess: [],
      processlist: [],
      deptMoneyList: [],
      selIndexDept: '',
      loadingMap: false,

    }
  },
  computed: {
    deptMoneySelect() {
      return this.deptMoneyList && this.deptMoneyList.length ? this.deptMoneyList[this.selIndexDept] : {}
    },
    deptMoneyDeptNames() {
      return this.deptMoneyList && this.deptMoneyList.length > 0 ? this.deptMoneyList.map(item => { return { label: item.deptName, id: item.deptId } }):[]
    },
    deptShouRuData() {
      return this.deptMoneySelect ? this.deptMoneySelect.incomeList : []
    },
    deptZhiChuData() {
      return this.deptMoneySelect ? this.deptMoneySelect.payList : []
    },
    deptShouMoneyList() {
      return this.deptMoneySelect ? this.deptMoneySelect.mincomeFundList : []
    },
    deptZhiMoneyList() {
      return this.deptMoneySelect ? this.deptMoneySelect.mpayFundList : []
    },
    deptShouRuData2Child() {
      if (this.deptShouRuData && this.deptShouRuData.length > 0) {
        // return this.deptShouRuData.reduce((acc, cur) => {
        //   if (!cur.children) {
        //     return acc
        //   }
        //   return acc.concat(cur.children.map(item => { return { oneLabel: cur.label, value: item.money, name: item.label, id: item.id, ...item } }))
        // }, [])
        // 排序金额大小 和排序字段

        return this.deptShouRuData.reduce((acc, cur) => {
          if (!cur.children) {
            return acc
          }
          const tmpAcc = acc.concat(cur.children.map((item, idx) => {
            // if (idx === 0) {
            return { oneLabel: cur.label, rolspan: cur.children.length, value: item.money, name: item.label, id: item.id, ...item }
            // }
            // return { value: item.money, name: item.label, id: item.id, ...item }
          }).sort((a, b) => {
            // if (a.value == b.value) {
            return a.sortby - b.sortby
            // } else {
            // return b.value - a.value
            // }
          }))
          // 增加合计
          tmpAcc.push({
            oneLabel: '小计',
            colspan: 2,
            money: cur.money,
            percent: cur.percent
          })
          return tmpAcc
        }, [])
      }
      return []
    },
    deptZhiChuData2Child() {
      if (this.deptZhiChuData && this.deptZhiChuData.length > 0) {
        return this.deptZhiChuData.reduce((acc, cur) => {
          if (!cur.children) {
            return acc
          }
          const tmpAcc = acc.concat(cur.children.map(item => { return { oneLabel: cur.label, rolspan: cur.children.length, value: item.money, name: item.label, id: item.id, ...item } }).sort((a, b) => {
            // if (a.value == b.value) {
            return a.sortby - b.sortby
            // } else {
            // return b.value - a.value
            // }
          }))
          tmpAcc.push({
            oneLabel: '小计',
            colspan: 2,
            money: cur.money,
            percent: cur.percent
          })
          return tmpAcc
        }, [])
      }
      return []
    },
    contactList() {
      if (this.tongnum == 0) {
        return this.showmyphonelist
      } else if (this.tongnum == 1) {
        return this.showmydepartment
      } else if (this.tongnum == 2) {
        return this.showmysubordinates
      }
      return []
    }
  },
  mounted() {
    // this.initChartData()
    this.loadDeptMoney()
    this.topage()
    getProcessList().then(res => {
      if (res !== undefined && res.resultCode === '0') {
        for (var a = 0; a < res.data.length; a++) {
          var item = res.data[a]
          for (var z = 0; z < item.sysProcessDetailList.length; z++) {
            var items = item.sysProcessDetailList[z]
            if (items.spare2 === '1') {
              items.parent = item.name
              this.processlist.push(items)
            }
          }
        }
      }
    })
    console.log(this.processlist)
  },
  methods: {
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 0) {
        this.temRowIdx = 0
      }
      if (columnIndex === 0) {
        if (row.rolspan && this.temRowIdx === rowIndex) {
          this.temRowIdx += row.rolspan
          this.temRowIdx += 1
          return {
            rowspan: row.rolspan,
            colspan: 1
          }
        } else {
          if (row.colspan) {
            return {
              rowspan: 1,
              colspan: row.colspan
            }
          }
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if (columnIndex === 1) {
        if (row.colspan) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    arrayZhiChuSpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log('row-515',row, column,'rowIndex:', rowIndex,'colidx:', columnIndex)
      if (rowIndex === 0 && columnIndex === 0) {
        this.temZhiRowIdx = 0
        // console.log('row-517,tmpidx',this.temZhiRowIdx)
      }
      if (columnIndex === 0) {
        // console.log('row-520,rolspan:', row.rolspan,'rowIndex:',rowIndex,'tmpidx:',this.temZhiRowIdx)
        if (row.rolspan && this.temZhiRowIdx === rowIndex) {
          this.temZhiRowIdx += row.rolspan
          this.temZhiRowIdx += 1
          // console.log('row-523 end tmpidx:',this.temZhiRowIdx,'rolspan:',row.rolspan)
          return {
            rowspan: row.rolspan,
            colspan: 1
          }
        } else {
          if (row.colspan) {
            return {
              rowspan: 1,
              colspan: row.colspan
            }
          }
          // console.log('row-529,r-0,c-0 end')
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if (columnIndex === 1) {
        if (row.colspan) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      // console.log('row-533 end')
    },
    moneyBfb(row, column, callValue) {
      return currency(callValue, { symbol: '', precision: 4 }).multiply(100).value + '%'
    },
    moneyTbFmt(row, column, callValue) {
      return this.moneyFmt(callValue)
    },
    moneyFmt(callValue) {
      if (!callValue) {
        return 0
      }
      // // 亿
      // if (callValue >= 100000000) {
      //   return currency(callValue, { symbol: '', precision: 2 }).divide(100000000).format() + '亿'
      // }
      // // 千万
      // if (callValue >= 10000000) {
      //   return currency(callValue, { symbol: '', precision: 2 }).divide(10000000).format() + '千万'
      // }
      // // 百万
      // if (callValue >= 1000000) {
      //   return currency(callValue, { symbol: '', precision: 2 }).divide(1000000).format() + '百万'
      // }
      // // 万
      // if (callValue >= 10000) {
      //   return currency(callValue, { symbol: '', precision: 2 }).divide(10000).format() + '万'
      // }
      return currency(callValue, { symbol: '', precision: 2 }).format()
    },
    handleDeptChange() {
      this.loadDeptMoneyChart()
      this.tableKey++
      // 表格重绘
      // this.$nextTick(() => {
      //   this.$refs.tabSr.doLayout()
      //   this.$refs.tabZc.doLayout()
      // })
    },
    handleDateChange() {
      this.loadDeptMoney()
    },
    showDetail(row, type) {
      console.log('showDetail', row, type)
      const deptId = this.deptMoneyDeptNames[this.selIndexDept].id
      const typeId = row.parentPathIds
      const time = this.mdayrange.join(',')
      const isChild = row.parentPathIds && row.parentPathIds.split(',').length > 1 ? row.parentPathIds : ''
      let activeName = ''
      if (type === 'rec') {
        activeName = 'paymentwater'
      }
      if (type === 'out') {
        activeName = 'receivewater'
      }
      if (row.wangLaiType == '1') {
        activeName = 'comeToTotalForm'
        const code = row.code || ''
        let isNeiWai = ''
        if (code.indexOf('wang_lai_') > -1) {
          isNeiWai = '1'
        }
        if (code.indexOf('wang_lai_wb') > -1) {
          isNeiWai = '2'
        }
        // 查询 type = rec 收入 往来公司
        // orDeptId
        if (type === 'rec') {
          // this.$router.push({ path: '/sumDetail/index', query: { company: deptId, activeName, type: typeId, time }})
          // this.$router.push('/sumDetail/index')
          // 打开新标签
          window.open(window.location.origin + '/sumDetail/index?company=' + deptId + '&activeName=' + activeName + '&type=' + typeId + '&time=' + time + '&isWangLaiType=1&child=' + isChild + '&isNeiWai=' + isNeiWai)
          // this.$router.push({ path: '/sumDetail/index', query: { company: deptId, activeName, type: typeId, time, isWangLaiType: 1 }})
          return
        }
        // type=out 支出 公司
        // deptId
        if (type === 'out') {
          // this.$router.push({ path: '/sumDetail/index', query: { company: deptId, activeName, type: typeId, time }})
          // this.$router.push('/sumDetail/index')
          // 打开新标签
          window.open(window.location.origin + '/sumDetail/index?company=' + deptId + '&activeName=' + activeName + '&type=' + typeId + '&time=' + time + '&isWangLaiType=2&child=' + isChild + '&isNeiWai=' + isNeiWai)
          // this.$router.push({ path: '/sumDetail/index', query: { company: deptId, activeName, type: typeId, time, isWangLaiType: 2 }})
          return
        }

        return
      }

      // this.$router.push({ path: '/sumDetail/index', query: { company: deptId, activeName, type: typeId, time }})
      // this.$router.push('/sumDetail/index')
      // 打开新标签
      window.open(window.location.origin + '/sumDetail/index?company=' + deptId + '&activeName=' + activeName + '&type=' + typeId + '&time=' + time + '&child=' + isChild)
      // this.$router.push({ path: '/sumDetail/index', query: { company: deptId, activeName, type: typeId, time }})
    },
    clkShouRu(data) {
      console.log('clkShouRu',data)
    },
    clkZhiChu(data) {
      console.log('clkZhiChu',data)
    },
    initChartData(loadOption = false) {
      if (!this.showMoney) {
        return
      }
      var chartDom = document.getElementById('main1');
      this.myChart = echarts.init(chartDom);
      this.myChart.on('click', (params)=> {
        this.clkShouRu(params.data)
      });
      this.myChart.showLoading();

      var chartDom2 = document.getElementById('main2');
      this.myChartZhichu = echarts.init(chartDom2);
      this.myChartZhichu.on('click', (params)=>{
        this.clkZhiChu(params.data)
      });
      this.myChartZhichu.showLoading();
      if (loadOption) {
        this.loadDeptMoneyChart()
      }
    },
    setShouRuChartData(option) {
      this.myChart.setOption(option);
      this.myChart.hideLoading();
      this.myChart.resize()

    },
    setZhiChuChartData(option) {
      this.myChartZhichu.setOption(option);
      this.myChartZhichu.hideLoading();
      this.myChartZhichu.resize()
    },
    loadDeptMoneyChart() {
      this.loadShouRuChart()
      this.loadChart()
    },
    loadShouRuChart() {
      // var chartDom = document.getElementById('main1');
      // var myChart = echarts.init(chartDom,'macarons');
      // var myChart = echarts.init(chartDom);
      const oneData = this.deptShouRuData.map(item => { return { value: item.money, name: item.label, id: item.id } })
      // 一级分类的子类是二级分类，所有二级分类的数据
      const twoData = this.deptShouRuData.reduce((acc, cur) => {
        if (!cur.children) {
          return acc
        }
        return acc.concat(cur.children.filter(im => im.money).map(item => { return { value: item.money, name: item.label, id: item.id, ...item } }))
      }, [])

      var option = {
        title: {
          text: '收入统计',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        // tooltip: {
        //   trigger: 'item',
        //   formatter: '{b}: {c} ({d}%)'
        // },
        tooltip: {
          trigger: 'item',

          // formatter: '{b}: {c} ({d}%)'
          // 金额格式化 c 为金额
          formatter: function(params) {
            return params.name + ': ' + currency(params.value, { separator: ',', precision: 2,symbol:'' }).format()
          }

        },
        series: [{
          name: '收入',
          type: 'pie',
          selectedMode: 'single',
          radius: [0, '30%'],
          label: {
            position: 'inner',
            overflow:'break',
            fontSize: 12
          },
          labelLine: {
            show: false
          },
          data: oneData
        },
        {
          name: '收入',
          type: 'pie',
          radius: ['45%', '60%'],
          labelLine: {
            length: 10
          },
          label: {
            // 名称要显示全
            // bleedMargin: 10,
            alignTo:'labelLine',
            overflow:'break'
          },
          data: twoData
        }]
      }
      this.setShouRuChartData(option)

    },
    loadChart() {
      const oneData = this.deptZhiChuData.map(item => { return { value: item.money, name: item.label, id: item.id } })
      // 一级分类的子类是二级分类，所有二级分类的数据
      const twoData = this.deptZhiChuData.reduce((acc, cur) => {
        if (!cur.children) {
          return acc
        }
        return acc.concat(cur.children.filter(im => im.money).map(item => { return { value: item.money, name: item.label, id: item.id, ...item } }))
      }, [])
      var options = {
        title: {
          text: '支出统计',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',

          // formatter: '{b}: {c} ({d}%)'
          // 金额格式化 c 为金额
          formatter: function(params) {
            return params.name + ': ' + currency(params.value, { separator: ',', precision: 2,symbol:'' }).format()
          }

        },
        series: [{
          name: '支出',
          type: 'pie',
          selectedMode: 'single',
          radius: [0, '30%'],
          label: {
            position: 'inner',
            overflow:'break',
            fontSize: 12
          },
          labelLine: {
            show: false
          },
          data: oneData
        },
        {
          name: '支出',
          type: 'pie',
          radius: ['45%', '60%'],
          labelLine: {
            length: 10
          },
          label: {
            alignTo:'labelLine',
            overflow:'break'
          },
          data: twoData
        }]
      }
      this.setZhiChuChartData(options)
    },
    loadDeptMoney() {
      let loadg
      if (!this.showMoney) {
        loadg = this.$loading({
          lock: true,
          text: '加载中',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.7)'
        })
      }
      this.deptMoneyList = []
      // this.selIndexDept = ''
      this.loadingMap = true
      if (this.showMoney) {
        this.myChartZhichu.showLoading();
        this.myChart.showLoading();
      }

      fundsAnalysisByDeptPlateAndDate(this.mdayrange[0],this.mdayrange[1]).then(res => {
        if (res !== undefined && res.resultCode === '0' && res.data) {
          this.deptMoneyList = res.data
          if (!this.showMoney) {
            this.showMoney = true
            this.activeName2 = 'deptMoney'
            this.selIndexDept = 0
            this.$nextTick(() => {
              this.initChartData(true)
            })
          } else {
            if (this.deptMoneyList && this.deptMoneyList.length > 0) {
              this.loadDeptMoneyChart()
            }
          }
        } else {
          this.showMoney = false
        }
      }).finally(() => {
        this.loadingMap = false
        if (this.showMoney) {
          this.myChartZhichu && this.myChartZhichu.hideLoading();
          this.myChart && this.myChart.hideLoading();
        }
        loadg && loadg.close()
      })
    },
    fmtNodeName(item){
      if(item.nodeName){
        // 部门负责人 审批人
        if(item.nodeName.indexOf('审批')!=0 && item.nodeName.indexOf('部门')!=0){
          return item.nodeName
        }
      }
        return  item.pname !== undefined ? item.pname:item.rname
    },
    fmtIng(txt, statusId) {
      if (!txt) {
        return ''
      }
      if (txt.indexOf('审批') > 0 || txt.indexOf('核验')>0) {
        return '中'
      }
      if (statusId && (Math.floor(statusId / 10) == 8 || Math.floor(statusId / 10) == 9)) {
        return '中'
      }
      return '审批中'
    },
    topage() {
      getHome().then(res => {
        if (res !== undefined && res.resultCode === '0') {
          console.log(res)
          this.processing = res.processing
          this.processed = res.processed
          this.myprocess = res.Myprocess
          this.myphonelist = res.phonelist
          this.showmyphonelist = res.phonelist
          for (var a = 0; a < this.myphonelist.length; a++) {
            var ress = this.myphonelist[a]
            if (ress.deptid === res.myphone.deptid) {
              this.mydepartment.push(ress)
              this.showmydepartment.push(ress)
            }
            if (ress.parentid === res.myphone.deptid) {
              this.mysubordinates.push(ress)
              this.showmysubordinates.push(ress)
            }
          }
          if (this.mydepartment.length === 0) {
            this.tongnum = 2
          }
          if (this.mysubordinates.length === 0) {
            this.tongnum = 1
          }
          console.log(res)
        }
      })
    },
    toMyprocess() {
      this.$router.push({ path: '/Myprocess/index' })
    },
    toMyprocessing() {
      if (this.activeName1 === 'first') {
        this.$router.push({ path: '/dispose/disposing' })
      } else {
        this.$router.push({ path: '/dispose/disposed' })
      }
      // 已办
      // this.$router.push({ path: '/dispose/disposing' })
    },
    toallprocess() {
      this.$router.push({ path: '/process/AllProcess' })
    },
    changetongnum(a) {
      this.tongnum = a
      this.remoteMethod()
    },

    handleClick(tab, event) {
      console.log(tab, event)
    },
    Goprocess(data) {
      console.log(data)
      if (data.spare1 === undefined || data.spare1 === '') {
        this.$message({
          showClose: true,
          type: 'warning',
          message: '该流程尚未维护'
        })
        // var z = JSON.stringify(this.test)
        // console.log(z)
        return
      }
      if (data.spare3) {
        var path = '/process/' + data.spare3
        this.$router.push({ path: path, query: { title: data.name, code: data.spare1, type: data.spare4, processJian: data.spare5 }})
      } else {
        this.$router.push({ path: '/process/SubmitProcess', query: { title: data.name, code: data.spare1, type: data.spare4, processJian: data.spare5 }})
      }
    },
    showshenpi(id, name) {
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    refresh() {
      this.topage()
    },
    handlerGlobalParams() {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
    // console.log(**********)
    },
    timeFmt(time, fmtstr) {
      if (time === undefined || time === null || time === '') {
        return ' '
      }
      return dayjs(time).format(fmtstr)
    },
    remoteMethod() {
      var query = this.souvalue
      this.options = []
      var list = {}
      if (query !== '') {
        if (this.tongnum === 0) {
          list = this.myphonelist
        } else if (this.tongnum === 1) {
          list = this.mydepartment
        } else if (this.tongnum === 2) {
          list = this.mysubordinates
        }
        this.loading = true
        setTimeout(() => {
          for (var a = 0; a < list.length; a++) {
            var item = list[a]
            if (item.name.indexOf(query) !== -1) {
              this.options.push(item)
            }
            if (item.phone.indexOf(query) !== -1) {
              this.options.push(item)
            }
          }
          if (this.tongnum === 0) {
            this.showmyphonelist = this.options
          } else if (this.tongnum === 1) {
            this.showmydepartment = this.options
          } else if (this.tongnum === 2) {
            this.showmysubordinates = this.options
          }
          console.log(this.options)
          this.loading = false
        }, 500)
      } else {
        this.showmyphonelist = this.myphonelist
        this.showmydepartment = this.mydepartment
        this.showmysubordinates = this.mysubordinates
      }
    }
  }
}
</script>

<style scoped>
.tactive{
  color: #00a0e9;
}
.conlt{
  grid-area:conlt;
}
.conrt{
  grid-area:conrt;
}
.conlb{
  grid-area:conlb;
}
.conrb{
  grid-area:conrb;
}
/* 样式转换添加动画 */
.aniGrid{
  transition: all 0.5s;
}
.gridNoMoneycls{
  width: 96%;
  /* height: calc(100vh - 166px); */
  height: 100%;
  display:grid;
  justify-content:stretch;
  grid-template-columns: 2fr 1fr;
  grid-template-rows:40vh 38vh;
  grid-template-areas:'conlt  conrt'
                      'conlb  conrb';
  margin-bottom: 3vh;
  gap:2%;
}
.gridcls{
  width: 96%;
  /* height: calc(100vh - 166px); */
  height: 100%;
  display:grid;
  justify-content:stretch;
  grid-template-columns: repeat(2,1fr);
  grid-template-rows:200px minmax(500px,100vh);
  grid-template-areas:'conlt conlb'
                      'conrt conrt';
  margin-bottom: 3vh;
  gap:2%;
}
.gridcls>.conrb{
  display: none;
}
.gridchartcls{
  width:100%;
  margin-top: 10px;
  display: grid;
  grid-template-columns: repeat(2,1fr);
  grid-template-rows: 250px auto;
  gap:10px;
  margin-bottom: 30px;
}
.longbox {
  overflow-y: auto;
  display: inline-block;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  /* height: 39.5vh; */
  margin-right: 3%;
  background-color: white;
  border-radius: 5px;
}

.shortbox {
  overflow-y: auto;
  display: inline-block;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  /* height: 39.5vh; */
  max-height: 800px;
  background-color: white;
  border-radius: 5px;
}
.fontclass{
  overflow-x:hidden;
  overflow-y:hidden;
  white-space:nowrap;
  text-overflow:ellipsis
}
</style>
