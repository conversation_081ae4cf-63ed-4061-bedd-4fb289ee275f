<template>
  <div>
    <div class="contract-wrap">
      <el-tabs v-model="tabIndex" tab-position="left" style="height: calc(100vh - 84px);margin-bottom: 0;">
        <el-tab-pane label="【1】选择船舶">
          <div style="margin-top: 20px;">
            <el-alert
              title="提示"
              type="success"
              :closable="false"
              style="margin-bottom: 10px;"
              :description="description">
            </el-alert>
            <el-table
              v-loading="shipData.loading"
              size="mini"
              :data="shipData.list"
              @row-click="onRowClick"
              style="width: 100%">
              <el-table-column label="#" width="55">
                <template slot-scope="scope">
                  <el-radio v-model="shipData.checkedId" :label="scope.row.shipName" @change="onChecked"><i></i></el-radio>
                </template>
              </el-table-column>
              <el-table-column
                label="船名"
                prop="shipName">
              </el-table-column>
<!--              <el-table-column-->
<!--                label="最近一次填写的合同信息"-->
<!--                prop="clausesFormData">-->
<!--                <template slot-scope="scope">-->
<!--                  <div>{{!scope.row.clausesFormData ? '暂无' : '有数据'}}</div>-->
<!--                </template>-->
<!--              </el-table-column>-->
            </el-table>
<!--            <el-pagination-->
<!--              layout="prev, pager, next"-->
<!--              hide-on-single-page-->
<!--              @current-change="onSearch"-->
<!--              :current-page.sync="shipData.page"-->
<!--              :page-size="shipData.size"-->
<!--              :total="shipData.total">-->
<!--            </el-pagination>-->
          </div>
        </el-tab-pane>
        <el-tab-pane label="【2】创建合同流程">
          <!-- 发起合同 -->
          <start-contract :is-dialog="false" :show.sync="contractDialogShow" @payload="setContractDialogPayload" @complete="contractComplete" @success-back-page="onSuccessBackPage"></start-contract>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import StartContractMixins from '@/mixins/start-contract.js'
import { saveShipContract, shipsContractData } from '@/api/business/contract.js'
export default {
  name: 'ContractProcess',
  mixins: [StartContractMixins],
  data() {
    return {
      tabIndex: 0,
      description: `你可以从下方列表中选择船舶，如果该船舶最近一次有填写过合同信息，那么选中该船舶后系统会把最近一次的合同信息自动填写上。如果下方列表没有找到要选择的船舶，您可以点击左方"【2】创建合同流程",直接进入合同的创建流程`,
      shipData: {
        checkedId: null,
        checkedItem: null,
        loading: false,
        list: [],
        // page: 1,
        // size: 10,
        // total: 0
      }
    }
  },
  watch: {
    tabIndex(newVal, oldVal) {
      console.log(typeof newVal)
      if (newVal === '1') {
        this.openContractDialog()
      } else {
        this.closeContractDialog()
      }
    }
  },
  mounted() {
    // this.openContractDialog()
    this.onSearch()
  },
  methods: {
    setContractDialogPayload(cb) {
      const checkedItem = this.shipData.checkedItem || {}
      const contractData = checkedItem.contractData || {}
      const clausesFormData = checkedItem.clausesFormData || {}
      // 设置发起合同时传递给合同iframe的payload
      cb(new Promise((resolve, reject) => {
        resolve({
          isPartyA: true,
          tplId: '6',
          partyAB: {
            partyAName: contractData.partyAName || '海南和盛海运有限公司',
            partyBName: contractData.partyBName || null
          },
          clausesForm: Object.assign({}, clausesFormData)
        })
      }))
    },
    onSuccessBackPage() {
      this.$router.back()
    },
    contractComplete(res) {
      console.log('合同创建完成')
      console.log(res)
      const clausesFormData = res.clausesForm || {}
      const shipName = clausesFormData.shipName
      const contractData_Full = res
      delete contractData_Full.clauses
      delete contractData_Full.clausesTops
      delete contractData_Full.clausesBottoms
      delete contractData_Full.clausesForm
      const contractData = contractData_Full
      saveShipContract({
        shipName: shipName,
        module: 'sanhuo',
        contractData: contractData,
        clausesFormData: clausesFormData
      }).then(res => {})
        .finally(() => {

        })
    },
    onSearch() {
      this.shipData.loading = true
      shipsContractData().then(res => {
        this.shipData.list = res
      }).finally(() => {
        this.shipData.loading = false
      })
    },
    onChecked(val) {
      this.shipData.checkedItem = this.shipData.list.find(item => item.shipName === val)
      this.tabIndex = '1'
    },
    onRowClick(row, column, event) {
      if (column.label === '#') {
        return
      }
      this.shipData.checkedItem = this.shipData.list.find(item => item.shipName === row.shipName)
      this.tabIndex = '1'
    }
  }
}
</script>

<style scoped>
.contract-wrap {
  width: 980px;
  margin: 0 auto;
}
>>> .el-tabs__nav-scroll {
  padding-top: 50px !important;
}
>>> .el-tabs__item {
  font-size: 16px;
}
</style>
