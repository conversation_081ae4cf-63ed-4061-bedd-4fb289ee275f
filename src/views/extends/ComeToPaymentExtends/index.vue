
<script>
import SubmitProcess from '@/views/process/submitprocess'
import {
  getAccountNumByAccountId, getAllDicBean,
  getBankNameByAccountName,
  getDicBeanbyDeptId
} from "../../../api/system/sysAccount";
import { isClearCascade } from '@/utils/utils'
export default {
  name: 'ComeToPaymentExtends',
  extends: SubmitProcess,
  watch: {
    'obj.paymentType': function(val,oldv) {
      if(this.obj.paymentType){
        isClearCascade(oldv).then(()=>{
          this.obj.paymentAccount = null
          this.obj.paymentBank = null
          this.obj.paymentAaccountNum = null
          this.obj.receiveAccount = null
          this.obj.receiveBank = null
          this.obj.receiveAccountNum = null
        }).catch(()=>{})
        var info = this.formSchema.find(item=>item.showT == '付款账户信息')
        for(var i=0;i<info.option.column.length;i++){
          var tmp = info.option.column[i]
          tmp.disabled = false
        }
        this.getDicBeanbyDeptId()
        this.getAllDicBean()
      }
    },
    'obj.company': function(val,oldv) {
      if(this.obj.paymentType){
        isClearCascade(oldv).then(()=>{
          this.obj.paymentAccount = null
          this.obj.paymentBank = null
          this.obj.paymentAaccountNum = null
          this.obj.Department = null
          if (this.$route.query.processJian == 'YHZHWLK') {
            this.obj.receiveAccount = null
            this.obj.receiveBank = null
            this.obj.receiveAccountNum = null
          }
        }).catch(()=>{})

        this.getDicBeanbyDeptId()
      }
    },
    'obj.paymentAccount': function(val,oldv) {
      if(val){
        isClearCascade(oldv).then(()=>{
           this.obj.paymentBank = null
          this.obj.paymentAaccountNum = null
        }).catch(()=>{})

        this.getBankNameByAccountName()
      }
    },
    'obj.paymentBank': function(val,oldv) {
      if(val){
        isClearCascade(oldv).then(()=>{
            this.obj.paymentAaccountNum = null
        }).catch(()=>{})

        this.getAccountNumByAccountId()
      }
    },
    'obj.receiveAccount': function(val,oldv) {
      if(val){
        isClearCascade(oldv).then(()=>{
          this.obj.receiveBank = null
        this.obj.receiveAccountNum = null
        }).catch(()=>{})

        this.getBankNameByAccountNameReceive()
      }
    },
    'obj.receiveBank': function(val,oldv) {
      if(val){
        isClearCascade(oldv).then(()=>{
            this.obj.receiveAccountNum = null
        }).catch(()=>{})

        this.getAccountNumByAccountIdReceive()
      }
    },
  },
  methods:{
    getDicBeanbyDeptId(){
      var type = 0
      if(this.obj.paymentType===3){
        type = 1
      }
      getDicBeanbyDeptId(this.obj.company,type).then(res=>{
        if (res && res.resultCode==='0'){
          var info = this.formSchema.find(item=>item.showT == '付款账户信息')
          var objItem =info.option.column.find(item=>item.label ==='我方账户')
          objItem.dicData = res.list
          if (this.$route.query.processJian == 'YHZHWLK') {
            let infoR = this.formSchema.find(item => item.showT == '收款账户信息')
            let objItemR = infoR.option.column.find(item => item.label === '对方账户')
            objItemR.dicData = res.list
          }
        }
      })
    },
    getBankNameByAccountName(){
      var type = 0
      if(this.obj.paymentType===3){
        type = 1
      }
      getBankNameByAccountName(this.obj.paymentAccount,type).then(res=>{
        if (res && res.resultCode==='0'){
          var info = this.formSchema.find(item=>item.showT == '付款账户信息')
          var objItem =info.option.column.find(item=>item.label ==='开户行')
          // 一个 默认选中
           if (res.list && res.list.length === 1) {
                  this.obj = Object.assign({}, this.obj, { paymentBank: res.list[0][objItem.props && objItem.props.value ? objItem.props.value : 'value'] })
              }
            this.$nextTick(() => {
              objItem.dicData = res.list
            })
          // objItem.dicData = res.list
        }
      })
    },
    getAccountNumByAccountId(){
      getAccountNumByAccountId(this.obj.paymentBank).then(res=>{
        if (res && res.resultCode==='0'){
          var info = this.formSchema.find(item=>item.showT == '付款账户信息')
          var objItem =info.option.column.find(item=>item.label ==='账户号')
            if (res.list && res.list.length === 1) {
                  this.obj = Object.assign({}, this.obj, { paymentAaccountNum: res.list[0][objItem.props && objItem.props.value ? objItem.props.value : 'value'] })
              }
            this.$nextTick(() => {
              objItem.dicData = res.list
            })
          // objItem.dicData = res.list
        }
      })
    },
    getAllDicBean() {
      if (this.$route.query.processJian == 'YHZHWLK') {
        return
      }
      var type = 0
      if(this.obj.paymentType===3){
        type = 1
      }
      getAllDicBean(type).then(res=>{
        if (res && res.resultCode==='0'){
          var info = this.formSchema.find(item=>item.showT == '收款账户信息')
          var objItem =info.option.column.find(item=>item.label ==='对方账户')
          objItem.dicData = res.list
        }
      })
    },
    getBankNameByAccountNameReceive(){
      var type = 0
      if(this.obj.paymentType===3){
        type = 1
      }
      getBankNameByAccountName(this.obj.receiveAccount,type).then(res=>{
        if (res && res.resultCode==='0'){
          var info = this.formSchema.find(item=>item.showT == '收款账户信息')
          var objItem =info.option.column.find(item=>item.label ==='开户行')
          // 一个 默认选中
           if (res.list && res.list.length === 1) {
                  this.obj = Object.assign({}, this.obj, { receiveBank: res.list[0][objItem.props && objItem.props.value ? objItem.props.value : 'value'] })
              }
            this.$nextTick(() => {
              objItem.dicData = res.list
            })
          // objItem.dicData = res.list
        }
      })
    },
    getAccountNumByAccountIdReceive(){
      getAccountNumByAccountId(this.obj.receiveBank).then(res=>{
        if (res && res.resultCode==='0'){
          var info = this.formSchema.find(item=>item.showT == '收款账户信息')
          var objItem =info.option.column.find(item=>item.label ==='账户号')
            if (res.list && res.list.length === 1) {
                  this.obj = Object.assign({}, this.obj, { receiveAccountNum: res.list[0][objItem.props && objItem.props.value ? objItem.props.value : 'value'] })
              }
            this.$nextTick(() => {
              objItem.dicData = res.list
            })
          // objItem.dicData = res.list
        }
      })
    },
  }
}
</script>

<style scoped>
#drawerBody {
  padding: 10px;
}

#timelineBody > * {
  text-align: left !important;
}

.wh40 {
  width: 40px;
  height: 42px;
  padding: 0 !important;
  margin: 0 !important;
}

.timelineContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  align-content: space-around;
}

.diy-avatar {
  display: inline-block;
  box-sizing: border-box;
  text-align: center;
  color: #fff;
  background: #C0C4CC;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 5px;
}
</style>

