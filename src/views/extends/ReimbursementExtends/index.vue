
<script>
import SubmitProcess from '@/views/process/submitprocess'
import { userBankNameListByName, userBankNumberListByName } from '@/api/system/user'
import { isClearCascade } from '@/utils/utils'
export default {
  name: 'ReimbursementExtends',
  extends: SubmitProcess,
  watch: {
    'groupcrubform.shui': function(val) {
      if (this.groupcrubform.invoiceType == 0) {
        if (val && this.groupcrubform.shuono) {
          var a = Number(val)
          var b = Number(this.groupcrubform.shuono)
          this.groupcrubform.sum = a + b
          var shui = b / a
          if (shui > 0.025 && shui < 0.035) {
            this.groupcrubform.fax = 1
          } else if (shui > 0.055 && shui < 0.065) {
            this.groupcrubform.fax = 0
          } else if (shui > 0.085 && shui < 0.095) {
            this.groupcrubform.fax = 2
          } else if (shui > 0.125 && shui < 0.135) {
            this.groupcrubform.fax = 3
          }
        }
      }
    },
    'groupcrubform.time': function(val) {
      if (!this.groupcrubform.oneday) {
        this.groupcrubform.oneday = '0'
      }
      if (val && this.groupcrubform.oneday) {
        var a = Number(this.groupcrubform.oneday)
        var b = Number(val)
        this.groupcrubform.sum = a * b
      }
    },
    'groupcrubform.oneday': function(val) {
      if (!this.groupcrubform.time) {
        this.groupcrubform.time = '0'
      }
      if (val && this.groupcrubform.time) {
        var a = Number(this.groupcrubform.time)
        var b = Number(val)
        this.groupcrubform.sum = a * b
      }
    },
    'groupcrubform.startdate': function(val) {
      if (val && this.groupcrubform.enddate && this.groupcrubform.purpose == '差旅补助') {
        const s = new Date(val)
        const e = new Date(this.groupcrubform.enddate)
        const is = s.getTime(s)
        const ie = e.getTime(e)
        this.groupcrubform.time = Math.floor((ie - is) / 86400000)
      }
    },
    'groupcrubform.enddate': function(val) {
      if (val && this.groupcrubform.startdate && this.groupcrubform.purpose == '差旅补助') {
        const s = new Date(this.groupcrubform.startdate)
        const e = new Date(val)
        const is = s.getTime(s)
        const ie = e.getTime(e)
        this.groupcrubform.time = Math.floor((ie - is) / 86400000)
      }
    },
    'groupcrubform.shuono': function(val) {
      if (this.groupcrubform.invoiceType == 0) {
        console.log(11111)
        if (val && this.groupcrubform.shui) {
          var a = Number(this.groupcrubform.shui)
          var b = Number(val)
          this.groupcrubform.sum = a + b
          var shui = b / a
          if (shui > 0.025 && shui < 0.035) {
            this.groupcrubform.fax = 1
          } else if (shui > 0.055 && shui < 0.065) {
            this.groupcrubform.fax = 0
          } else if (shui > 0.085 && shui < 0.095) {
            this.groupcrubform.fax = 2
          } else if (shui > 0.125 && shui < 0.135) {
            this.groupcrubform.fax = 3
          }
        }
      }
    },
    'obj.opositeUnit': function(val,oldv) {
      console.log('name watch', val,oldv);
      isClearCascade(oldv).then(()=>{
          this.obj.BankNum = ''
          this.obj.cardNum = ''
      }).catch(()=>{})
      if (val) {
        this.opositeUnitwatch(val)
      }
    },
    'obj.BankNum': function(val,oldv) {
      isClearCascade(oldv).then(()=>{
        this.obj.cardNum = ''
      }).catch(()=>{})

      if (val) {
        this.kaihuwatch(this.obj.opositeUnit, val)
      }
    }
  },
  methods: {
    opositeUnitwatch(name) {
      userBankNameListByName(name).then(res => {
        for (var a = 0; a < this.formSchema.length; a++) {
          var item = this.formSchema[a]
          if (item.showT === '收款人账户信息') {
            for (var b = 0; b < item.option.column.length; b++) {
              var items = item.option.column[b]
              if (items.label === '开户行') {
                // 默认选中第一个
                if (res.data && res.data.length === 1) {
                  this.obj = Object.assign({}, this.obj, { BankNum: res.data[0][items.params && items.params.valueKey ? items.params.valueKey : 'value'] })
                }
                this.$nextTick(() => {
                  items.dicData = res.data
                })
                // items.dicData = res.data
                break
              }
            }
            break
          }
        }
        // this.obj.BankNum='招商银行'
      })
    },
    kaihuwatch(name, bankname) {
      userBankNumberListByName(name, bankname).then(res => {
        for (var a = 0; a < this.formSchema.length; a++) {
          var item = this.formSchema[a]
          if (item.showT === '收款人账户信息') {
            for (var b = 0; b < item.option.column.length; b++) {
              var items = item.option.column[b]
              if (items.label === '卡号') {
                if (res.data && res.data.length === 1) {
                  this.obj = Object.assign({}, this.obj, { cardNum: res.data[0][items.params && items.params.valueKey ? items.params.valueKey : 'value'] })
                }
                this.$nextTick(() => {
                  items.dicData = res.data
                })
                break
              }
            }
            break
          }
        }
        // console.log(this.formSchema)
      })
    }
  }
}
</script>

<style scoped>
#drawerBody {
  padding: 10px;
}

#timelineBody > * {
  text-align: left !important;
}

.wh40 {
  width: 40px;
  height: 42px;
  padding: 0 !important;
  margin: 0 !important;
}

.timelineContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  align-content: space-around;
}

.diy-avatar {
  display: inline-block;
  box-sizing: border-box;
  text-align: center;
  color: #fff;
  background: #C0C4CC;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 5px;
}
</style>

