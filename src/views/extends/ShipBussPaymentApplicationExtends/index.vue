<script>
import SubmitProcess from '@/views/process/submitprocess'
import { getAllBank, getAllBankNum } from '@/api/business/sysExternalAccount'
import { getContract } from '@/api/business/sysContractapi'
import { isClearCascade } from '@/utils/utils'
export default {

  name: 'ShipBussPaymentApplicationExtends',
  extends: SubmitProcess,
  watch: {
    'obj.contractid': function(val) {
      console.log(val)
      for (var a = 0; a < this.contractList.length; a++) {
        var item = this.contractList[a]
        if (item.value === val) {
          this.contractimageList = JSON.parse(item.id)
        }
      }
    },
    'contractHistroy': function(val,oldv){
      isClearCascade(oldv).then(()=>{
      this.contractimageList = []
      this.obj.contractid = undefined
      this.obj.contractname = undefined
      }).catch(()=>{})
      for (var a = 0; a < this.formSchema.length; a++) {
        var item = this.formSchema[a]
        if (item.showT === '合同信息') {
          for (var b = 0; b < item.option.column.length; b++) {
            var items = item.option.column[b]
            if (items.prop === 'contractname') {
              items.display = !items.display
            }
            if (items.prop === 'contractid') {
              items.display = !items.display
            }
          }
        }
      }
    },
    'obj.opositeUnit': function(val,oldv) {
      isClearCascade(oldv).then(()=>{
        this.obj.kaihu = ''
        this.obj.zhanghunum = ''
      }).catch(()=>{})

      this.selectContract()
      if (val) {
        this.opositeUnitwatch(val)
      }
    },
    'obj.company': function() {
      this.selectContract()
    },
    'obj.kaihu': function(val,oldv) {
      isClearCascade(oldv).then(()=>{
        this.obj.zhanghunum = ''
      }).catch(()=>{})

      if (val) {
        this.kaihuwatch(this.obj.opositeUnit, val)
      }
    }
  },
  mounted() {
    getContract('', 0, '', '').then(res => {
      this.contractList = res.data
    })
  },
  methods: {
    selectContract() {
      // getContract(this.obj.contract,0,this.obj.company,this.obj.opositeUnit).then(res =>{
      //
      // })
      for (var a = 0; a < this.formSchema.length; a++) {
        var item = this.formSchema[a]
        if (item.showT === '合同信息') {
          for (var b = 0; b < item.option.column.length; b++) {
            var items = item.option.column[b]
            if (items.label === '合同名称') {
              if (this.obj.company && this.obj.opositeUnit) {
                items.dicUrl = 'https://api.erp.successhetai.com/api/sysContract/getContract?receiveid=' + this.obj.opositeUnit + '&paymentid=' + this.obj.company + '&type=0&name={{key}}'
              } else if (this.obj.company) {
                items.dicUrl = 'https://api.erp.successhetai.com/api/sysContract/getContract?paymentid=' + this.obj.company + '&type=0&name={{key}}'
              } else if (this.obj.opositeUnit) {
                items.dicUrl = 'https://api.erp.successhetai.com/api/sysContract/getContract?receiveid=' + this.obj.opositeUnit + '&type=0&name={{key}}'
              } else {
                items.dicUrl = 'https://api.erp.successhetai.com/api/sysContract/getContract?type=0&name={{key}}'
              }
            }
          }
        }
      }
    },
    opositeUnitwatch(id) {
      getAllBank(id).then(res => {
        for (var a = 0; a < this.formSchema.length; a++) {
          var item = this.formSchema[a]
          if (item.showT === '对方单位信息') {
            for (var b = 0; b < item.option.column.length; b++) {
              var items = item.option.column[b]
              if (items.label === '开户行') {
                // 默认第一个
                if (res.data && res.data.length === 1) {
                  this.obj = Object.assign({}, this.obj, { kaihu: res.data[0][items.props && items.props.value ? items.props.value : 'value'] })
                }
                this.$nextTick(() => {
                  items.dicData = res.data
                })
                break
              }
            }
            break
          }
        }
        // console.log(this.formSchema)
      })
    },
    kaihuwatch(id, bankid) {
      getAllBankNum(id, bankid).then(res => {
        for (var a = 0; a < this.formSchema.length; a++) {
          var item = this.formSchema[a]
          if (item.showT === '对方单位信息') {
            for (var b = 0; b < item.option.column.length; b++) {
              var items = item.option.column[b]
              if (items.label === '账户号') {
                if (res.data && res.data.length === 1) {
                  this.obj = Object.assign({}, this.obj, { zhanghunum: res.data[0][items.props && items.props.value ? items.props.value : 'value'] })
                }
                this.$nextTick(() => {
                  items.dicData = res.data
                })
                break
              }
            }
            break
          }
        }
        console.log(this.formSchema)
      })
    }
  }
}
</script>

<style scoped>
#drawerBody {
  padding: 10px;
}

#timelineBody > * {
  text-align: left !important;
}

.wh40 {
  width: 40px;
  height: 42px;
  padding: 0 !important;
  margin: 0 !important;
}

.timelineContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  align-content: space-around;
}

.diy-avatar {
  display: inline-block;
  box-sizing: border-box;
  text-align: center;
  color: #fff;
  background: #C0C4CC;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 5px;
}
</style>
