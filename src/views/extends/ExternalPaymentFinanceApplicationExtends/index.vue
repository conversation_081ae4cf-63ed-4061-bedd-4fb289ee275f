<script>
import SubmitProcess from '@/views/process/submitprocess'
import { getAllBank, getAllBankNum } from '@/api/business/sysExternalAccount'
import { isClearCascade } from '@/utils/utils'
export default {
  name: 'ExternalPaymentFinanceApplicationExtends',
  extends: SubmitProcess,
  watch: {
    'obj.opositeUnit': function(val, oldv) {
      isClearCascade(oldv).then(() => {
        this.obj.kaihu = ''
        this.obj.zhanghunum = ''
      }).catch(() => {})

      if (val) {
        this.opositeUnitwatch(val)
      }
    },
    'obj.kaihu': function(val, oldv) {
      isClearCascade(oldv).then(() => {
        this.obj.zhanghunum = ''
      }).catch(() => {})
      if (val) {
        this.kaihuwatch(this.obj.opositeUnit, val)
      }
    }
  },
  methods: {
    opositeUnitwatch(id) {
      getAllBank(id).then(res => {
        for (var a = 0; a < this.formSchema.length; a++) {
          var item = this.formSchema[a]
          if (item.showT === '对方单位信息') {
            for (var b = 0; b < item.option.column.length; b++) {
              var items = item.option.column[b]
              if (items.label === '开户行') {
                 if (res.data && res.data.length === 1) {
                  this.obj = Object.assign({}, this.obj, { kaihu: res.data[0][items.props && items.props.value ? items.props.value : 'value'] })
                }
                this.$nextTick(() => {
                  items.dicData = res.data
                })
                // items.dicData = res.data
                break
              }
            }
            break
          }
        }
        console.log(this.formSchema)
      })
    },
    kaihuwatch(id, bankid) {
      getAllBankNum(id, bankid).then(res => {
        for (var a = 0; a < this.formSchema.length; a++) {
          var item = this.formSchema[a]
          if (item.showT === '对方单位信息') {
            for (var b = 0; b < item.option.column.length; b++) {
              var items = item.option.column[b]
              if (items.label === '账户号') {
                // items.dicData = res.data
                 if (res.data && res.data.length === 1) {
                  this.obj = Object.assign({}, this.obj, { zhanghunum: res.data[0][items.props && items.props.value ? items.props.value : 'value'] })
                }
                this.$nextTick(() => {
                  items.dicData = res.data
                })
                break
              }
            }
            break
          }
        }
        console.log(this.formSchema)
      })
    }
  }
}
</script>

<style scoped>
#drawerBody {
  padding: 10px;
}

#timelineBody > * {
  text-align: left !important;
}

.wh40 {
  width: 40px;
  height: 42px;
  padding: 0 !important;
  margin: 0 !important;
}

.timelineContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  align-content: space-around;
}

.diy-avatar {
  display: inline-block;
  box-sizing: border-box;
  text-align: center;
  color: #fff;
  background: #C0C4CC;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 5px;
}
</style>

