<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      备用金账单汇总
    </div>
    <el-divider />
    <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
      <el-form-item label="账户:">
        <el-select v-model="queryForm.companyid" clearable placeholder="请选择" @change="getAccount()">
          <el-option
            v-for="item in companyList"
            :key="item.id"
            :label="item.companyName"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="queryForm.status" clearable placeholder="请选择状态" @change="getAccount()">
          <el-option
            v-for="item in billStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期:">
        <el-date-picker
          v-model="queryForm.shipTime"
          clearable
          type="monthrange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="handleDatePickOptions()"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <div id="yewuzongbiao">
      <vxe-table
        ref="xTable"
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '业务总台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column field="companyName" :formatter="publicFmtVxe" title="账户" align="center"  />
        <vxe-table-column field="balance" :formatter="publicFmtnumber" title="账单金额" align="center"  />
        <!-- <vxe-table-column field="amount" :formatter="publicFmtnumber" title="余额" align="center" width="100" /> -->
        <vxe-table-column field="personName" :formatter="publicFmtVxe" title="申请人" align="center" width="100" />
        <vxe-table-column field="personDate" :formatter="publicFmtVxe" title="申请时间" align="center"  />
        <vxe-table-column field="incomeBus" :formatter="publicFmtnumber" title="收入" align="center"  />
        <vxe-table-column field="param1" :formatter="publicFmtnumber" title="支出" align="center"  />
        <vxe-table-column field="status" :formatter="publicFmtStatus" title="状态" align="center"  />
        <vxe-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="todetail(scope.row)">查看</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />
    <ProgressComponent ref="processDrawerqingjia" componentsWidth="50%" @refresh="refresh" @onCallback="handlerGlobalParams" />
  </section>
</template>

<script>
import { fundList,monthBillListByFundIdAndMonth, monthBillIdToProcessId} from '@/api/system/imprestFund'
// import store from '@/store'
import 'vxe-table/lib/style.css'
import dayjs from 'dayjs'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
import currency from 'currency.js'
import ProgressComponent from '@/components/workflow/process'

export default {
  name: 'BillList',
  components: { ProgressComponent },
  data() {
    function getFullDate(targetDate) {
      var D, y, m, d
      if (targetDate) {
        D = new Date(targetDate)
        y = D.getFullYear()
        m = D.getMonth() + 1
        d = D.getDate()
      } else {
        y = fullYear
        m = month
        d = date
      }
      m = m > 9 ? m : '0' + m
      d = d > 9 ? d : '0' + d
      return y + '-' + m + '-' + d
    }
    var nowDate = new Date()
    var lastDate = new Date()
    lastDate.setMonth(nowDate.getMonth() + 1)
    // var fullYear = nowDate.getFullYear()
    // var month = nowDate.getMonth() + 1 // getMonth 方法返回 0-11，代表1-12月
    // var endOfMonth = new Date(fullYear, month, 1).getDate() // 获取本月最后一天
    var endDate = getFullDate(lastDate.setDate(1))// 当月最后一天
    var startDate = getFullDate(nowDate.setDate(1))// 当月第一天

    return {
      billStatusList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '待审批',
          value: '0'
        },
        {
          label: '审批中',
          value: '1'
        },
        {
          label: '审批成功',
          value: '3'
        },
        {
          label: '审批失败',
          value: '2'
        }
      ],
      data: [],
      data1: [],
      form: {},
      banlist: [],
      toolbarButtons: [],
      tableLoading: false,
      companyList: [],
      accountList: [],
      total: 0,
      queryForm: {
        pageSize: 10,
        pageNum: 1,
        status: '3',
        accid: undefined,
        companyid: null,
        shipTime: [startDate, endDate]
      },
      tableData: [],
      pickEndDate: lastDate
    }
  },
  activated(){
     if(this.$route.query.deptId){
        this.queryForm.companyid = this.$route.query.deptId+''
      }else{
        this.queryForm.companyid = null
      }
      this.getAccount();
      this.onQuery();
  },
  created() {
    this.loadInitData()
      // if(this.$route.query.deptId){
      //   this.queryForm.companyid = this.$route.query.deptId+''
      // }
      //  getDicts().then(res =>{
      //   for(var a = 0; a < res.length;a++){
      //     var item = res[a]
      //     if (item.name === 'plate'){
      //       this.banlist.push(item)
      //     }
      //   }
      //   console.log(this.banlist)
      // })
      // this.getAccount();
      this.onQuery();
  },
  methods: {
    refresh() {
      console.log('刷新')
    },
    handlerGlobalParams(){
      console.log('回调')
    },
    loadInitData() {
      fundList().then(res => {
        if (res !== undefined && res.resultCode === '0') {
          this.companyList = res.list
        }
     })
    },
    handleDatePickOptions() {
      return {
        disabledDate: time => {
          // console.log(time)
          return time.getTime() > this.pickEndDate.getTime()
        },
        onPick({ maxDate, minDate }) {
          if (maxDate && maxDate.getTime() == minDate.getTime()) {
            // minDate = new Date(minDate.getTime())
            if (minDate.getTime() >= Date.now()) {
              minDate = new Date(minDate.setMonth(maxDate.getMonth() - 1))
            } else {
              maxDate = new Date(maxDate.setMonth(minDate.getMonth() + 1))
            }
          }
          console.log(maxDate, minDate)
        }
      }
    },
    todetail(item) {
      // this.$router.push({ path: '/imprestf/bill-detail', query: { id: item.id, startDate: this.queryForm.startTime, endDate: this.queryForm.endTime }})
      monthBillIdToProcessId(item.id).then(res => {
        // res
        if (res.processId) {
          this.showshenpi(res.processId,"备用金账单")
        } else {
          this.$message({
            message: '该账单没有审批流程',
            type: 'warning'
          })
        }
      }).catch(() => {
        this.$message({
            message: '网络异常，请稍后再试',
            type: 'warning'
          })
      })
    },
    showshenpi(id, name) {
      this.$refs.processDrawerqingjia.refrshDef()
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    getAccount() {
      console.log(this.queryForm)
      // if (!this.queryForm.companyid){
      //   this.queryForm.accid = undefined
      // }
      this.topage(1)
    },
    eventPage(e) {
      this.topage(e)
    },
    onQuery() {
      this.topage(1)
    },
    fmtDayByMonth(time) {
      return dayjs(time).format('YYYY-MM')
    },
    topage(pageNo) {
      this.tableLoading = true
      this.queryForm.pageNum = pageNo

      if (
        this.queryForm.shipTime != undefined &&
        this.queryForm.shipTime != null
      ) {
        this.queryForm.startTime = this.queryForm.shipTime[0]
        this.queryForm.endTime = this.queryForm.shipTime[1]
      } else {
        var [startDate, endDate] = this.getMonthDay()
        this.queryForm.startTime = startDate
        this.queryForm.endTime = endDate
      }

      const data = {
        fundId: this.queryForm.companyid,
        status: this.queryForm.status,
        startMonth: this.fmtDayByMonth(this.queryForm.startTime),
        endMonth: this.fmtDayByMonth(this.queryForm.endTime)
      }
      this.tableData = []
      monthBillListByFundIdAndMonth(
        data
      ).then(res => {
        this.tableLoading = false
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.list
        }
        console.log(this.tableData)
      })
    },
    getMonthDay() {
      var nowDate = new Date()
      var fullYear = nowDate.getFullYear()
      var month = nowDate.getMonth() + 1 // getMonth 方法返回 0-11，代表1-12月
      var endOfMonth = new Date(fullYear, month, 1) // 获取本月最后一天
      return [this.getFullDate(nowDate.setDate(1)), this.getFullDate(endOfMonth)]
    },
    getFullDate(targetDate) {
      var D, y, m, d
      if (targetDate) {
        D = new Date(targetDate)
        y = D.getFullYear()
        m = D.getMonth() + 1
        d = D.getDate()
      } else {
        y = fullYear
        m = month
        d = date
      }
      m = m > 9 ? m : '0' + m
      d = d > 9 ? d : '0' + d
      return y + '-' + m + '-' + d
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtStatus({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        // v = cellValue
        v = this.billStatusList.find(item => item.value == cellValue).label
      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        // v =  cellValue.toFixed(2)
        v = currency(cellValue, { symbol: '', precision: 2 }).format()
      }
      return v
    },
    handleRowSave(row, done, loading) {
      this.data1.splice(0, 0, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      this.data1.splice(index, 1, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data1.splice(index, 1)
      })
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
</style>
