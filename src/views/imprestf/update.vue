<template>
  <section class="content">
    <div v-show="selCompanyIndex==-1" style="text-align:center;width:100%;margin-top:50px;">暂无账户</div>
    <div v-show="selCompanyIndex!=-1" style="flex-grow:5;">
      <!-- mg30 minh30 -->
      <div class="bodd minh30 flexd flexdire  justaround " >
        <div class="flexbet fcenter">
             <div >备用金账户：
              <el-select v-model="selCompanyIndex" @change="changeCompany" placeholder="请选择公司">
                <el-option v-for="(item,index) in fundList"
                  :key="index"
                  :label="item.companyName"
                  :value="index">
                </el-option>
              </el-select>
            </div>
            <!-- <div>余额：{{selComDetail.balance|moneyFmt}}</div> -->
            <div class="flexbet fcenter">
              <el-button type="primary" @click="submitChangeAccountRecord">提交改帐</el-button>
              <!-- <br/>并上报本期账单 -->
             <!-- <el-button type="primary" plain @click="addRecord(undefined,undefined,true)">上报本期账单</el-button> -->
            </div>
        </div>
        <!-- <div class="flexbet fcenter font13">
          <div class="flexbet fcenter">
           本期账单支出：{{applyDetail.totalExpenditure}} &nbsp;&nbsp; -->
           <!-- <el-button @click="addRecord"  type="primary" plain >录入支出</el-button> -->
          <!-- </div>
          <div class="flexbet fcenter">
           本期账单收入：{{applyDetail.incomeBus}} &nbsp;&nbsp; -->
           <!-- <el-button plain @click="addRecord(undefined,'income')" >录入收入</el-button> -->
          <!-- </div>
        </div> -->

      </div>
      <div class="mg30">
        <div class="flexbet fcenter">
          <div>
            付款日期：
              <el-date-picker
            v-model="queryRecordFrom.dates"
            type="monthrange"
            @change="queryPageRecord"
            value-format="yyyy-MM"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份">
          </el-date-picker>
          </div>
            <div>
              <el-tag class="curpoi" :type="queryRecordFrom.isIncomeStatus==1?'success':'info'" @click="selStatus(1)">支出</el-tag>
              <el-tag class="curpoi" :type="queryRecordFrom.isIncomeStatus==2?'success':'info'" @click="selStatus(2)">收入</el-tag>
            </div>

          <!-- <div style="width:230px;text-align:right;">
            <div v-show="queryRecordFrom.isIncomeStatus==1">
            <el-tag class="curpoi"  :type="queryRecordFrom.isPriceIncome==1?'success':'info'"  @click="selPriInc(1)">日常消费</el-tag>
            <el-tag class="curpoi" :type="queryRecordFrom.isPriceIncome==2?'success':'info'"  @click="selPriInc(2)">招待费</el-tag>
             <el-tag class="curpoi" :type="queryRecordFrom.isPriceIncome==3?'success':'info'"  @click="selPriInc(3)">业务费用</el-tag>
             </div>
          </div> -->
          <div class="flexbet fcenter">
            <el-button type="success" plain @click="queryPageRecord">查询</el-button>
            <!-- <el-button type="primary" @click="addRecord(undefined,undefined,true)">上报账单</el-button> -->
          </div>


        </div>
        <div class="mt10">
          <table border="1" class="tablecls tabbord">
            <tr>
              <th>船期</th>
              <th style="width:120px;">付款日期</th>
              <th>用途说明</th>
              <th v-show="queryRecordFrom.isIncomeStatus==null || queryRecordFrom.isIncomeStatus == 2 ">收入</th>
              <th v-show="queryRecordFrom.isIncomeStatus==null || queryRecordFrom.isIncomeStatus == 1 ">支出</th>

              <!-- <th>金额</th> -->
              <th>发票</th>
              <th>备注</th>
              <th>操作</th>
            </tr>

            <tr v-for="(item,index) in pageRecordList" :key="index">
              <td>{{shipLineFmt( item.departParam||item.param3) }}

              </td>
              <td>
                <div :class="{'deltext':recordOldList[item.id] && !isEq(item.consumptionDate,recordOldList[item.id].consumptionDate)}">
                  {{item.consumptionDate}}{{item.param2 && item.param2!=item.consumptionDate ? ' ~ '+item.param2 :''}}
                </div>
                <span class="redtext" v-if="recordOldList[item.id] && !isEq(item.consumptionDate,recordOldList[item.id].consumptionDate)">{{recordOldList[item.id].consumptionDate}}</span>
              </td>
              <td>
                <div :class="{'deltext':recordOldList[item.id] && !isEq(item.param1,recordOldList[item.id].param1)}">{{item.param1}}</div>
                <span class="redtext" v-if="recordOldList[item.id] && !isEq(item.param1,recordOldList[item.id].param1)">{{  recordOldList[item.id].param1}}</span>
              </td>
              <td >
                <div :class="{'deltext':recordOldList[item.id] && !isEq(item.balance,recordOldList[item.id].balance)}">{{ item.balance|moneyFmt}}</div>
                <span class="redtext" v-if="recordOldList[item.id] && !isEq(item.balance,recordOldList[item.id].balance)">{{ recordOldList[item.id].balance | moneyFmtNull}}</span>
              </td>
              <!-- <td>xxx</td> -->
              <td>
                <div :class="{'deltext':recordOldList[item.id] && !isEq(item.isBill,recordOldList[item.id].isBill)}">{{ fmtBill(item.isBill)}}</div>
                <span class="redtext" v-if="recordOldList[item.id] && !isEq(item.isBill,recordOldList[item.id].isBill) ">{{ fmtBill(recordOldList[item.id].isBill)}}</span>
              </td>
              <td>
                <div :class="{'deltext':recordOldList[item.id] && !isEq(item.remarks,recordOldList[item.id].remarks)}">{{item.remarks}}</div>
                <span class="redtext" v-if="recordOldList[item.id] && !isEq(item.remarks,recordOldList[item.id].remarks)">{{ recordOldList[item.id].remarks}}</span>
              </td>
              <td>
                <div v-if="item.status==1 || item.changeStatus==1">
                  审批中
                </div>
                <div  v-else-if="item.status == 3 || (item.tijiaocaiwu && item.tijiaocaiwu==1)">
                  <!-- 3 审核成功 修改要走改帐 -->
                  <el-tooltip :disabled="!upBtnDisByShipLine(item.shipLineId,item.type)" class="item" effect="dark" content="改帐请选同一船期,每次改账，要么修改航运部相关、要么修改运营部相关" placement="top-end">
                    <div>
                     <el-button :disabled="upBtnDisByShipLine(item.shipLineId,item.type)" @click="updateRecordAndProcess(recordOldList[item.id] || item,item)">改帐</el-button>
                    </div>
                  </el-tooltip>
                  <el-button v-show="recordOldList[item.id]" @click="cancelOldDataById(item.id)">取消</el-button>
                </div>
                <div v-else>
                  <div v-if="item.costIsComplete && item.costIsComplete==1">
                    <!-- 1 提交中 不能修改 -->
                    <el-tooltip class="item" effect="dark" content="如需修改 请联系单证员" placement="top-end"></el-tooltip>
                     <span>费用已确认</span>
                    </el-tooltip>
                  </div>
                  <!-- 0 创建 2  失败 可以修改 -->
                 <el-button v-else @click="updateRecord(item)">修改</el-button>
                </div>



                <!-- 状态:{{item.tijiaocaiwu}}, -->
                <!-- quren:{{item.costIsComplete}}, -->
                <!-- shipLineId:{{item.shipLineId}} -->
                  <!-- <el-popconfirm
                   v-show="item.status==0 || item.status==2"
                   title="确定要删除吗？"
                   icon-color="red"
                   @confirm="removePageRecordList(item.id)"
                  >
                  <el-button type="danger" plain slot="reference">删除</el-button>
                </el-popconfirm> -->
              </td>
            </tr>
          </table>
          <div style="text-align:center;padding:10px;width:100%;" v-show="!pageRecordList || pageRecordList.length==0">
          <el-empty description="暂无数据"></el-empty>
          </div>
          <el-pagination
            v-show="pageRecordList && pageRecordList.length>0"
            layout="prev, pager, next"
            :total="pageRecordTotal"
            background
            :page-size="pageRecordSize"
            :current-page="pageRecordNum"
            style="text-align: right;padding: 10px 0px;background-color: #fff"
            @current-change="eventPage"
            >
          </el-pagination>
        </div>
      </div>
    </div>
    <div v-show="selCompanyIndex!=-1"  style="flex-grow:1;" class="mtrb30">
    <el-button icon="el-icon-d-arrow-left" @click="drawerRight=true">审批列表</el-button>


    </div>
    <el-dialog
      title="修改"
      :visible.sync="updateProcessDialogVisible"
      :close-on-click-modal="false"
      width="350px">
      <el-form ref="updateProcessInfo" :model="updateProcessInfo" label-width="80px">
        <el-form-item label="付款日期">
          <el-date-picker
                  v-model="updateProcessInfo.consumptionDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择付款时间"
                >
                </el-date-picker>
        </el-form-item>
        <el-form-item label="用途">
          <el-input v-model="updateProcessInfo.param1"></el-input>
        </el-form-item>
        <el-form-item label="金额">
          <el-input v-model="updateProcessInfo.balance" oninput="value=value.replace(/[^\d\.]/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')" placeholder="请输入金额"></el-input>
        </el-form-item>
        <el-form-item label="发票">
          <el-select v-model="updateProcessInfo.isBill" placeholder="请选择发票">
              <el-option
                v-for="sitem in billList"
                :key="sitem.value"
                :label="sitem.label"
                :value="sitem.value">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="updateProcessInfo.remarks" placeholder="请输入备注"></el-input>
        </el-form-item>
        <div style="text-align:right;">
          <el-button @click="updateProcessDialogVisible = false">取消</el-button>
        <el-button @click="submitUpdateRecordProcess" type="primary">确认</el-button>
        </div>


      </el-form>
    </el-dialog>
    <el-dialog
      title="修改"
      :visible.sync="updateDialogVisible"
      :close-on-click-modal="false"
      width="350px">
      <el-form ref="updateInfo" :model="updateInfo" label-width="80px">
        <el-form-item label="付款日期">
          <el-date-picker
                  v-model="updateInfo.consumptionDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择付款时间"
                >
                </el-date-picker>
        </el-form-item>
        <el-form-item label="用途">
          <el-input v-model="updateInfo.param1"></el-input>
        </el-form-item>
        <el-form-item label="金额">
          <el-input v-model="updateInfo.balance" oninput="value=value.replace(/[^\d\.]/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')" placeholder="请输入金额"></el-input>
        </el-form-item>
        <el-form-item label="发票">
          <el-select v-model="updateInfo.isBill" placeholder="请选择发票">
              <el-option
                v-for="sitem in billList"
                :key="sitem.value"
                :label="sitem.label"
                :value="sitem.value">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="updateInfo.remarks" placeholder="请输入备注"></el-input>
        </el-form-item>
        <div style="text-align:right;">
          <el-button @click="updateDialogVisible = false">取消</el-button>
        <el-button @click="submitUpdateRecord" type="primary">确认</el-button>
        </div>


      </el-form>
    </el-dialog>


      <el-drawer
      :title="selComDetail.companyName"
      :visible.sync="drawerRight"
      direction="rtl"
      size="50%"
      :before-close="handleRightClose">
      <section style="padding:10px;">
        <div style="text-align:center;margin-bottom:5px;">审批列表<el-button icon="el-icon-refresh" @click="loadProcessList" size="mini" circle></el-button>
          </div>
          <table border="1" class="tablecls">
            <tr>
              <th >申请日期</th>
              <th>类型</th>
              <th>金额</th>
              <th>状态</th>
            </tr>
            <tr v-if="!pageProcessList || pageProcessList.length==0">
              <td colspan="4">
                <el-empty description="暂无信息"></el-empty>
              </td>
            </tr>
            <tr v-for="(item,index) in pageProcessList" :key="index">
              <td>{{item.sponsorTime}}</td>
              <td class="colclk" @click="showshenpi(item.id,item.flowName)">{{item.flowName}}</td>
              <td>{{item.globalParam3|moneyFmt}}</td>
              <td>
                  <div
                    v-if="item.status=== '1' "
                    class="fontclass">审批中</div>
                  <div
                    v-if="item.status=== '2' "
                    class="fontclass">已完成</div>
                  <div
                    v-if="item.status==='3' "
                    class="fontclass">已驳回</div>
                    <div
                    v-if="item.status==='4' "
                    class="fontclass">已撤回</div>
              </td>
            </tr>
          </table>
          <el-pagination
                v-show="pageProcessList && pageProcessList.length>0"
                layout="prev, pager, next"
                :total="pageProcessTotal"
                background
                :page-size="pageProcessSize"
                :current-page="pageProcessNum"
                style="text-align: right;padding: 10px 0px;background-color: #fff"
                @current-change="eventPageProcess"
                >
              </el-pagination>
      </section>
    </el-drawer>

      <WorkFlowComponent ref="processDrawerIndex" componentsWidth="50%" @onCallback="onCallback" @onBeforeSubmit="onBeforeSubmit"></WorkFlowComponent>
      <ProgressComponent ref="processDrawerqingjia" componentsWidth="50%" @refresh="refresh" @onCallback="handlerGlobalParams" />
  </section>
</template>

<script>
  import PanelSelection from '@/components/PanelSelection/panelSelection'
import { fundList,updateRecord,
        queryRecordAndWuLiuPage, addFundChangeBill } from '@/api/system/imprestFund'
import { mapGetters } from 'vuex'
import {imprestFundChangeProcessList} from '@/api/business/processapi'
import dayjs from 'dayjs'
import currency from 'currency.js'
import WorkFlowComponent from "@/components/workflow/index";
import ProgressComponent from '@/components/workflow/process'
export default {
  components: {
    WorkFlowComponent,ProgressComponent,PanelSelection
  },
  data() {
    return {
      recordOldList:{},
      recordOldDataList:{},
      updateProcessInfo:{},
      updateProcessDialogVisible:false,
      updateInfo: {
      },
      updateDialogVisible:false,
      shipLineGoodsInfo:{
        tonnage:0, // 吨位 计算总价
        invoiceType:10, // 费率 默认不开票
        payer:'', // 付款方
        customerGoodsCostId:'', // 货物id
        companyName:'', // 供应商id
        costName:'', // 费用类型
        totalPriceNo: 0, // 总价
        priceNo: 0, // 单价
      },
      shipLineShipGoodsType:0, // 0 船上费用 1 货物费用
      drawerShipLine:false,
      queryParams: {
        shipTime: null,
        shipName: ''
      },
      contractCompany:[{
        value:'海南成功',
        code:10
      },{
        value:'海南和盛',
        code:20
      }],
      billList: [
        { label: '无票', value: 0 },
        { label: '✓', value: 1 },
        {label:'收据',value:2}
      ],
      selCompanyIndex: -1,
      selComDetail: {},
      drawerRight: false,
      costDialog:false,
      recordLoading: false,
      drawerPrice: false,
      recordDialog: false,
      isPriceIncome: 3, // 1 日常 2  招待 3 业务费用
      isIncomeStatus: false, // true 收入 false 支出
      queryRecordFrom: {
        dates: null,
        isIncomeStatus: 1,
        isPriceIncome: null,

      },
      isProcessRecord: false,
      fundList: [],
      recordList: [],
      stagRecordList: [],
      lastApplyDetail: {},
      applyDetail: {
        expendDaily: 0, // 支出日常
        expendHosp: 0, // 支出招待费
        expendBus: 0, // 支出业务费用
        expendBusAndStartEnd:0, // 支出 按时间
        incomeBus: 0, // 收入业务费用
        totalExpenditure: 0, // 支出合计
        incomeBusAndStartEnd: 0, // 收入业务费用
        totalExpenditureAndStartEnd: 0, // 支出合计
        accountAmount: 0, // 现账上余额
        applyBalance: 0, // 申请金额
        applyAmount: 0, // 申请后余额
        applyPersonName: '', // 申请人
        useRemarks: '', // 资金用途
        applyCreateTime: dayjs().format('YYYY-MM-DD'), // 申请时间
        lastApplyCreateTime: '', // 上次请款时间
        lastApplyBalance: 0, // 上次请款金额
        lastApplyAmount: 0, // 上次请款后余额
      },
      pageRecordList: [],
      pageRecordTotal: 0,
      pageRecordNum: 1,
      pageRecordSize: 20,
      subType: 0, // 1 账单 2 申请备用金
      pageProcessList: [],
      pageProcessTotal: 0,
      pageProcessNum: 1,
      pageProcessSize: 15,
      pageProcessStatus: null,
      shipLineListTotal:0,
      shipCostList: [],
      showShipCostList: [],
      shipCostShipName: '',
      shipCostStartShipTime: '',
      shipCostEndShipTime: '',
      selShipCostList: [],
      isBillOpen: false,
      shipLineParams: {
        shipChuanQiTime:null,shipName:'',status:'1',pageNum:1,pageSize:10
      },
      shipLineList: [],
      shipChuanQiId:'',
      shipChuanQiIdRadio: '',
      supplierList:[],
      itemsOccurredList:[],
      dictionaryLists:{},
      shipLineInfo:{
        sysSupplierId: '',
        costPriceNo: '',
        tonnesNum: '',
        danjia: '',
        incomeCompany: '10',
        costProject: '',
        costBear: '',
        tax: '10',
        payer:''
      },
      payerList:[],
      costNameList:[],
      shipLineGoodsList: [],
      supplierCostTypeList:[],
      tmpLastMonthMoney: 0,
      tmpTotalExpenditureAndStartEnd: 0,
      recordUpShipLineType: '',
      recordUpShipLineCountByShipLineId: '',
      recordUpShipLineCount:0,
    }
  },
  computed: {
    upBtnDisByShipLine() {
      this.recordUpShipLineCountByShipLineId
      this.recordUpShipLineType
      return (shipLineId, type) => {
        if(!shipLineId){
          return false
        }
        // this.recordUpShipLineCountByShipLineId
        // this.recordUpShipLineType
        // 判断shipLineId 和 type 是否相等
        if (!this.recordUpShipLineCountByShipLineId) {
          return false
        }
        if (this.recordUpShipLineCountByShipLineId == shipLineId && this.recordUpShipLineType == type) {
          return false
        }
        return true
      }
    },
    ...mapGetters(['user'])
  },
  created() {
    this.loadData()
    var getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuex')
    Promise.all([getSupplier]).then(res=>{
      let v = res[0]
      if (v) {
        this.supplierList = v.supplierList
      }
    })
  },
  filters: {
    moneyFmt(v) {
      if (v) {
       return  currency(v, { symbol: '', precision: 2 }).format()
      }
      return 0
    },
    moneyFmtNull(v) {
      if (v) {
       return  currency(v, { symbol: '', precision: 2 }).format()
      }
      return ''
    },
    bankFmt(v,idx){
      if (v) {
        return v.split(',')[idx]
      }
      return '--'
    }
  },
  methods: {

    isEq(v1, v2) {
      return v1 == v2
    },
    cancelOldDataById(id) {
      // remove
      // this.recordOldList[id]=null
      // this.recordOldDataList[id]=null
      if (this.recordUpShipLineCountByShipLineId && this.recordOldList[id].shipLineId && this.recordOldList[id].shipLineId == this.recordUpShipLineCountByShipLineId) {
        this.recordUpShipLineCount -= 1
        console.log('remove', this.recordUpShipLineCount)
        if (this.recordUpShipLineCount == 0) {
          console.log('removenull')
          this.recordUpShipLineCountByShipLineId = null
          this.recordUpShipLineType = null
        }
      }
      // delete this.recordOldList[id]
      // delete this.recordOldDataList[id]
      this.$delete(this.recordOldList, id)
      this.$delete(this.recordOldDataList, id)
    },
    submitChangeAccountRecord(){
      // 判断是否有改帐记录
      if(!this.recordOldList || Object.keys(this.recordOldList).length==0){
        this.$message.error("请选择改帐数据")
        return
      }
      // 请输入改帐理由
      this.$prompt('请输入改帐理由', '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          this.subProcessChangeUpStowage(value||'--')
        }).catch(() => {

        });
      console.log(this.recordOldList)

    },
    updateRecordAndProcess(row,oldRow){
      this.updateProcessInfo = JSON.parse(JSON.stringify(row))
      this.updateOldProcessInfo = JSON.parse(JSON.stringify(oldRow))
      this.updateProcessDialogVisible = true
    },

    shipLineFmt(str) {
      if(!str || str=='undefined'){
        return '--'
      }
      const arr = str.split(',')
      let s = ''
      for (let i = 0; i < arr.length; i++) {
        if(s){
          s += ','
        }
        s += arr[i].split('|')[1] || '--'
      }
      return s
    },
    handleRightClose() {
      this.drawerRight=false
    },
    refresh(){},
    handlerGlobalParams(){

    },
    showshenpi(id, name) {
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    loadProcessList() {
      const params = {
        pageSize: this.pageProcessSize,
        pageNum: this.pageProcessNum,
        status: this.pageProcessStatus,
        fundId: this.selComDetail.id
      }
      imprestFundChangeProcessList(params).then(res => {
        console.log('processlist', res)
        if (res.resultCode === '0' && res.data) {
          this.pageProcessList = res.data
          this.pageProcessTotal = res.total
        }
      })
    },
    onCallback(processId) {
      const data = {
        processId,
        imprestFundId: this.selComDetail.id,
        balance: this.newSum,
        oldBalance: this.oldSum,
        amount: this.selComDetail.balance,
        companyId: this.selComDetail.companyId,
        companyName: this.selComDetail.companyName,
        expendTotal: this.sumRecordUpOut(),// 支出合计
        incomeBus: this.sumRecordUpIn(),// 收入合计
        recordInfo: this.objToList(this.recordOldList), // 收支记录
        oldRecordInfo: this.objToList(this.recordOldDataList), // 旧的收支记录
        param3: this.tmpChangeMsg,
      }
      addFundChangeBill(data).then(res=>{
        console.log('addFundChangeBill',res)
        if(res.resultCode==='0'){
          // this.$message.success('操作成功')
          this.recordOldList={}
          this.recordOldDataList={}
          this.updateComDetail()
        }
      })
    },
    objToList(obj){
      if(!obj){
        return []
      }
      const arr = []
      for(const key in obj){
        arr.push(obj[key])
      }
      return arr
    },
    dateFmt(v, fmt = 'YYYY-MM-DD') {
      if(!v){
        return ''
      }
      return dayjs(v).format(fmt)
    },
    eventPageProcess(e) {
      this.pageProcessNum = e
      this.loadProcessList()
    },

    // setMultiSelShipLine() {
    //   const selShipLineMultiListConst=[]
    //   this.$refs.multipleShipLineTable.selection.forEach(item => {
    //     selShipLineMultiListConst.push(item)
    //   })
    //   this.selShipLineMultiList = selShipLineMultiListConst
    // },
    eventPage(e) {
      this.pageRecordNum = e
      this.loadRecordListPage()
    },
    selStatus(value) {
      if (this.queryRecordFrom.isIncomeStatus == value) {
        this.queryRecordFrom.isIncomeStatus = null
      } else {
        this.queryRecordFrom.isIncomeStatus = value
      }
      this.queryPageRecord()
    },

    queryPageRecord() {
      this.pageRecordNum = 1
      this.loadRecordListPage()
    },
    loadRecordListPage() {
      console.log('p',this.queryRecordFrom.dates)
      const data = {
        startMonth: this.queryRecordFrom.dates ? this.queryRecordFrom.dates[0]:'',
        endMonth: this.queryRecordFrom.dates ? this.queryRecordFrom.dates[1]:'',
        fundId: this.selComDetail.id,
        state: this.queryRecordFrom.isIncomeStatus,
        // consumptionType: this.queryRecordFrom.isIncomeStatus == 2 ? 3 : this.queryRecordFrom.isPriceIncome,
        // status: 0,
        pageNum: this.pageRecordNum,
        pageSize: this.pageRecordSize
      }
      this.pageRecordList = []
      queryRecordAndWuLiuPage(data).then(res => {
        console.log('res', res)
        if (res.resultCode === '0' && res.data) {
          this.pageRecordList = res.data
          this.pageRecordTotal = res.total
        }
      })
    },

    fmtBill(idx) {
      const b = this.billList.filter(item => item.value == idx)
      if (b && b.length > 0) {
        return b[0].label
      }
      return ''
    },

    changeCompany(idx) {
      this.selCompanyIndex = idx
      this.selComDetail = this.fundList[this.selCompanyIndex]
      console.log('selComDetail', this.selComDetail)
      this.recordOldList={}
      this.recordOldDataList={}
      this.updateComDetail()

    },
    onBeforeSubmit(cb) {
      let flag = true
      this.$refs.processDrawerIndex.formValue = {
          shipLineId: this.recordUpShipLineCountByShipLineId || '',
          type: this.recordUpShipLineType || '',
          company: this.selComDetail.companyDepartId,
          goodsDetailId: this.recordUpShipLineGoodsDetailId || '',
          shipCostId: this.recordUpShipLineShipCostId || '',
          fundId: this.selComDetail.id
        }
      cb(flag)
    },
    loadData() {
      fundList().then(res => {
        if (res.resultCode == '0' && res.list && res.list.length > 0) {
          this.fundList = res.list
          this.selCompanyIndex = 0
          this.selComDetail = this.fundList[this.selCompanyIndex]
          this.loadRecordListPage()
          this.loadProcessList()
        }
      })
    },
    subProcessChangeUpStowage(msg){
      if(!this.recordOldList || Object.keys(this.recordOldList).length == 0){
        this.$message('请先选择一条记录')
        return
      }
      this.tmpChangeMsg = msg
      // 上报
      // 获取期初、期末余额
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      Promise.all([this.sumRecordOldList(),this.sumRecordNewList()]).then(res=>{
        this.oldSum = res[0]
        this.newSum = res[1]
        this.$refs.processDrawerIndex.drawer = true
        this.$refs.processDrawerIndex.title = '备用金改账'

        this.$refs.processDrawerIndex.datas = {
          fundId: this.selComDetail.id,
          oldList: this.recordOldDataList,
          updateDataList: this.recordOldList,
          oldSum: this.oldSum,
          newSum: this.newSum,
          changeMsg:msg
        }
        this.$refs.processDrawerIndex.globalParams = {
          params4: this.selComDetail.companyParentId,
          params1: this.selComDetail.id,
          params3: this.newSum,
          params5:msg
        }
        this.$refs.processDrawerIndex.processCode = 'reserveChangeBill'
        this.$refs.processDrawerIndex.briefContent = '公司：'+this.selComDetail.companyName+ '，改账理由：'+msg
        this.$refs.processDrawerIndex.doInit()
      }).finally(()=>{
        loading.close()
      })
    },
    sumRecordUpOut(){
      let sum = 0
      for(let key in this.recordOldList){
        if(this.recordOldList[key].state == 2){
          // sum += this.recordOldList[key].balance
          sum = currency(sum).add(this.recordOldList[key].balance).value
        }
      }
      return sum
    },
    sumRecordUpIn(){
      let sum = 0
      for(let key in this.recordOldList){
        if(this.recordOldList[key].state == 1){
          // sum += this.recordOldList[key].balance
          sum = currency(sum).add(this.recordOldList[key].balance).value
        }
      }
      return sum
    },
  sumRecordOldList() {
      return new Promise((resolve, reject) => {
        let sum = 0
        for (const key in this.recordOldDataList) {
          //   // 类型 state 1 收入 2 支出
          if (this.recordOldDataList[key].state == 1) {
            sum = currency(sum).add(this.recordOldDataList[key].balance).value
          } else {
            sum = currency(sum).subtract(this.recordOldDataList[key].balance).value
          }
        }
        resolve(sum)
      })
    },
    sumRecordNewList(){
      return new Promise((resolve, reject) => {
        let sum = 0
        for (const key in this.recordOldList) {
          // sum += this.recordOldList[key].balance
          // sum = currency(sum).add(this.recordOldList[key].balance).value
          if (this.recordOldList[key].state == 1) {
            sum = currency(sum).add(this.recordOldList[key].balance).value
          } else {
            sum = currency(sum).subtract(this.recordOldList[key].balance).value
          }
        }
        resolve(sum)
      })
    },
    submitUpdateRecordProcess() {
      console.log('submit', this.updateProcessInfo)
      this.updateProcessInfo.param2=this.updateProcessInfo.consumptionDate
      if (this.updateProcessInfo.id) {
        // 增加到列表
        // this.recordOldList[this.updateProcessInfo.id] = this.updateProcessInfo
        this.$set(this.recordOldList, this.updateProcessInfo.id, this.updateProcessInfo)
        this.recordOldDataList[this.updateOldProcessInfo.id] = this.updateOldProcessInfo
        if (this.updateProcessInfo.shipLineId) {
          this.recordUpShipLineType = this.updateProcessInfo.type
          this.recordUpShipLineCountByShipLineId = this.updateProcessInfo.shipLineId
          this.recordUpShipLineGoodsDetailId = this.updateProcessInfo.goodsCostId || ''
          this.recordUpShipLineShipCostId = this.updateProcessInfo.shipCostId || ''
          if(this.recordUpShipLineCount){
            this.recordUpShipLineCount += 1
          }else{
            this.recordUpShipLineCount = 1
          }
        }

      }
      this.updateProcessDialogVisible = false
    },
    submitUpdateRecord(){
      console.log('thisup', this.updateInfo)
      this.updateInfo.param2=this.updateInfo.consumptionDate
      if (this.updateInfo.id) {
        updateRecord(this.updateInfo).then(res => {
          if (res.resultCode == '0' && res.data) {
            this.$message.success('修改成功')
            this.updateDialogVisible = false
            this.loadRecordListPage()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    updateRecord(row) {
      this.updateInfo = JSON.parse(JSON.stringify(row))
      this.updateDialogVisible = true

    },


    closeRecordDialog() {
      // this.recordDialog = false
//      this.updateProcessDialogVisible = false
      this.updateComDetail()
    },
    updateComDetail() {
      // 更新账户余额
      // 更新 各支出合计

      // 更新第一页 列表
      // this.queryPageRecord()
      this.loadRecordListPage()
      // 更新流程列表
      this.loadProcessList()
      // this.loadRecordListPage()
    },
  }
}
</script>

<style scoped>
.content{
  margin:10px;
  display:flex;
  justify-content:space-between;
}
.mg30{
  margin:30px 15px 30px 10px;
}
.flexd{
  display:flex;
}
.flexdire{
  flex-direction:column;
}
.flexbet{
  display:flex;
  justify-content:space-around;
}
.fcenter{
  align-items:center;
}
.justcenter{
  justify-content:center;
}
.justaround{
  justify-content:space-around;
}
.contcenter{
  align-content:center;
}
.bodd{
  border: 1px solid #e0e0e0;
}
.minh30{
  min-height: 150px;
}
.tablerightcls{
  width:100%;
  border: 1px solid #e0e0e0;
  border-collapse: collapse;
  text-align: center;
}
.tablerightcls td,.tablerightcls th{
  padding:3px;
}
.tablecls{
  width:100%;
  border: 1px solid #e0e0e0;
  border-collapse: collapse;
  text-align: center;
}
.tablecls td,.tablecls th{
  padding:10px;
}
.colclk{
  color:#409EFF;
  cursor: pointer;
}
.mt10{
  margin-top:10px;
}
.mb10{
  margin-bottom: 5px;
}
.mtrb30{
  margin: 30px 10px 30px 10px;
}
.curpoi{
  cursor: pointer;
}
.font13{
  font-size:13px;
}
.shipLineDrawerCls{
  padding:0px;
}
.recordDiaCls{
  width: 100%;
  /* 滚动条 自动 */
  overflow-y: auto;
}
.minwd30{
  min-width:120px;
}
.minwd20{
  min-width:100px;
}
.tabbord{
  border: 1px solid #e0e0e0;
  /* border-collapse: collapse; */
}
.deltext{
  text-decoration: line-through;
}
.redtext{
  color:#f56c6c;
}
</style>
