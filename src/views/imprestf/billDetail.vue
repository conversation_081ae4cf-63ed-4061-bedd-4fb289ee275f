<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      备用金日记账明细表
    </div>
    <el-divider />
    <div>
      <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
        <el-form-item label="账户名:">
          <el-select v-model="queryForm.accid" disabled clearable placeholder="请选择">
            <el-option
              v-for="item in companyList"
              :key="item.id"
              :label="item.companyName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期:">
        <el-date-picker
          v-model="queryForm.shipTime"
          clearable

          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          type="monthrange"
          :picker-options="handleDatePickOptions()"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onQuery">查询</el-button>
      </el-form-item>
      </el-form>
    </div>
    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <div id="yewuzongbiao">
      <vxe-table
        ref="xTable"
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '业务总台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column field="consumptionDate" :formatter="publicFmtVxe" title="日期" align="center" width="200" />
        <vxe-table-column field="param1" :formatter="publicFmtVxe" title="说明" align="center" />
        <vxe-table-column field="incomeMoney" :formatter="publicFmtnumber" title="收入（借方）" align="center"  />
        <vxe-table-column field="expendMoney" :formatter="publicFmtnumber" title="支出（贷方）" align="center"  />
        <vxe-table-column field="diffMoney" :formatter="publicFmtnumber" title="余额" align="center"  />
        <!-- ordercompanynameFmt -->
        <vxe-table-column field="remarks" :formatter="publicFmtVxe" title="备注" align="center" />

      </vxe-table>
    </div>

  </section>
</template>

<script>
import { fundList,fundAccountDetail} from '@/api/system/imprestFund'
// import store from '@/store'
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
import dayjs from 'dayjs';


VXETable.use(VXETablePluginExportXLSX)

export default {
  name: 'BillDetail',
  data() {
    function  getFullDate(targetDate) {
      var D, y, m, d;
      if (targetDate) {
        D = new Date(targetDate);
        y = D.getFullYear();
        m = D.getMonth() + 1;
        d = D.getDate();
      } else {
        y = fullYear;
        m = month;
        d = date;
      }
      m = m > 9 ? m : '0' + m;
      d = d > 9 ? d : '0' + d;
      return y + '-' + m + '-' + d;
    }
        var nowDate = new Date();
        var fullYear = nowDate.getFullYear();
        var lastDate = new Date()
        lastDate.setMonth(nowDate.getMonth() + 1)
        var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
        var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
        var endDate = getFullDate(nowDate.setDate(endOfMonth));//当月最后一天
        var startDate =  getFullDate(new Date(fullYear,0,1));//当年第一天

    return {
      queryForm: {
        accid: '',
        shipTime: [startDate, endDate]
      },
      tableLoading:false,
      toolbarButtons: [],
      tableData: [],
      companyList: [],
      pickEndDate: lastDate

    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.queryForm.accid = this.$route.query.id
    }
    if (this.$route.query.startDate && this.$route.query.endDate) {
      this.queryForm.shipTime = [this.$route.query.startDate, this.$route.query.endDate]
    }

    // getDicts().then(res => {
    //   for (var a = 0; a < res.length; a++) {
    //     var item = res[a]
    //     if (item.name === 'plate') {
    //       this.banlist.push(item)
    //     }
    //   }
    //   console.log(this.banlist)
    // })
    // this.getCompany()

    this.loadInitData()
    this.onQuery()

    // console.log(`${store.getters.baseApi}api/wxDepartment/getAllDepartment`)
  },
  methods: {
    dayjsFmtMonth(time) {
      return dayjs(time).format('YYYY-MM')
    },
    loadInitData() {
      fundList().then(res => {
        if (res !== undefined && res.resultCode === '0') {
          this.companyList = res.list
        }
     })
    },
    handleDatePickOptions() {
      return {
        disabledDate: time => {
          // console.log(time)
          return time.getTime() > this.pickEndDate.getTime()
        },
        onPick({ maxDate, minDate }) {
          if (maxDate && maxDate.getTime() == minDate.getTime()) {
            // minDate = new Date(minDate.getTime())
            if (minDate.getTime() >= Date.now()) {
              minDate = new Date(minDate.setMonth(maxDate.getMonth() - 1))
            } else {
              maxDate = new Date(maxDate.setMonth(minDate.getMonth() + 1))
            }
          }
        }
      }
    },

    onQuery() {
      this.topage(1)
    },
    topage(pageNo) {
      this.tableLoading = true
      if (
        this.queryForm.shipTime != undefined &&
        this.queryForm.shipTime != null
      ) {
        this.queryForm.startTime = this.queryForm.shipTime[0]
        this.queryForm.endTime = this.queryForm.shipTime[1]
      } else {
        var [startDate,endDate] = this.getMonthDay()
        this.queryForm.startTime = startDate
        this.queryForm.endTime = endDate
      }
      this.loadData()
    },
    loadData() {
      this.tableLoading = true
      this.tableData = []
      fundAccountDetail({ fundId: this.queryForm.accid, startMonth: this.dayjsFmtMonth(this.queryForm.startTime), endMonth: this.dayjsFmtMonth(this.queryForm.endTime) }).then(res => {
        this.tableLoading = false
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.list
        }
      })
    },
    getMonthDay(){
         var nowDate = new Date();
        var fullYear = nowDate.getFullYear();
        var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
        var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
        var endDate = this.getFullDate(nowDate.setDate(endOfMonth));//当月最后一天
        var startDate = this.getFullDate(new Date(fullYear,0,1));//当年第一天
        return [startDate,endDate]
    },
    getFullDate(targetDate) {
      var D, y, m, d
      if (targetDate) {
        D = new Date(targetDate)
        y = D.getFullYear()
        m = D.getMonth() + 1
        d = D.getDate()
      } else {
        y = fullYear
        m = month
        d = date
      }
      m = m > 9 ? m : '0' + m
      d = d > 9 ? d : '0' + d
      return y + '-' + m + '-' + d
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },

    companyFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.allcompany.length; a++) {
          var item = this.allcompany[a]
          if (item.deptId == cellValue) {
            return item.label
          }
        }
      }
      return v
    },
    paymentTypeFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.paymentTypeList.length; a++) {
          var itm = this.paymentTypeList[a]
          if (itm.code === Number(v)) {
            return itm.value
          }
        }
      }
      return v
    },
    paymentTypeFmt(cellValue) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.paymentTypeList.length; a++) {
          var itm = this.paymentTypeList[a]
          if (itm.code === Number(v)) {
            return itm.value
          }
        }
      }
      return v
    },
    receiveTypeFmt(cellValue) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.receiveTypeList.length; a++) {
          var itm = this.receiveTypeList[a]
          if (itm.code === Number(v)) {
            return itm.value
          }
        }
      }
      return v
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = Number(cellValue).toFixed(2)
      }
      return v
    }

  }
}
</script>

<style scoped>

.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}
.cole8{
  color :#D3D5DB;
}
.cole9f{
  color:#1890ff;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
</style>
