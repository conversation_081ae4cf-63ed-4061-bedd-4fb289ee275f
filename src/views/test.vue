<template>
  <div>
    <div style="font-size: 30px">财务系统</div>

    <!-- 发起合同 -->
    <start-contract :show.sync="isDialogShow" @payload="setContractDialogPayload" @callbackData="setContractDialogCallbackData" @complete="contractComplete"></start-contract>

    <button @click="isDialogShow = !isDialogShow">
      {{ isDialogShow ? "关闭弹窗" : "打开弹窗" }}
    </button>
  </div>
</template>

<script>
import StartContractMixins from '@/mixins/start-contract.js'
export default {
  mixins: [StartContractMixins],
  data() {
    return {
      isDialogShow: false,
    };
  },
  mounted() {
    
  },
  methods: {
    
  }
};
</script>

<style>

</style>