<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">板块</label>
        <el-select v-model="query.plateId" class="filter-item" style="width: 140px;" placeholder="不限">
          <el-option
            v-for="item in plates"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <label class="el-form-item-label">公司</label>
        <el-select v-model="query.deptId" class="filter-item" style="width: 140px;" clearable placeholder="不限">
          <el-option
            v-for="item in companys"
            :key="item.wxDepartmentId"
            :label="item.companyname"
            :value="item.wxDepartmentId"
          />
        </el-select>
        <label class="el-form-item-label">类型</label>
        <el-select v-model="query.type" class="filter-item" style="width: 140px;" clearable placeholder="不限">
          <el-option
            v-for="item in types"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <label class="el-form-item-label">贴现类型</label>
        <el-select v-model="query.discountOrCash" class="filter-item" style="width: 140px;" clearable placeholder="不限">
          <el-option
            v-for="item in types2"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <label class="el-form-item-label">模糊搜索</label>
        <el-input v-model="query.blurry" clearable placeholder="银行名称、账户名称、账户号" style="width: 220px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="所属板块" prop="plateId">
            <el-select v-model="form.plateId" style="width: 370px;" placeholder="请选择板块">
              <el-option
                v-for="item in plates"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属公司" prop="deptId">
            <el-select v-model="form.deptId" style="width: 370px;" placeholder="请选择公司">
              <el-option
                v-for="item in companys"
                :key="item.wxDepartmentId"
                :label="item.companyname"
                :value="item.wxDepartmentId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="form.type" style="width: 370px;" placeholder="请选择类型">
              <el-option
                v-for="item in types"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="贴现类型" prop="discountOrCash">
            <el-select v-model="form.discountOrCash" style="width: 370px;" placeholder="请选择贴现类型">
              <el-option
                v-for="item in types2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="币种" prop="accountNationality">
            <el-select v-model="form.accountNationality" style="width: 370px;" placeholder="请选择币种">
              <el-option
                v-for="item in nations"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="银行名称" prop="bankName">
            <el-input v-model="form.bankName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="账户名称" prop="accountName">
            <el-input v-model="form.accountName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="账户号" prop="accountNumber">
            <el-input v-model="form.accountNumber" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="余额" prop="initializationMoney">
            {{form.initializationMoney}}
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="form.remarks" type="textarea" :rows="2" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="id" width="45" />
        <el-table-column prop="type" label="类型" width="60">
          <template slot-scope="scope">
            <span>{{ parseType(scope.row.type) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="discountOrCash" label="贴现类型" width="90">
          <template slot-scope="scope">
            <span>{{ parseType2(scope.row.discountOrCash) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="bankName" label="银行名称" />
        <el-table-column prop="accountName" label="账户名称" />
        <el-table-column prop="accountNumber" label="账户号" width="190" />
        <el-table-column prop="initializationMoney" label="余额" width="90" />
        <el-table-column prop="deptId" label="公司">
          <template slot-scope="scope">
            <span>{{ parseCompany(scope.row.deptId) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="accountNationality" label="币种">
          <template slot-scope="scope">
            <span>{{ parseNation(scope.row.accountNationality) }}</span>
          </template>
        </el-table-column>
        <el-table-column v-permission="['admin','sysAccount:edit','sysAccount:del']" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
// 数据字典
import dict from '@/api/system/dict'
import { getCompanyByPlateId } from '@/api/system/dept'

import crudSysAccount from '@/api/sysAccount'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, type: '1', bankName: null, accountName: null, accountNumber: null, initializationMoney: 0, dictionaryCode: null, createBy: null, createDate: null, updateBy: null, updateDate: null, delFlag: null, remarks: null, discountOrCash: 0, deptId: null, plateId: null, accountNationality: 0 }
export default {
  name: 'SysAccount',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '账户', url: 'api/sysAccount', idField: 'id', sort: 'id,desc', crudMethod: { ...crudSysAccount }, optShow: {
      add: true,
      edit: true,
      del: false,
      reset: false,
      download: true
    }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'sysAccount:add'],
        edit: ['admin', 'sysAccount:edit'],
        del: ['admin', 'sysAccount:del']
      },
      rules: {
      },
      queryTypeOptions: [
        { key: 'type', display_name: '类型' },
        { key: 'blurry', display_name: '模糊搜索' },
        { key: 'plateId', display_name: '板块' }
      ],

      plates: [],
      types: [{ label: '公户', value: '1' }, { label: '私户', value: '0' }],
      types2: [{ label: '现金账户', value: 0 }, { label: '贴现账户', value: 1 }, { label: '库存现金', value: 2 }],
      nations: [{ label: '人民币', value: 0 }, { label: '美元', value: 1 }],
      companys: null
    }
  },
  created() {
  },
  mounted() {
    dict.getPlateListByCurrentUser().then(res => {
      this.plates = Array.from(res.list, item => { item.value = parseInt(item.value); return item })
      if (this.plates.length === 0) {
        return
      }
      this.query.plateId = this.plates[0].value

      this.crud.toQuery()
    })
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      if (!this.query.plateId) {
        return false
      }
      getCompanyByPlateId(this.query.plateId).then(res => {
        this.companys = Array.from(res, item => { item.wxDepartmentId = parseInt(item.wxDepartmentId); return item })
        if (this.query.deptId) {
          this.crud.defaultForm.deptId = this.query.deptId
        } else {
          this.crud.defaultForm.deptId = this.companys[0].wxDepartmentId
        }
      })

      this.crud.defaultForm.plateId = this.query.plateId
      
      return true
    },
    parseType(value) {
      const res = this.types.filter((item) => item.value === value)
      return res.length > 0 ? res[0].label : ''
    },
    parseType2(value) {
      const res = this.types2.filter((item) => item.value === value)
      return res.length > 0 ? res[0].label : ''
    },
    parseCompany(value) {
      if (!this.companys) {
        return ''
      }
      // eslint-disable-next-line eqeqeq
      const res = this.companys.filter((item) => item.wxDepartmentId == value)
      return res.length > 0 ? res[0].companyname : ''
    },
    parseNation(value) {
      // eslint-disable-next-line eqeqeq
      const res = this.nations.filter((item) => item.value == value)
      return res.length > 0 ? res[0].label : ''
    }
  }
}
</script>

<style scoped>

</style>
