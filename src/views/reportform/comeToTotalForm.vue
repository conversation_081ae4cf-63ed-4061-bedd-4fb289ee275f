<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      往来款汇总表
    </div>
    <el-divider/>
    <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
      <el-form-item label="公司:" >
        <el-select v-model="queryForm.deptId" placeholder="请选择" :disabled="!useSelect" @change="onQuery">
          <el-option
            v-for="item in userCompanyIds "
            :key="item.deptId"
            :label="item.name"
            :value="item.deptId+''">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发生时间">
        <el-date-picker
          clearable
          v-model="queryForm.shipTime"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="handleDatePickOptions()"
        ></el-date-picker>
      </el-form-item>
         <el-form-item label="往来公司:" >
        <el-select v-model="queryForm.orDeptId" placeholder="请选择" clearable :disabled="!useSelect" @change="onQuery">
          <el-option
            v-for="item in userCompanyIds "
            :key="item.deptId"
            :label="item.name"
            :value="item.deptId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="onQuery" type="primary">查询</el-button>
      </el-form-item>
    </el-form>
   <vxe-toolbar
     export
     custom
     print
     ref="xToolbar"
     :buttons="toolbarButtons"
   >
   </vxe-toolbar>
    <div id="yewuzongbiao">
      <vxe-table
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        ref="xTable"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '往来款汇总', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column  field="companyOtherName"  :formatter="publicFmtVxe"  title="往来公司"  align="center" width="200"/>
        <vxe-table-column  field="shourumoney" :formatter="publicFmtnumber"  title="收入总额"  align="center"  width="200"/>
        <vxe-table-column  field="zhichumoney" :formatter="publicFmtnumber"  title="支出总额"  align="center"  width="200"/>
        <vxe-table-column  field="diff" :formatter="publicFmtnumber"  title="往来差额"  align="center"  width="200"/>
        <vxe-table-column  field="remarks" :formatter="publicFmtVxe"  title="备注"  align="center"  width="200"/>
        <!-- <vxe-table-column  field="shouruNum" :formatter="publicFmtVxe"  title="收入笔数"  align="center"  width="200"/>
        <vxe-table-column  field="zhichuNum" :formatter="publicFmtVxe" title="支出笔数"  align="center"  width="200"/> -->
        <vxe-table-column title="操作" align="center">
          <template slot-scope="scope">
            <el-button @click="todetail(scope.row)" type="text" size="small"  >查看</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
<!--    <el-pagination-->
<!--      background-->
<!--      layout="prev, pager, next"-->
<!--      :total="total"-->
<!--      :page-size="queryForm.pageSize"-->
<!--      :current-page="queryForm.pageNum"-->
<!--      style="text-align: right;padding: 10px 0px;background-color: #fff"-->
<!--      @current-change="eventPage"-->
<!--    />-->
  </section>
</template>

<script>
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx';
import VXETable from 'vxe-table'
import {userRole} from "../../api/system/jurisdiction";
import {getAllPlateDept, getDeptInfo} from "../../api/business/wxDepartmentapi";
import {getPayReceiveList} from "../../api/system/comeToPaymentWater";
VXETable.use(VXETablePluginExportXLSX)
import dayjs from 'dayjs'
export default {
  name: 'comeToTotal',
  props: {
    // 时间、公司、类型
    propTime: {
      type: Array,
      default: () => []
    },
    propCompany: {
      type: String,
      default: ''
    },
    propWlType: {
      type: String,
      default: ''
    },
    propNeiWai: {
      type: String,
      default: ''
    },
    propType: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      data: [],
      data1: [],
      form: {},
      banlist: [],
      toolbarButtons: [],
      tableLoading: false,
      companyList: [],
      accountList: [],
      total: 0,
      queryForm: {
        deptId: this.propCompany ? this.propCompany : '',
        orDeptId:  '',
        shipTime: this.propTime && this.propTime.length ? this.propTime : dayjs().date() > 3 ? [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
        : [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')],
      },
      tableData: [],
      userCompanyIds: null,
      useSelect: true,
      userRole: ["allRole"],
    }
  },
  mounted() {
    if (userRole(this.userRole)) {
      getAllPlateDept().then(res => {
        if(res) {
          this.userCompanyIds = res.list
          if (!res.list || res.list.length == 0) {
            this.queryForm.deptId = null
            // this.useSelect = false
          } else if (res.list != null && res.list.length >0 && this.queryForm.deptId == '') {
            this.queryForm.deptId = res.list[0].deptId+''
            // this.useSelect = false
          }
          this.useSelect = true
          // else {
          //   this.queryForm.deptId = res.list[0].deptId
          //   this.useSelect = true
          // }
          this.onQuery()
        }
      })
    } else {
      let ids = ''
      if (this.propCompany) {
        ids = this.propCompany
      }
      const data = {
        deptId: ids || ''
      }
      getDeptInfo(data).then(res => {
        if (res) {
          this.userCompanyIds = res.list
          if (!res.list || res.list.length == 0) {
            this.queryForm.deptId = null
            // this.useSelect = false
          } else if (res.list != null && res.list.length > 0 && this.queryForm.deptId == '' ) {
            this.queryForm.deptId = res.list[0].deptId+''
          }
          this.useSelect = true
          // else {
          //   this.queryForm.deptId = res.list[0].deptId
          //   this.useSelect = true
          // }
          this.onQuery();
        }
      })
    }


    // console.log(`${store.getters.baseApi}api/wxDepartment/getAllDepartment`)
  },
  methods: {
     handleDatePickOptions() {
      return {
        onPick({ maxDate, minDate }) {
          if (maxDate && maxDate.getTime() == minDate.getTime()) {
            // minDate = new Date(minDate.getTime())
              maxDate = new Date(maxDate.setDate(minDate.getDate() + 1))
          }
        }
      }},
    todetail(item) {
      if (this.propWlType) {
        this.$router.push({ path: '/comeToPayDetail/index', query: {companyId: item.companyDeptId ,companyOtherDeptId: item.companyOtherDeptId,companyName: item.companyName,companyOtherName: item.companyOtherName,
          startTime: this.queryForm.shipTime ? this.queryForm.shipTime[0] : '',endTime: this.queryForm.shipTime ? this.queryForm.shipTime[1] : '', wlType: this.propWlType
        }})
        return
      }
      this.$router.push({ path: '/comeToDetail', query: {companyId: item.companyDeptId ,companyOtherDeptId: item.companyOtherDeptId,companyName: item.companyName,companyOtherName: item.companyOtherName,
      startTime: this.queryForm.shipTime ? this.queryForm.shipTime[0] : '',endTime: this.queryForm.shipTime ? this.queryForm.shipTime[1] : ''
      }})
    },
    eventPage(e) {
      this.topage(e)
    },
    onQuery() {
      this.topage(1);
    },
    topage(pageNo) {
      this.tableLoading = true
      const data = {
        companyId: this.queryForm.deptId,
        orCompanyId: this.queryForm.orDeptId,
        isWangLaiType: this.propWlType || '',
        isNeiWai: this.propNeiWai || ''
      }
      if (this.queryForm.shipTime) {
        data['startTime']  = this.queryForm.shipTime[0]
        data['endTime'] = this.queryForm.shipTime[1]
      }
      console.log('pm',this.queryForm)
      console.log('data',data)
      getPayReceiveList(data).then(res => {
        this.tableLoading =false
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.list
        }
      })
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtVxe({row, column, cellValue, index}) {
      var v = "--";
      if (cellValue != undefined && cellValue != null && cellValue != "") {
        v = cellValue
      }
      return v;
    },
    publictypeVxe({row, column, cellValue, index}) {
      var v = 0;
      if (cellValue != undefined) {
        v = cellValue
        for (var a = 0;a<this.zhangType.length;a++) {
          var item = this.zhangType[a]
          if (v == item.value) {
            return item.label
          }
        }
      }
      return v;
    },
    publicFmtnumber({row, column, cellValue, index}) {
      var v = 0;
      if (cellValue != undefined && cellValue != null && cellValue != "") {
        v = cellValue.toFixed(2);

      }
      return v;
    },
    handleRowSave(row, done, loading) {
      this.data1.splice(0, 0, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      this.data1.splice(index, 1, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data1.splice(index, 1)
      })
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
</style>
