<template>
  <div style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 18px">
      现金流量表
    </div>
    <el-divider/>
    <div style="text-align: center;">
    <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
        <el-form-item label="公司:" >
          <el-select v-model="queryForm.companyid" clearable placeholder="请选择" @change="changeCompany()">
            <el-option
              v-for="item in companyList"
              :key="item.deptId"
              :label="item.name"
              :value="item.deptId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期:">
        <el-date-picker
          clearable
          v-model="queryForm.shipTime"
          @change="loadData()"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    </el-form>
  </div>
    <div style="width:700px;margin:0px auto 20px auto;">
      <!-- <template #buttons>
      <div style="text-align: center;font-size:16px;">{{companyName||'请选择公司'}} {{ queryForm.shipTime && queryForm.shipTime.length>=2 ? queryForm.shipTime[0]+'~'+queryForm.shipTime[1]:''  }}</div>
      </template> -->
    <vxe-toolbar
      export
      print
    >

    </vxe-toolbar>
    <!-- <el-table style="width:700px;margin:0px auto;" :data="list"  border stripe size="mini" highlight-current-row  >
      <el-table-column class-name="height30" prop="fundsClassName" label="类型">
        <template slot-scope="scope">
          <div><span v-for="(i,idx) in scope.row.level" :key="i" v-if="idx>0" style="margin-left: 10px"></span>{{scope.row.fundsClassName}}</div>
        </template>
      </el-table-column>
      <el-table-column  prop="explanation" label="释义说明"></el-table-column>
      <el-table-column align="center" prop="totalAmount" label="金额">
        <template slot-scope="scope">
          <div v-if="scope.row.fundsCode=='sum'">
            {{priceFmt(scope.row.totalAmount)}}
          </div>
          <div v-else-if="scope.row.isSel==1">
            {{scope.row.totalAmount?priceFmt(scope.row.totalAmount):'--'}}
          </div>
          <div v-else>

          </div>
        </template>
      </el-table-column>
    </el-table> -->
    <!-- vxe-table -->
    <vxe-table
      :data="list"
      border
      stripe
      size="mini"

      highlight-current-row
      :export-config="{filename: '企业现金流量', sheetName: '企业现金流量'}"
      :print-config="{filename: '企业现金流量', sheetName: '企业现金流量'}"
    >
      <vxe-table-column field="fundsClassName" title="类型">
        <template slot-scope="scope">
          <div><span v-for="(i,idx) in scope.row.level" :key="i" v-if="idx>0" style="margin-left: 10px"></span>{{scope.row.fundsClassName}}</div>
        </template>
      </vxe-table-column>
      <vxe-table-column align="center" field="totalAmount" title="金额">
        <template slot-scope="scope">
          <div v-if="scope.row.fundsCode && sumCodeList.includes(scope.row.fundsCode) ">{{scope.row.totalAmount?priceFmt(scope.row.totalAmount):''}}</div>
          <div v-else-if="scope.row.isSel==1">
            <el-button type="text" @click="toDetail(scope.row.fundsClassId)">{{scope.row.totalAmount?priceFmt(scope.row.totalAmount):''}}</el-button>
          </div>
        </template>
      </vxe-table-column>
      </vxe-table>
  </div>
  </div>
</template>
<script>
import 'vxe-table/lib/style.css'
import { cashFlow } from '@/api/system/fundsClassification'
import currency from 'currency.js';
import { selectCompanyList } from '@/api/system/user'
import dayjs from 'dayjs'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
export default {
  name: 'cashFlow',
  data() {
    return {
      queryForm: {
        companyid: '',
        shipTime: []
      },
      sumCodeList: [
        'cashNetInc', 'openingCash', 'endingCash', 'rec_sum_subt_out_sum', 'res_sum_subt_resout_sum', 'fundsub_sum_subt_fundsubout_sum',
        'rec_sum','out_sum','res_sum','resout_sum','fundsub_sum','fundsubout_sum'
      ],
      list: [],
      companyList: [],
      companyName: ''
    }
  },
  created() {
    this.shipTimeDefDay()
    this.loadCompany()
  },
  methods: {
    toDetail(flowId) {
      const startDate = this.queryForm.shipTime && this.queryForm.shipTime.length >= 2 && this.queryForm.shipTime[0] ? dayjs(this.queryForm.shipTime[0]).format('YYYY-MM-DD') : ''
      const endDate = this.queryForm.shipTime && this.queryForm.shipTime.length >= 2 && this.queryForm.shipTime[1] ? dayjs(this.queryForm.shipTime[1]).format('YYYY-MM-DD') : ''
      window.open(`/fundAnalysisDetail?flowIds=${flowId}&companyId=${this.queryForm.companyid}&startDate=${startDate}&endDate=${endDate}`,'_blank')
    },
    shipTimeDefDay() {
      // 3号前 查询上一月的数据 3号后查询本月数据
      const day = dayjs().date()
      if (day < 3) {
        this.queryForm.shipTime = [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')]
      } else {
        this.queryForm.shipTime = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
      }
    },
    changeCompany() {
      if (this.queryForm.companyid) {
        this.companyList.forEach(item => {
          if (item.deptId == this.queryForm.companyid) {
            this.companyName = item.name
            return
          }
        })
      } else {
        this.companyName = ''
      }
      this.loadData()
    },
    loadCompany() {
      selectCompanyList().then(res => {
        this.companyList = res.data
        this.defSelData()
      })
    },
    defSelData() {
      if(!this.companyList || this.companyList.length==0){
        return
      }
      this.queryForm.companyid = this.companyList[0].deptId
      this.companyName = this.companyList[0].name
      this.loadData()
    },
    loadData() {
      this.list = []
      const startDate = this.queryForm.shipTime && this.queryForm.shipTime.length >= 2 && this.queryForm.shipTime[0] ? dayjs(this.queryForm.shipTime[0]).format('YYYY-MM-DD') : ''
      const endDate = this.queryForm.shipTime && this.queryForm.shipTime.length >= 2 && this.queryForm.shipTime[1] ? dayjs(this.queryForm.shipTime[1]).format('YYYY-MM-DD') : ''
      cashFlow(this.queryForm.companyid, startDate, endDate).then(res => {
        this.list = res.data
      })
    },
    priceFmt(price) {
      return currency(price, { separator: ',', precision: 2, symbol: '' }).format()
    }
  }
}
</script>
<style scoped>
.height30{
  padding: 0px;
}
</style>
