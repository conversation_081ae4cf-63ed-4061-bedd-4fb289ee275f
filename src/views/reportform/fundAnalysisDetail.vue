<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      资金分析明细表
    </div>
    <el-divider />
    <div>
      <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
        <el-form-item label="公司名:">
          <el-select v-model="queryForm.companyId" @change="onQuery"  placeholder="请选择">
            <el-option
              v-for="item in companyList"
              :key="item.wxDepartmentId"
              :label="item.companyname"
              :value="item.wxDepartmentId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类:" >
         <FcItem style="margin: 0;" :list="fundsClassList" v-model="fundsSelItemId" @clkItem="fundClassClk" ></FcItem>
        </el-form-item>
        <!-- <el-form-item label="往来单位:">
          <el-input v-model="queryForm.comecompany" placeholder="请输入往来单位" clearable />
        </el-form-item> -->
        <!-- <el-form-item v-show="queryForm.iorp === 1" label="付款方式:">
          <el-select v-model="queryForm.type" clearable placeholder="请选择">
            <el-option
              v-for="item in paymentTypeList "
              :key="item.value"
              :label="item.value"
              :value="item.code"
            />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item v-show="queryForm.iorp === 0" label="收款方式:">
          <el-select v-model="queryForm.type" clearable placeholder="请选择">
            <el-option
              v-for="item in receiveTypeList "
              :key="item.value"
              :label="item.value"
              :value="item.code"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="日期:">
          <el-date-picker
            v-model="queryForm.shipTime"
            clearable

            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            type="daterange"
            :picker-options="handleDatePickOptions()"
          />
        </el-form-item>
        <el-form-item>
        <el-button type="primary" @click="onQuery">查询</el-button>
      </el-form-item>
        <!-- <el-form-item label="金额:">
          <div style="display: inline-flex">
            <el-input v-model="queryForm.moneyMin" type="number" placeholder="最小值" />
            <span style="padding-left: 10px;padding-right: 10px">至</span>
            <el-input v-model="queryForm.moneyMax" type="number" placeholder="最大值" />
          </div>
        </el-form-item> -->
      </el-form>
    </div>
    <!-- <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline"> -->
      <!-- <el-form-item label="支付方式:">
        <el-select v-model="queryForm.costType" clearable placeholder="请选择">
          <el-option
            v-for="item in costType "
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="收支类型:">
        <el-select v-model="queryForm.iorp" clearable placeholder="请选择">
          <el-option
            v-for="item in invoiceorpayment "
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注:">
        <el-input v-model="queryForm.remake" type="text" placeholder="备注" />
      </el-form-item> -->
      <!-- <el-form-item>
        <el-button type="primary" @click="onQuery">查询</el-button>
      </el-form-item> -->
    <!-- </el-form> -->
    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <div id="yewuzongbiao">
      <vxe-table
        ref="xTable"
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        show-overflow
        show-footer
        :footer-method="footerMethod"
        stripe
        size="small"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '资金分析明细', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column field="occurDate" fixed="left" show-overflow  :formatter="publicDateFmt" title="日期" align="center" width="110" />
        <vxe-table-column field="fundsClassificationName" :formatter="publicFmtVxe" title="收支类型" align="center" width="150" />
        <vxe-table-column field="fundsMoneyName" :formatter="publicFmtVxe" title="现金流量" show-overflow="tooltip" align="center" width="150" />
        <vxe-table-column field="rmoney" :formatter="publicFmtnumber" title="收入（借方）" align="center" width="100" />
        <vxe-table-column field="pmoney" :formatter="publicFmtnumber" title="支出（贷方）" align="center" width="100" />
        <!-- <vxe-table-column field="money" :formatter="publicFmtnumber" title="余额" align="center" width="100" /> -->
        <!-- ordercompanynameFmt -->
        <vxe-table-column field="summary"  :formatter="publicFmtVxe" title="摘要" align="center" show-overflow="tooltip" min-width="200" />
        <vxe-table-column field="customerName" :formatter="publicFmtVxe" title="往来单位" show-overflow="tooltip" align="center" width="200" />
        <vxe-table-column field="paymentType" :formatter="costTypeFmtVxe" title="支付方式" align="center" width="100" />
        <vxe-table-column field="remarks" :formatter="publicFmtVxe" title="备注" show-overflow="tooltip" align="center" width="100" />
        <vxe-table-column field="companyName" :formatter="publicFmtVxe" title="收付款公司" show-overflow="tooltip" align="center" width="150"/>
        <vxe-table-column field="bankName" :formatter="publicFmtVxe" title="收付款银行" show-overflow="tooltip" align="center" width="160"/>
        <vxe-table-column field="accountNumber" :formatter="publicFmtVxe" title="收付款账户" align="center" width="180" />
        <vxe-table-column label="操作" align="center" width="150" fixed="right" show-overflow  >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showshenpi(scope.row.processId)">查看</el-button>
              <el-button @click="showPic(scope.row)"  :class="scope.row.picList?'cole9f':'cole8'" type="text" size="small">{{scope.row.picList?'查看财务截图':'暂无财务截图'}}</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    /> -->
    <ProgressComponent ref="processDrawerqingjia" style="z-index:9999" @refresh="refresh" @onCallback="handlerGlobalParams" />
     <el-dialog
      class="xuanxiang"
      title="查看财务截图"
      :visible.sync="showPicApply"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div v-for="(item,index) in picUrl" :key="index" style="text-align:center;">
        <image-rotate :src="item"></image-rotate>
      </div>
    </el-dialog>
  </section>
</template>

<script>
// import store from '@/store'
import { getDicts } from '@/api/system/dict'
import 'vxe-table/lib/style.css'
import { getAllCompany } from '@/api/business/wxDepartmentapi'
import { fundsAnalysisDetail,query } from '@/api/system/fundsClassification'
import {
  getCompanyByPlate,
  selectAccount,
  getAccountMoney,
  getAccountMoneyDetail,
  getMoneyForMonthStart
} from '@/api/business/accountMoneyForMonthapi'
import ProgressComponent from '@/components/workflow/process'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
import { getDictionaryList } from '@/api/system/baseInit'
import { getAllSysAccount } from '@/api/system/sysAccount'
import ImageRotate from '@/components/ImageRotate/index'
import FcItem from '@/views/components/fcitem/fcitem'
import { onlyTreeList } from '@/api/system/fundsClassification'
VXETable.use(VXETablePluginExportXLSX)

export default {
  name: 'FundAnalysisDetail',
  components: {
    ProgressComponent,ImageRotate,FcItem
  },
  data() {

    function  getFullDate(targetDate) {
      var D, y, m, d;
      if (targetDate) {
        D = new Date(targetDate);
        y = D.getFullYear();
        m = D.getMonth() + 1;
        d = D.getDate();
      } else {
        y = fullYear;
        m = month;
        d = date;
      }
      m = m > 9 ? m : '0' + m;
      d = d > 9 ? d : '0' + d;
      return y + '-' + m + '-' + d;
    }
        var nowDate = new Date();
        var fullYear = nowDate.getFullYear();
        var lastDate = new Date()
        lastDate.setMonth(nowDate.getMonth() + 1)
        var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
        var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
        var endDate = getFullDate(nowDate.setDate(endOfMonth));//当月最后一天
        var startDate =  getFullDate(new Date(fullYear,0,1));//当年第一天

    return {
      costType: [
        { value: 1, label: '现金' },
        { value: 2, label: '网银汇款' },
        { value: 3, label: '承兑汇票' }
      ],
      invoiceorpayment: [
        { value: 0, label: '收款' },
        { value: 1, label: '付款' },
        { value: 2, label: '全部' }
      ],
      picUrl:[],
      showPicApply:false,
      data: [],
      data1: [],
      form: {},
      banlist: [],
      toolbarButtons: [],
      tableLoading: false,
      allcompany: [],
      companyList: [],
      accountList: [],
      receiveTypeList: [],
      fundsSelItemId:[],
      bankName: '',
      accountNumber: '',
      total: 0,
      money: 0,
      queryForm: {
        iorp: 2,
        pageSize: 10,
        pageNum: 1,
        companyId: '',
        fundClassId: '',
        flowIds:'',
        shipTime: [startDate, endDate]
      },
      fundsClassList:[],
      paymentTypeList: [],
      tableData: [],
      sysAccountList: [],
      pickEndDate: lastDate

    }
  },
  mounted() {
    // 获取类型id 公司，日期
    // this.$route.query.fundClassId, this.$route.query.companyId, this.$route.query.startDate, this.$route.query.endDate
    if (this.$route.query.fundClassId) {
      this.queryForm.fundClassId = this.$route.query.fundClassId
    }
    if (this.$route.query.flowIds) {
      this.queryForm.flowIds = this.$route.query.flowIds
    }
    if (this.$route.query.companyId) {
      this.queryForm.companyId = this.$route.query.companyId
    }
    if (this.$route.query.startDate && this.$route.query.endDate) {
      this.queryForm.shipTime = [this.$route.query.startDate,this.$route.query.endDate]
    }

    this.loadList(this.$route.query.fundClassId,this.$route.query.companyId,this.$route.query.startDate,this.$route.query.endDate,this.$route.query.flowIds)

    // if (this.$route.query.id) {
    //   this.queryForm.accid = Number(this.$route.query.id)
    // }
    // if (this.$route.query.money) {
    //   this.money = Number(this.$route.query.money)
    // }
    // if (this.$route.query.bankName) {
    //   this.bankName = this.$route.query.bankName
    // }
    // if (this.$route.query.number) {
    //   this.accountNumber = this.$route.query.number
    // }
    // if (this.$route.query.startDate && this.$route.query.endDate) {
    //   this.queryForm.shipTime = [this.$route.query.startDate, this.$route.query.endDate]
    // }
    // getAllSysAccount().then(res => {
    //   this.sysAccountList = res.list
    // })
    // getDicts().then(res => {
    //   for (var a = 0; a < res.length; a++) {
    //     var item = res[a]
    //     if (item.name === 'plate') {
    //       this.banlist.push(item)
    //     }
    //   }
    //   console.log(this.banlist)
    // })
    this.getCompany()
    this.loadFundsClassList()
    this.loadFundsIds(this.queryForm.fundClassId||this.$route.query.flowIds)
    // getDictionaryList('fdbiz_payment_type,fdbiz_cost_type').then(res => {
    //   this.paymentTypeList = res.map['fdbiz_payment_type']
    //   this.receiveTypeList = res.map['fdbiz_cost_type']
    //   this.paymentTypeList.unshift({ 'code': '业务费用付款', 'value': '业务费用付款' })
    //   this.paymentTypeList.unshift({ 'code': '财务对外付款', 'value': '财务对外付款' })
    //   this.paymentTypeList.unshift({ 'code': '对外付款', 'value': '对外付款' })
    //   this.paymentTypeList.unshift({ 'code': '借款', 'value': '借款' })
    //   this.paymentTypeList.unshift({ 'code': '往来款', 'value': '往来款' })
    //   this.paymentTypeList.unshift({ 'code': '费用报销', 'value': '费用报销' })
    //   this.paymentTypeList.unshift({ 'code': '差旅报销', 'value': '差旅报销' })
    //   this.paymentTypeList.unshift({ 'code': '经营付款（油品）', 'value': '经营付款（油品）' })
    // })
    // getAllCompany().then(res => {
    //   this.allcompany = res.data
    // })
    // this.getAccount()
    // this.onQuery()

    // console.log(`${store.getters.baseApi}api/wxDepartment/getAllDepartment`)
  },
  methods: {
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count = this.currency(count).add(item[field]).value
      })
      return count
    },
    footerMethod({ columns,data }) {
      const footerData = [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (['pmoney','rmoney'].includes(column.property)) {
            return this.sumNum(data, column.property)
          }
          return null
        })
      ]
      return footerData
    },
    loadFundsIds(fundClassId) {
      this.fundsSelItemId=[]
      query({ id: fundClassId }).then(res => {
        if(res && res.content && res.content.length==1 && res.content[0].param3)
        this.fundsSelItemId = res.content[0].param3.split(',')
      })
    },
    fundClassClk(item) {
      if (this.$route.query.flowIds) {
        this.queryForm.flowIds = item.id
        this.onQuery()
        return
      }
      this.queryForm.fundClassId = item.id
      this.onQuery()
    },
    loadFundsClassList() {
      this.fundsClassList = []
      if (this.fundsClassList.length == 0) {
        if (this.$route.query.flowIds) {
          onlyTreeList('flow').then(res => {
            if (res.resultCode == '0' && res.data) {
              this.fundsClassList = this.fundsClassList.concat(res.data)
              // console.log('fundsClassList', this.fundsClassList)
            }
          })
          return
        }
        onlyTreeList('out').then(res => {
          if (res.resultCode == '0' && res.data) {
            this.fundsClassList = this.fundsClassList.concat(res.data)
            // console.log('fundsClassList', this.fundsClassList)
          }
        })
        onlyTreeList('rec').then(res => {
          if (res.resultCode == '0' && res.data) {
            this.fundsClassList = this.fundsClassList.concat(res.data)
            // console.log('fundsClassList', this.fundsClassList)
          }
        })
      }
    },
    loadList(fundClassId, companyId, startDate, endDate,flowIds) {
      this.tableData = []
      this.tableLoading = true
        fundsAnalysisDetail(companyId, startDate, endDate, fundClassId,flowIds).then(res => {
            if (res && res.data) {
              this.tableData=res.data
            }
        }).finally(() => {
          this.tableLoading = false
        })
      },
      showPic(item) {
      if (!item.picList) {
        this.$message.warning('暂无财务截图!')
        return
      }
      this.picUrl = []
      this.showPicApply = true
      this.picUrl = item.picList.split(',')
    },
     handleClose() {
      this.showPicApply = false
    },
    handleDatePickOptions() {
      return {
        disabledDate: time => {
          // console.log(time)
          return time.getTime() > this.pickEndDate.getTime()
        },
        onPick({ maxDate, minDate }) {
          if (maxDate && maxDate.getTime() == minDate.getTime()) {
            // minDate = new Date(minDate.getTime())
            if (minDate.getTime() >= Date.now()) {
              minDate = new Date(minDate.setMonth(maxDate.getMonth() - 1))
            } else {
              maxDate = new Date(maxDate.setMonth(minDate.getMonth() + 1))
            }
          }
        }
      }
    },
    getCompany() {
      console.log(this.queryForm)
      // if (!this.queryForm.banid){
      //   this.queryForm.companyid = undefined
      // }
      this.getAccount()
      getCompanyByPlate(this.queryForm.banid).then(res => {
        this.companyList = res.data
      })
    },
    refresh() {
      this.topage(1)
    },
    handlerGlobalParams() {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      // console.log(**********)
    },
    showshenpi(id, name) {
      if (!id) {
        this.$message({
          message: '该流程暂无审批详情',
          type: 'warning'
        })
        return
      }
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.doInit()
    },
    getAccount() {
      selectAccount().then(res => {
        this.accountList = res.list
      })
    },
    ordercompanynameFmt({ row, column, cellValue, index }) {
      if (!cellValue) {
        return ' '
      }
      for (var a = 0; a < this.sysAccountList.length; a++) {
        var item = this.sysAccountList[a]
        if (item.id === Number(cellValue)) {
          return item.accountName
        }
      }
      return cellValue
    },
    eventPage(e) {
      this.topage(e)
    },
    onQuery() {
      this.topage(1)
    },
    topage(pageNo) {
      // this.tableLoading = true
      // this.queryForm.pageNum = pageNo

      if (
        this.queryForm.shipTime != undefined &&
        this.queryForm.shipTime != null
      ) {
        this.queryForm.startTime = this.queryForm.shipTime[0]
        this.queryForm.endTime = this.queryForm.shipTime[1]
      } else {
        var [startDate,endDate] = this.getMonthDay()
        this.queryForm.startTime = startDate
        this.queryForm.endTime = endDate
      }
      this.loadList(this.queryForm.fundClassId,this.queryForm.companyId,this.queryForm.startTime,this.queryForm.endTime,this.queryForm.flowIds)
      // this.loadData()
    },
    async loadData() {
      const startMoney = getMoneyForMonthStart(this.queryForm.accid, this.queryForm.startTime, this.queryForm.endTime)
      const detail = getAccountMoneyDetail(this.queryForm)
      const result = await startMoney
      const res = await detail
      if (result.resultCode === '0') {
        this.money = result.data || 0
      } else {
        this.money = 0
      }

      if (res !== undefined && res.resultCode === '0') {
        this.tableData = res.list
        var lishimoney = 0
        for (var a = 0; a < this.tableData.length; a++) {
          var item = this.tableData[a]
          lishimoney = lishimoney + this.wupublicFmtnumber(item.rmoney) - this.wupublicFmtnumber(item.pmoney)
          item.money = (this.money + lishimoney).toFixed(2)
        }
      }
      this.tableData.unshift({ type: '初始化余额', money: this.money })
      this.tableLoading = false
    },
    getMonthDay(){
         var nowDate = new Date();
        var fullYear = nowDate.getFullYear();
        var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
        var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
        var endDate = this.getFullDate(nowDate.setDate(endOfMonth));//当月最后一天
        var startDate = this.getFullDate(new Date(fullYear,0,1));//当年第一天
        return [startDate,endDate]
    },
    getFullDate(targetDate) {
      var D, y, m, d
      if (targetDate) {
        D = new Date(targetDate)
        y = D.getFullYear()
        m = D.getMonth() + 1
        d = D.getDate()
      }
      m = m > 9 ? m : '0' + m
      d = d > 9 ? d : '0' + d
      return y + '-' + m + '-' + d
    },
    dateFmt(v, fmt = 'YYYY-MM-DD') {
      if(!v){
        return ''
      }
      return this.dayjs(v).format(fmt)
    },
    publicDateFmt({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        // v = this.getFullDate(cellValue)
        v = this.dateFmt(cellValue)
      }
      return v
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    costTypeFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.costType.length; a++) {
          var itm = this.costType[a]
          if (itm.value === Number(v)) {
            return itm.label
          }
        }
      }
      return v
    },

    companyFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.allcompany.length; a++) {
          var item = this.allcompany[a]
          if (item.deptId == cellValue) {
            return item.label
          }
        }
      }
      return v
    },
    paymentTypeFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.paymentTypeList.length; a++) {
          var itm = this.paymentTypeList[a]
          if (itm.code === Number(v)) {
            return itm.value
          }
        }
      }
      return v
    },
    paymentTypeFmt(cellValue) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.paymentTypeList.length; a++) {
          var itm = this.paymentTypeList[a]
          if (itm.code === Number(v)) {
            return itm.value
          }
        }
      }
      return v
    },
    receiveTypeFmt(cellValue) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.receiveTypeList.length; a++) {
          var itm = this.receiveTypeList[a]
          if (itm.code === Number(v)) {
            return itm.value
          }
        }
      }
      return v
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = Number(cellValue).toFixed(2)
      }
      return v
    },
    wupublicFmtnumber(cellValue) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = Number(cellValue)
      }
      return v
    },
    handleRowSave(row, done, loading) {
      this.data1.splice(0, 0, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      this.data1.splice(index, 1, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data1.splice(index, 1)
      })
    }
  }
}
</script>

<style scoped>

.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}
.cole8{
  color :#D3D5DB;
}
.cole9f{
  color:#1890ff;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
</style>
