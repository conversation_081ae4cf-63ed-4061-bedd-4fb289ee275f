<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      备用金明细表
    </div>
    <el-divider/>
    <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
      <el-form-item label="办事处:">
        <el-select v-model="queryForm.cashId" clearable placeholder="请选择">
          <el-option
            v-for="item in cashList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期:">
        <el-date-picker
          clearable
          v-model="queryForm.shipTime"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="金额:">
        <div style="display: inline-flex">
          <el-input v-model="queryForm.moneyMin" type="number" placeholder="最小值"/>
          <span style="padding-left: 10px;padding-right: 10px">至</span>
          <el-input v-model="queryForm.moneyMax" type="number" placeholder="最大值"/>
        </div>
      </el-form-item>
      <el-form-item label="摘要:">
        <el-input v-model="queryForm.remake" type="text" placeholder="摘要"/>
      </el-form-item>
      <el-form-item>
        <el-button @click="onQuery" type="primary">查询</el-button>
      </el-form-item>
    </el-form>
    <vxe-toolbar
      export
      custom
      print
      ref="xToolbar"
      :buttons="toolbarButtons"
    >
    </vxe-toolbar>
    <div id="yewuzongbiao">
      <vxe-table
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        ref="xTable"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '备用金', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column field="createDate"  title="日期" align="center" width="200"
                          :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'YYYY-MM-DD')}"
        />
        <vxe-table-column field="spare1" :formatter="publicFmtVxe" title="摘要" align="center" width="150"/>
        <vxe-table-column field="type" :formatter="typeFmtVxe" title="收支类型" align="center" width="100"/>
        <vxe-table-column field="money" :formatter="publicFmtnumber" title="金额" align="center" width="150"/>
        <vxe-table-column field="spare4" :formatter="publicFmtVxe" title="票据情况" align="center" width="50"/>
        <vxe-table-column field="spare3" :formatter="publicFmtVxe" title="备注" align="center"/>

      </vxe-table>
    </div>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />
    <ProgressComponent ref="processDrawerqingjia" style="z-index:9999" @refresh="refresh" @onCallback="handlerGlobalParams" />
  </section>
</template>

<script>
// import store from '@/store'
import { getDicts } from '@/api/system/dict'
import 'vxe-table/lib/style.css'
import {
  getCompanyByPlate,
  selectAccount,
  getReceiveWater
} from '@/api/business/accountMoneyForMonthapi'
import {getCashAndDetail} from '@/api/business/sysPettycashapi'
import ProgressComponent from '@/components/workflow/process'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
import dayjs from 'dayjs'

VXETable.use(VXETablePluginExportXLSX)

export default {
  name: 'pettycash',
  components: {
    ProgressComponent
  },
  data() {
    return {
      costType: [
        { value: 5, label: '其他' },
        { value: 2, label: '网银汇款' },
        { value: 1, label: '现金' },
        { value: 3, label: '承兑汇票' },
        { value: 4, label: '支票' }
      ],
      data: [],
      data1: [],
      form: {},
      banlist: [],
      toolbarButtons: [],
      tableLoading: false,
      companyList: [],
      accountList: [],
      cashList:[],
      total: 0,
      queryForm: {
        cashId:undefined,
        pageSize: 10,
        pageNum: 1
      },
      tableData: []

    }
  },
  mounted() {
    getDicts().then(res => {
      for (var a = 0; a < res.length; a++) {
        var item = res[a]
        if (item.name === 'plate') {
          this.banlist.push(item)
        }
      }
      console.log(this.banlist)
    })
    this.getCompany()
    this.getAccount()
    this.onQuery()

    // console.log(`${store.getters.baseApi}api/wxDepartment/getAllDepartment`)
  },
  methods: {
    getCompany() {
      console.log(this.queryForm)
      // if (!this.queryForm.banid){
      //   this.queryForm.companyid = undefined
      // }
      this.getAccount()
      getCompanyByPlate(this.queryForm.banid).then(res => {
        this.companyList = res.data
      })
    },
    refresh() {
      this.topage(1)
    },
    handlerGlobalParams() {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      // console.log(**********)
    },
    showshenpi(id, name) {
      if (!id){
        this.$message({
          message: '该流程暂无审批详情',
          type: 'warning'
        });
        return;
      }
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.doInit()
    },
    getAccount() {
      selectAccount().then(res => {
        this.accountList = res.list
      })
    },
    eventPage(e) {
      this.topage(e)
    },
    onQuery() {
      this.topage(1)
    },
    topage(pageNo) {
      this.tableLoading = true
      this.queryForm.pageNum = pageNo

      if (
        this.queryForm.shipTime != undefined &&
        this.queryForm.shipTime != null
      ) {
        this.queryForm.startTime = this.queryForm.shipTime[0]
        this.queryForm.endTime = this.queryForm.shipTime[1]
      } else {
        var nowDate = new Date();
        var fullYear = nowDate.getFullYear();
        var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
        var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
        var endDate = this.getFullDate(nowDate.setDate(endOfMonth));//当月最后一天
        var startDate = this.getFullDate(nowDate.setDate(1));//当月第一天
        this.queryForm.startTime = startDate
        this.queryForm.endTime = endDate
      }
      getCashAndDetail(this.queryForm).then(res => {
        this.tableLoading = false
        if (res !== undefined && res.resultCode === '0') {
          console.log(res)
          this.cashList = res.cashlist
          if (this.cashList) {
            this.total = res.total
            if (!this.queryForm.cashId) {
              this.queryForm.cashId = this.cashList[0].id
            }
            this.tableData = res.waterlist
            for (var a = 0;a<this.cashList.length;a++){
              var item = this.cashList[a]
              if(item.id === this.queryForm.cashId){
                this.tableData.unshift({"spare1":item.name + "备用金余额","money":item.money})
              }
            }
          }
        }
      })
    },
    getFullDate(targetDate) {
      var D, y, m, d;
      if (targetDate) {
        D = new Date(targetDate);
        y = D.getFullYear();
        m = D.getMonth() + 1;
        d = D.getDate();
      } else {
        y = fullYear;
        m = month;
        d = date;
      }
      m = m > 9 ? m : '0' + m;
      d = d > 9 ? d : '0' + d;
      return y + '-' + m + '-' + d;
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    typeFmtVxe({ row, column, cellValue, index }) {
      var v = ''
      if (cellValue === 1){
        v = '支出'
      } else if(cellValue === 0){
        v = '收入'
      }
      return v
    },
    costTypeFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.costType.length; a++) {
          var itm = this.costType[a]
          if (itm.value === Number(v)) {
            return itm.label
          }
        }
      }
      return v
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue == undefined || cellValue == null || cellValue == "") {
        return "--";
      }
      return dayjs(cellValue).format(fmtstr);
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue.toFixed(2)

      }
      return v
    },
    handleRowSave(row, done, loading) {
      this.data1.splice(0, 0, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      this.data1.splice(index, 1, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data1.splice(index, 1)
      })
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
</style>
