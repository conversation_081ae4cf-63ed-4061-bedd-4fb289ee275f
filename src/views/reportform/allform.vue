<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      资金账户汇总表
    </div>
    <el-divider />
    <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
      <el-form-item label="公司:">
        <el-select v-model="queryForm.companyid" clearable placeholder="请选择" @change="getAccount()">
          <el-option
            v-for="item in companyList"
            :key="item.deptId"
            :label="item.name"
            :value="item.deptId+''">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期:">
        <el-date-picker
          v-model="queryForm.shipTime"
          clearable
          type="monthrange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="handleDatePickOptions()"
        />
      </el-form-item>
      <el-form-item label="账户:">
        <el-select v-model="queryForm.accid" clearable filterable placeholder="请选择">
          <el-option
            v-for="item in accountList"
            :key="item.id"
            :label="item.bankName||item.accountName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账户类型:">
        <el-select v-model="queryForm.discount" clearable placeholder="请选择">
          <el-option
            v-for="item in zhangType "
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <div id="yewuzongbiao">
      <vxe-table
        ref="xTable"
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '业务总台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column field="accountName" :formatter="publicFmtVxe" title="公司" align="center" />
        <vxe-table-column field="bankName" :formatter="publicFmtVxe" title="开户行" align="center" />
        <vxe-table-column field="accountNumber" :formatter="publicFmtVxe" title="账户号" align="center" />
        <vxe-table-column field="discountOrCash" :formatter="publictypeVxe" title="账户类型" width="100" align="center" />
        <vxe-table-column field="qichu" :formatter="publicFmtnumber" title="期初余额" align="center" width="100" />
        <vxe-table-column field="shourumoney" :formatter="publicFmtnumber" title="收入总额" align="center" width="100" />
        <vxe-table-column field="zhichumoney" :formatter="publicFmtnumber" title="支出总额" align="center" width="100" />
        <vxe-table-column field="qimo" :formatter="publicFmtnumber" title="期末余额" align="center" width="100" />
        <vxe-table-column field="shouruNum" title="收入笔数" align="center" width="50" />
        <vxe-table-column field="zhichuNum" title="支出笔数" align="center" width="50" />
        <vxe-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="todetail(scope.row)">查看</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />
  </section>
</template>

<script>
// import store from '@/store'
import { getDicts } from '@/api/system/dict'
import 'vxe-table/lib/style.css'
import { getCompanyByPlate, selectAccount, getAccountMoney } from '@/api/business/accountMoneyForMonthapi'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)

export default {
  name: 'Allform',
  data() {
    function getFullDate(targetDate) {
      var D, y, m, d
      if (targetDate) {
        D = new Date(targetDate)
        y = D.getFullYear()
        m = D.getMonth() + 1
        d = D.getDate()
      } else {
        y = fullYear
        m = month
        d = date
      }
      m = m > 9 ? m : '0' + m
      d = d > 9 ? d : '0' + d
      return y + '-' + m + '-' + d
    }
    var nowDate = new Date()
    var lastDate = new Date()
    lastDate.setMonth(nowDate.getMonth() + 1)
    // var fullYear = nowDate.getFullYear()
    // var month = nowDate.getMonth() + 1 // getMonth 方法返回 0-11，代表1-12月
    // var endOfMonth = new Date(fullYear, month, 1).getDate() // 获取本月最后一天
    var endDate = getFullDate(lastDate.setDate(1))// 当月最后一天
    var startDate = getFullDate(nowDate.setDate(1))// 当月第一天

    return {
      zhangType: [
        { value: 0, label: '银行网银' },
        { value: 2, label: '库存现金' },
        { value: 1, label: '承兑账户' }
      ],
      data: [],
      data1: [],
      form: {},
      banlist: [],
      toolbarButtons: [],
      tableLoading: false,
      companyList: [],
      accountList: [],
      total: 0,
      queryForm: {
        pageSize: 10,
        pageNum: 1,
        accid: undefined,
        companyid: null,
        shipTime: [startDate, endDate]
      },
      tableData: [],
      pickEndDate: lastDate
    }
  },
  activated(){
     if(this.$route.query.deptId){
        this.queryForm.companyid = this.$route.query.deptId+''
      }else{
        this.queryForm.companyid = null
      }
      this.getAccount();
      this.onQuery();
  },
  created(){
      if(this.$route.query.deptId){
        this.queryForm.companyid = this.$route.query.deptId+''
      }
       getDicts().then(res =>{
        for(var a = 0; a < res.length;a++){
          var item = res[a]
          if (item.name === 'plate'){
            this.banlist.push(item)
          }
        }
        console.log(this.banlist)
      })
      this.getAccount();
      this.onQuery();
  },
  methods: {
    handleDatePickOptions() {
      return {
        disabledDate: time => {
          // console.log(time)
          return time.getTime() > this.pickEndDate.getTime()
        },
        onPick({ maxDate, minDate }) {
          if (maxDate && maxDate.getTime() == minDate.getTime()) {
            // minDate = new Date(minDate.getTime())
            if (minDate.getTime() >= Date.now()) {
              minDate = new Date(minDate.setMonth(maxDate.getMonth() - 1))
            } else {
              maxDate = new Date(maxDate.setMonth(minDate.getMonth() + 1))
            }
          }
          console.log(maxDate, minDate)
        }
      }
    },
    todetail(item) {
      this.$router.push({ path: '/formdetail', query: { id: item.id, money: item.qichu, bankName: item.accountName, number: item.accountNumber, startDate: this.queryForm.startTime, endDate: this.queryForm.endTime }})
    },
    getAccount() {
      console.log(this.queryForm)
      // if (!this.queryForm.companyid){
      //   this.queryForm.accid = undefined
      // }
      selectAccount(this.queryForm.banid, this.queryForm.companyid).then(res => {
        this.accountList = res.list
      })
    },
    eventPage(e) {
      this.topage(e)
    },
    onQuery() {
      this.topage(1)
    },
    topage(pageNo) {
      this.tableLoading = true
      this.queryForm.pageNum = pageNo

      if (
        this.queryForm.shipTime != undefined &&
        this.queryForm.shipTime != null
      ) {
        this.queryForm.startTime = this.queryForm.shipTime[0]
        this.queryForm.endTime = this.queryForm.shipTime[1]
      } else {
        var [startDate, endDate] = this.getMonthDay()
        this.queryForm.startTime = startDate
        this.queryForm.endTime = endDate
      }
      getAccountMoney(
        this.queryForm.banid,
        this.queryForm.companyid,
        this.queryForm.accid,
        this.queryForm.pageSize,
        this.queryForm.pageNum,
        this.queryForm.startTime,
        this.queryForm.endTime,
        this.queryForm.discount
      ).then(res => {
        this.tableLoading = false
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.page
          this.total = res.total
          this.companyList = res.companylist
        }
        console.log(this.tableData)
      })
    },
    getMonthDay() {
      var nowDate = new Date()
      var fullYear = nowDate.getFullYear()
      var month = nowDate.getMonth() + 1 // getMonth 方法返回 0-11，代表1-12月
      var endOfMonth = new Date(fullYear, month, 1) // 获取本月最后一天
      return [this.getFullDate(nowDate.setDate(1)), this.getFullDate(endOfMonth)]
    },
    getFullDate(targetDate) {
      var D, y, m, d
      if (targetDate) {
        D = new Date(targetDate)
        y = D.getFullYear()
        m = D.getMonth() + 1
        d = D.getDate()
      } else {
        y = fullYear
        m = month
        d = date
      }
      m = m > 9 ? m : '0' + m
      d = d > 9 ? d : '0' + d
      return y + '-' + m + '-' + d
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publictypeVxe({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined) {
        v = cellValue
        for (var a = 0; a < this.zhangType.length; a++) {
          var item = this.zhangType[a]
          if (v == item.value) {
            return item.label
          }
        }
      }
      return v
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue.toFixed(2)
      }
      return v
    },
    handleRowSave(row, done, loading) {
      this.data1.splice(0, 0, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      this.data1.splice(index, 1, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data1.splice(index, 1)
      })
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
</style>
