<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      资金收款台账
    </div>
    <el-divider/>
    <div>
      <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
        <el-form-item label="公司:" >
          <el-select v-model="queryForm.companyid" clearable placeholder="请选择" @change="getAccount()">
            <el-option
              v-for="item in companyList"
              :key="item.deptId"
              :label="item.name"
              :value="item.deptId+''">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账户:">
          <el-select v-model="queryForm.accid" clearable placeholder="请选择">
            <el-option
              v-for="item in accountList"
              :key="item.id"
              :label="item.bankName"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="往来单位:">
          <el-input v-model="queryForm.comecompany"placeholder="请输入往来单位" clearable></el-input>
        </el-form-item>
        <el-form-item label="收款类型:">
          <div  style="position: relative;bottom: 6px;">
            <FcItem :list="fundsClassList" @clkItem="clkQuery" v-model="fundsQueryId" ></FcItem>
          </div>
        </el-form-item>
<!--        <el-form-item label="收款方式:">-->
<!--          <el-select v-model="queryForm.type" clearable placeholder="请选择">-->
<!--            <el-option-->
<!--              v-for="item in receiveTypeList "-->
<!--              :key="item.value"-->
<!--              :label="item.value"-->
<!--              :value="item.code">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->

      </el-form>
    </div>
    <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
      <!-- v-if="!propTime || propTime.length==0" -->
      <el-form-item label="收款日期:" >
        <el-date-picker
          clearable
          v-model="queryForm.shipTime"
          @change="onQuery"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="创建日期:" v-if="propTime && propTime.length">
        <el-date-picker
          clearable
          v-model="payCreateTime"
          @change="onQuery"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item> -->
      <el-form-item label="金额:">
        <div style="display: inline-flex">
          <el-input v-model="queryForm.moneyMin" type="number" placeholder="最小值"/>
          <span style="padding-left: 10px;padding-right: 10px">至</span>
          <el-input v-model="queryForm.moneyMax" type="number" placeholder="最大值"/>
        </div>
      </el-form-item>
      <el-form-item label="账户类型:">
        <el-select v-model="queryForm.costType" clearable placeholder="请选择">
          <el-option
            v-for="item in costType "
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注:">
        <el-input v-model="queryForm.remake" type="text" placeholder="备注"/>
      </el-form-item>
      <el-form-item>
        <el-button @click="onQuery" type="primary">查询</el-button>
      </el-form-item>
    </el-form>
    <vxe-toolbar
      export
      custom
      print
      ref="xToolbar"
      :buttons="toolbarButtons"
    >
    </vxe-toolbar>
    <div id="yewuzongbiao">
      <div style="text-align:right;padding:5px;">收款汇总：{{sumprice}}</div>
      <vxe-table
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        ref="xTable"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '收款台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
        @sort-change="sortChangeEvent3"
        :sort-config="{remote:true}"
      >
        <vxe-table-column field="occurDate" :formatter="publicFmtVxe" title="收款日期" align="center" width="100"/>
        <vxe-table-column field="receviceCompanyName" :formatter="publicFmtVxe" title="账户名称" align="center" width="150"/>
        <vxe-table-column field="receviceType" :formatter="costTypeFmtVxe" title="账户类型" align="center" width="100"/>
        <vxe-table-column field="costType"  title="收款类型" align="center" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.fundsClassName">{{ scope.row.fundsClassName }}</span>
           </template>
          </vxe-table-column>
        <vxe-table-column field="customerName" :formatter="publicFmtVxe" title="往来单位" align="center"/>
        <vxe-table-column field="paymentBank" :formatter="publicFmtVxe" title="付款银行" align="center"/>
        <vxe-table-column field="paymentAccount" :formatter="publicFmtVxe" title="付款账户" align="center"/>
        <vxe-table-column field="balance" :formatter="publicFmtnumber" title="收款金额" sortable align="center" width="100"/>
        <vxe-table-column field="remark" :formatter="publicFmtVxe" title="备注" align="center"/>
        <vxe-table-column field="username" :formatter="publicFmtVxe" title="制单人" align="center"/>
        <vxe-table-column field="createTime" :formatter="publicFmtVxe" title="创建时间" align="center"/>
        <vxe-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button @click="showshenpi(scope.row.processId)" type="text" size="small">查看</el-button>
            <el-button @click="showPic(scope.row)" :class="scope.row.picList?'cole9f':'cole8'" type="text" size="small">{{scope.row.picList?'查看财务截图':'暂无财务截图'}}</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      layout="sizes,prev, pager, next"
      :page-sizes="[10, 500, 1000, 3000]"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
      @size-change="handleSizeChange"
    />
    <ProgressComponent ref="processDrawerqingjia" style="z-index:9999" @refresh="refresh" @onCallback="handlerGlobalParams" />
      <el-dialog
      class="xuanxiang"
      title="查看财务截图"
      :visible.sync="showPicApply"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div v-for="(item,index) in picUrl" :key="index" style="text-align:center;">
        <image-rotate :src="item"></image-rotate>
      </div>
    </el-dialog>
  </section>
</template>

<script>
// import store from '@/store'
import { getDicts } from '@/api/system/dict'
import 'vxe-table/lib/style.css'
import {
  getCompanyByPlate,
  selectAccount,
  getReceiveWater
} from '@/api/business/accountMoneyForMonthapi'
import ProgressComponent from '@/components/workflow/process'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
import { getDictionaryList } from '@/api/system/baseInit'
import ImageRotate from '@/components/ImageRotate/index'
import currency from 'currency.js'
import FcItem from '@/views/components/fcitem/fcitem'
import dayjs from 'dayjs'
import { onlyTreeList } from '@/api/system/fundsClassification'

VXETable.use(VXETablePluginExportXLSX)

export default {
  name: 'paymentwater',
  components: {
    ProgressComponent,ImageRotate,FcItem
  },
  props: {
    // 时间、公司、类型
    propTime: {
      type: Array,
      default: () => []
    },
    propCompany: {
      type: String,
      default: ''
    },
    propType: {
      type: Array,
      default: () => []
    },
    propChild: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      picUrl: [],
      // payCreateTime: this.propTime ? this.propTime : [],
      fundsClassList: [],
      fundsQueryId: this.propType ? this.propType : [],
      childType: this.propChild ? this.propChild : [],
      showPicApply: false,
      costType: [
        { value: 5, label: '其他' },
        { value: 2, label: '网银汇款' },
        { value: 1, label: '现金' },
        { value: 3, label: '承兑汇票' },
        { value: 4, label: '支票' }
      ],
      data: [],
      data1: [],
      form: {},
      banlist: [],
      receiveTypeList:[],
      toolbarButtons: [],
      tableLoading: false,
      companyList: [],
      accountList: [],
      total: 0,
      queryForm: {
        pageSize: 10,
        pageNum: 1,
        companyid: this.propCompany ? this.propCompany : '',
        shipTime: this.propTime && this.propTime.length ? this.propTime : dayjs().date() > 3 ? [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
          : [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')],
      },
      tableData: [],
      sumprice: 0,


    }
  },
  mounted() {
    this.loadFundsClassList()
    getDicts().then(res => {
      for (var a = 0; a < res.length; a++) {
        var item = res[a]
        if (item.name === 'plate') {
          this.banlist.push(item)
        }
      }
      console.log(this.banlist)
    })
    getDictionaryList("fdbiz_payment_type,fdbiz_cost_type").then(res => {
      this.receiveTypeList = res.map['fdbiz_cost_type']
    })
    this.getAccount()
    this.onQuery()

    // console.log(`${store.getters.baseApi}api/wxDepartment/getAllDepartment`)
  },
  methods: {
    sortChangeEvent3({ sortList, column, property, order }) {
      console.log('sortList', sortList, column, property, order)
      this.queryForm.sort = property
      this.queryForm.order = order
      this.topage(1)
    },
    clkQuery() {
      this.childType = []
      this.topage(1)
    },
    loadFundsClassList() {
      if(this.fundsClassList.length == 0) {
        onlyTreeList('rec').then(res => {
          if (res.resultCode == '0' && res.data) {
            this.fundsClassList = res.data
            // console.log('fundsClassList', this.fundsClassList)
          }
        })
      }
    },
     showPic(item) {
      if (!item.picList) {
        this.$message.warning('暂无财务截图!')
        return
      }
      this.picUrl = []
      this.showPicApply = true
      this.picUrl = item.picList.split(',')
    },
     handleClose() {
      this.showPicApply = false
    },
    refresh() {
      this.topage(1)
    },
    handlerGlobalParams() {
      // 这里是右侧划出审批页面并且请求完数据，返回的全局参数，参考 本组件 132～136 行代码
      // console.log(**********)
    },
    showshenpi(id) {
      console.log(id)
      if (!id){
        this.$message({
          message: '该流程暂无审批详情',
          type: 'warning'
        });
        return;
      }
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.doInit()
    },
    getAccount() {
      selectAccount(this.queryForm.banid,this.queryForm.companyid).then(res => {
        this.accountList = res.list
        this.accountList.unshift({"bankName":"全部"})
      })
    },
    getFullDate(targetDate) {
      var D, y, m, d;
      if (targetDate) {
        D = new Date(targetDate);
        y = D.getFullYear();
        m = D.getMonth() + 1;
        d = D.getDate();
      } else {
        y = fullYear;
        m = month;
        d = date;
      }
      m = m > 9 ? m : '0' + m;
      d = d > 9 ? d : '0' + d;
      return y + '-' + m + '-' + d;
    },
    eventPage(e) {
      this.topage(e)
    },
    handleSizeChange(e) {
      this.queryForm.pageSize = e
      this.topage(1)
    },
    onQuery() {
      this.topage(1)
    },
    topage(pageNo) {
      this.tableLoading = true
      this.queryForm.pageNum = pageNo
      this.queryForm.fundsClassId = this.fundsQueryId && this.fundsQueryId.length > 0 ? this.fundsQueryId[this.fundsQueryId.length - 1] : null
      if (this.childType && this.childType.length > 0) {
        this.queryForm.isChild = this.childType.join(',')
      } else {
        this.queryForm.isChild = null
      }
      if (
        this.queryForm.shipTime != undefined &&
        this.queryForm.shipTime != null
      ) {
        this.queryForm.startTime = this.queryForm.shipTime[0]
        this.queryForm.endTime = this.queryForm.shipTime[1]
      }
      // else {
      //   var nowDate = new Date();
      //   var fullYear = nowDate.getFullYear();
      //   var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
      //   var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
      //   var endDate = this.getFullDate(nowDate.setDate(endOfMonth));//当月最后一天
      //   var startDate = this.getFullDate(nowDate.setDate(1));//当月第一天
      //   this.queryForm.startTime = startDate
      //   this.queryForm.endTime = endDate
      // }
      // if (this.payCreateTime && this.payCreateTime.length > 0) {
      //   this.queryForm.startCreateTime = this.payCreateTime[0]
      //   this.queryForm.endCreateTime = this.payCreateTime[1]
      // }
      if (this.propCompany) {
        this.queryForm.popDeptId = this.propCompany
      }
      getReceiveWater(this.queryForm).then(res => {
        this.tableLoading = false
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.page
          this.total = res.total
          this.companyList = res.companylist
          this.sumprice = res.sum && this.fmtPrice(res.sum) || 0
        }
        console.log(this.tableData)
      })
    },
    // 格式化金额
    fmtPrice(price){
      return currency(price,{ symbol:''}).format()
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    costTypeFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.costType.length; a++) {
          var itm = this.costType[a]
          if (itm.value === Number(v)) {
            return itm.label
          }
        }
      }
      return v
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        // v = cellValue.toFixed(2)
        v = this.fmtPrice(cellValue)
      }
      return v
    },
    receiveTypeFmt({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
        for (var a = 0; a < this.receiveTypeList.length; a++) {
          var itm = this.receiveTypeList[a]
          if (itm.code === Number(v)) {
            return itm.value
          }
        }
      }
      return v
    },
    handleRowSave(row, done, loading) {
      this.data1.splice(0, 0, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      this.data1.splice(index, 1, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data1.splice(index, 1)
      })
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}
.cole8{
  color :#D3D5DB;
}
.cole9f{
  color:#1890ff;
}
.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
</style>
