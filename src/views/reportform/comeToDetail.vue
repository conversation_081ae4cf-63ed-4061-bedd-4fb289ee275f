<template>
  <section style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      往来款明细表({{ companyName }}--{{ companyOtherName }})
    </div>
    <el-divider />
    <el-form :inline="true" :model="queryForm" style="display:inline-flex;" class="query-form demo-form-inline">
      <!--      <el-form-item label="日期:">-->
      <!--        <el-date-picker-->
      <!--          clearable-->
      <!--          v-model="queryForm.shipTime"-->
      <!--          type="daterange"-->
      <!--          range-separator="至"-->
      <!--          value-format="yyyy-MM-dd"-->
      <!--          start-placeholder="开始日期"-->
      <!--          end-placeholder="结束日期"-->
      <!--        ></el-date-picker>-->
      <!--      </el-form-item>-->
      <el-form-item label="银行:">
        <el-input v-model="queryForm.bankOne" type="text" placeholder="本公司账户开户行" />
      </el-form-item>
      <el-form-item label="往来银行:">
        <el-input v-model="queryForm.bankTwo" type="text" placeholder="往来公司账户开户行" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <div id="yewuzongbiao">
      <vxe-table
        ref="xTable"
        v-loading="tableLoading"
        class="el-table-info"
        :data="tableData"
        stripe
        size="small"
        border
        align="center"
        :print-config="{}"
        max-height="800"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '往来款汇总', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column  field="accountNameOwn"  :formatter="publicFmtVxe"  title="账户名"  align="center" width="180"/>
        <vxe-table-column  field="bankOwn" :formatter="publicFmtVxe"  title="开户行"  align="center"  width="180"/>
        <vxe-table-column  field="accountNumberOwn" :formatter="publicFmtVxe"  title="账号"  align="center"  width="180"/>
        <vxe-table-column  field="accountNameOther" :formatter="publicFmtVxe"  title="往来账户名"  align="center"  width="180"/>
        <vxe-table-column  field="bankOther" :formatter="publicFmtVxe"  title="往来开户行"  align="center"  width="180"/>
        <vxe-table-column  field="accountNumberOther" :formatter="publicFmtVxe" title="往来账号"  align="center"  width="180"/>
        <vxe-table-column  field="money" :formatter="publicFmtnumber" title="金额"  align="center"  width="100"/>
        <vxe-table-column  field="isPay" :formatter="payTypeFmt" title="类型"  align="center"  width="100"/>
        <vxe-table-column  field="remarks" :formatter="publicFmtVxe" title="备注"  align="center"  width="100"/>
        <vxe-table-column title="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showPic(scope.row)">查看截图</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryForm.pageSize"
      :current-page="queryForm.pageNum"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />

    <el-dialog
      class="xuanxiang"
      title="查看财务截图"
      :visible.sync="showPicApply"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <section v-for="(item,index) in picUrlArr" :key="index">
        <div class="img-ptr">
          <img ref="simgs" style="width: 100%;height: 100%;" :src="item">
          <div class="cont-center">
            <i class="el-icon-refresh-left" @click="rotateImg(index,'left')" />
            &nbsp;&nbsp;&nbsp;&nbsp;
            <i class="el-icon-refresh-right" @click="rotateImg(index,'right')" />
          </div>

        </div>

      </section>

    </el-dialog>
  </section>
</template>

<script>
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
import { getComeToDetail } from '../../api/system/comeToPaymentWater'
VXETable.use(VXETablePluginExportXLSX)

export default {
  name: 'ComeToTotal',
  data() {
    return {
      data: [],
      data1: [],
      form: {},
      banlist: [],
      toolbarButtons: [],
      tableLoading: false,
      companyList: [],
      accountList: [],
      total: 0,
      queryForm: {
        shipTime: null,
        pageSize: 10,
        pageNum: 1,
        deptId: null,
        companyOtherDeptId: null,
        startTime: null,
        endTime: null,
        bankOne: null,
        bankTwo: null
      },
      tableData: [],
      companyName: null,
      companyOtherName: null,
      payType: [
        { value: 0, label: '收款' },
        { value: 1, label: '付款' }
      ],
      showPicApply: false,
      picUrl: null
    }
  },
  computed: {
    picUrlArr() {
      if (this.picUrl) {
        return this.picUrl.split(',')
      }
      return []
    }
  },
  mounted() {
    if (this.$route.query.companyId) {
      this.queryForm.deptId = Number(this.$route.query.companyId)
    }
    if (this.$route.query.companyOtherDeptId) {
      this.queryForm.companyOtherDeptId = Number(this.$route.query.companyOtherDeptId)
    }
    if (this.$route.query.companyName) {
      this.companyName = this.$route.query.companyName
    }
    if (this.$route.query.companyOtherName) {
      this.companyOtherName = this.$route.query.companyOtherName
    }
    if(this.$route.query.startTime && this.$route.query.endTime){
      this.queryForm.startTime = this.$route.query.startTime
      this.queryForm.endTime = this.$route.query.endTime
    }
    if(this.$route.query.wlType){
      this.queryForm.wlType = this.$route.query.wlType
    }
    this.onQuery()
  },
  methods: {
    rotateImg(index, tye) {
      if (this.$refs.simgs[index].style.transform.indexOf('rotate') > -1) {
        const r = this.$refs.simgs[index].style.transform
        let r1 = r.substring(r.indexOf('(') + 1, r.indexOf('deg'))
        if (tye === 'left') {
          r1 = Number(r1) - 90
        } else {
          r1 = Number(r1) + 90
        }
        this.$refs.simgs[index].style.transform = 'rotate(' + r1 + 'deg)'
      } else {
        this.$refs.simgs[index].style.transform = 'rotate(' + (tye == 'left' ? '-90deg' : '90deg') + ')'
      }
    },
    handleClose() {
      this.showPicApply = false
    },
    showPic(item) {
      if (!item.picList) {
        this.$message.warning('该流水暂无财务截图!')
        return
      }
      this.picUrl = null
      this.showPicApply = true
      this.picUrl = item.picList
    },
    eventPage(e) {
      this.topage(e)
    },
    onQuery() {
      this.topage(1)
    },
    getFullDate(targetDate) {
      var D, y, m, d
      if (targetDate) {
        D = new Date(targetDate)
        y = D.getFullYear()
        m = D.getMonth() + 1
        d = D.getDate()
      } else {
        y = fullYear
        m = month
        d = date
      }
      m = m > 9 ? m : '0' + m
      d = d > 9 ? d : '0' + d
      return y + '-' + m + '-' + d
    },
    topage(pageNo) {
      this.tableLoading = true
      // if (
      //   this.queryForm.shipTime != undefined &&
      //   this.queryForm.shipTime != null
      // ) {
      //   this.queryForm.startTime = this.queryForm.shipTime[0]
      //   this.queryForm.endTime = this.queryForm.shipTime[1]
      // } else {
      //   var nowDate = new Date();
      //   var fullYear = nowDate.getFullYear();
      //   var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
      //   var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
      //   var endDate = this.getFullDate(nowDate.setDate(endOfMonth));//当月最后一天
      //   var startDate = this. getFullDate(nowDate.setDate(1));//当月第一天
      //   this.queryForm.startTime = startDate
      //   this.queryForm.endTime = endDate
      // }
      var query = {
        companyId: this.queryForm.deptId,
        companyIdOther: this.queryForm.companyOtherDeptId,
        bankOne: this.queryForm.bankOne,
        bankTwo: this.queryForm.bankTwo,
        startTime: this.queryForm.startTime,
        endTime: this.queryForm.endTime,
        pageSize: this.queryForm.pageSize,
        pageNum: this.queryForm.pageNum,
        wlType: this.queryForm.wlType || ''
      }
      getComeToDetail(query).then(res => {
        this.tableLoading = false
        if (res !== undefined && res.resultCode === '0') {
          this.tableData = res.list
          this.total = res.total
        }
      })
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtVxe({ row, column, cellValue, index }) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    payTypeFmt({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined) {
        v = cellValue
        for (var a = 0; a < this.payType.length; a++) {
          var item = this.payType[a]
          if (v == item.value) {
            return item.label
          }
        }
      }
      return v
    },
    publicFmtnumber({ row, column, cellValue, index }) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue.toFixed(2)
      }
      return v
    },
    handleRowSave(row, done, loading) {
      this.data1.splice(0, 0, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      this.data1.splice(index, 1, row)
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.data1.splice(index, 1)
      })
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.el-tie {
  line-height: 32px;
  text-align: right;
  font-size: 14px;
  vertical-align: middle;
  color: #606266;
  padding: 0 12px 0 0;
  font-weight: 700;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
.cont-center{
  text-align: center;
}
</style>
