import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css'

import Element from 'element-ui'
//
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

// 数据字典
import dict from './components/Dict'

// 权限指令
import checkPer from '@/utils/permission'
import permission from './components/Permission'
import './assets/styles/element-variables.scss'
// global css
import './assets/styles/index.scss'
// 代码高亮
import VueHighlightJS from 'vue-highlightjs'
import 'highlight.js/styles/atom-one-dark.css'

import dayjs from "dayjs"

import App from './App'
import store from './store'
import router from './router/routers'
import './utils/drag'

import './assets/icons' // icon
import './router/index' // permission control
import 'echarts-gl'

import VXETable from 'vxe-table'
import Avue from '@smallwei/avue'
import '@smallwei/avue/lib/index.css'
import service from './utils/request'
import Autocomplete from './components/Autocomplete'
import currency from 'currency.js'
import '@/assets/css/mytable-scrollbar.css'
Vue.prototype.currency = v => currency(v, { symbol: '￥' })

Vue.prototype.fmtMoney2 = value => Vue.prototype.currency(value).format()
Vue.prototype.fmtMoney = v => currency(v, { symbol: '' }).format()


window.axios = service

Vue.prototype.$XPrint = VXETable.print

Vue.prototype.dayjs = dayjs
Vue.use(Avue)
Vue.use(VXETable)
Vue.use(checkPer)
Vue.use(VueHighlightJS)
Vue.use(mavonEditor)
Vue.use(permission)
Vue.use(dict)
Vue.use(Element, {
  size: Cookies.get('size') || 'small' // set element-ui default size
})
Vue.component('MyInputAutocomplete', Autocomplete)
Vue.config.productionTip = false

window.round = function(num, n = 2) {
  return Math.round(num * Math.pow(10, n)) / Math.pow(10, n);
}

Vue.prototype.round = window.round

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
