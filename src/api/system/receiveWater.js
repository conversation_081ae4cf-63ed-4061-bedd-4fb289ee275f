import request from '@/utils/request'
const BASE_REQUEST = '/api/receiveWater'

export function add(data) {
  return request({
    url: 'api/receiveWater',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/receiveWater/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/receiveWater',
    method: 'put',
    data
  })
}

export function getReceiveWater(data) {
  return request({
    url: `${BASE_REQUEST}/getReceiveWater`,
    method: 'get',
    params: data
  })
}
export function getReceiveWaterListByAccountId(data) {
  return request({
    url: `${BASE_REQUEST}/getReceiveWaterListByAccountId`,
    method: 'get',
    params: data
  })
}

export function getReceiveWaterBusiness(data) {
  return request({
    url: `${BASE_REQUEST}/getReceiveWaterBusiness`,
    method: 'get',
    params: data
  })
}
export function isReceiveWaterExist(data) {
  return request({
    url: `${BASE_REQUEST}/isReceiveWaterExist`,
    method: 'post',
    data
  })
}
export function addReceiveWater(data) {
  return request({
    url: `${BASE_REQUEST}/addReceiveWater`,
    method: 'post',
    data
  })
}
export function updateAddReceiveWater(data) {
  return request({
    url: `${BASE_REQUEST}/updateAddReceiveWater`,
    method: 'post',
    data
  })
}
export function addReceiveWaterList(data) {
  return request({
    url: `${BASE_REQUEST}/addReceiveWaterList`,
    method: 'post',
    data
  })
}
export function addReceiveWaterSubList(data) {
  return request({
    url: `${BASE_REQUEST}/addReceiveWaterSubList`,
    method: 'post',
    data
  })
}
export function updateAddReceiveWaterSubs(data) {
  return request({
    url: `${BASE_REQUEST}/updateAddReceiveWaterSubs`,
    method: 'post',
    data
  })
}
export function getReceiveWaterSubByParentId(data) {
  return request({
    url: `${BASE_REQUEST}/getReceiveWaterSubByParentId`,
    method: 'get',
    params: data
  })
}

export function getReceive(data) {
  return request({
    url: `${BASE_REQUEST}/getReceive`,
    method: 'get',
    params: data
  })
}

export function updateReceiveStatus(receiveWaterId) {
  return request({
    url: `${BASE_REQUEST}/updateReceiveStatus`,
    method: 'get',
    params: {receiveWaterId:receiveWaterId}
  })
}

export function updateReceiveWater(verificationInfo,receiveWaterId,contractId) {
  return request({
    url: `${BASE_REQUEST}/updateReceiveWater`,
    method: 'get',
    params: {verificationInfo:verificationInfo,receiveWaterId:receiveWaterId,contractId:contractId}
  })
}

export function updateReceiveWaterStatus(receiveWaterId,applyStatus,isAdvance) {
  return request({
    url: `${BASE_REQUEST}/updateReceiveWaterStatus`,
    method: 'get',
    params: {receiveWaterId:receiveWaterId,applyStatus:applyStatus,isAdvance:isAdvance}
  })
}

export function getBusinessReceiveDetail(receiveWaterId) {
  return request({
    url: `${BASE_REQUEST}/getBusinessReceiveDetail`,
    method: 'get',
    params: {receiveWaterId:receiveWaterId}
  })
}

export function getReceiveWaterById(receiveWaterId) {
  return request({
    url: `${BASE_REQUEST}/getReceiveWaterById`,
    method: 'get',
    params: {receiveWaterId:receiveWaterId}
  })
}

export function getUnusedAcceptancesAll() {
  return request({
    url: `${BASE_REQUEST}/getUnusedAcceptancesAll`,
    method: 'get',
  })
}

export function checkAcceptance(numberAndMoneys) {
  return request({
    url: `${BASE_REQUEST}/checkAcceptance`,
    method: 'post',
    data:numberAndMoneys
  })
}

export function getUnusedAcceptancesAllByAccountId(accountId) {
  return request({
    url: `${BASE_REQUEST}/getUnusedAcceptancesAllByAccountId`,
    method: 'get',
    params: {accountId:accountId}
  })
}
export function splitTicket(ticketId, money) {
  return request({
    url: `${BASE_REQUEST}/splitTicket`,
    method: 'post',
    data: {
      ticketId,money
    }
  })
}

export function updateTicketById(ticketIds, processId) {
  return request({
    url: `${BASE_REQUEST}/updateTicketById`,
    method: 'post',
    data: {
      ticketIds,processId
    }
  })
}

export default { add, edit, del }
