import request from '@/utils/request'
const BASE_REQUEST = '/api/fundsClassification'
const BASE_REQUEST_PLATE = '/api/fundsClassificationPlate'
export function add(data) {
  return request({
    url: `${BASE_REQUEST}`,
    method: 'post',
    data
  })
}
export function addPlate(data) {
  return request({
    url: `${BASE_REQUEST_PLATE}`,
    method: 'post',
    data
  })
}
export function getLSPayTypeByDeptCode(deptCode) {
  return request({
    url: `${BASE_REQUEST}/getLSPayTypeByDeptCode`,
    params: {
      deptCode
    }
  })
}
export function removePlate(ids) {
  return request({
    url: `${BASE_REQUEST_PLATE}/`,
    method: 'delete',
    data: ids
  })
}

export function del(ids) {
  return request({
    url: `${BASE_REQUEST}/`,
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: `${BASE_REQUEST}`,
    method: 'put',
    data
  })
}
export function query(data) {
  return request({
    url: `${BASE_REQUEST}`,
    method: 'get',
    params:data
  })
}
export function treeList(model) {
  return request({
    url: `${BASE_REQUEST}/treeList`,
    params: {
      model
    }
  })
}
export function onlyTreeList(model) {
  return request({
    url: `${BASE_REQUEST}/onlyTreeList`,
    params: {
      model
    }
  })
}
export function fundsAnalysisDetail(deptId,startDate,endDate,funIds,flowIds) {
  return request({
    url: `${BASE_REQUEST}/fundsAnalysisDetail`,
    params: {
      deptId,
      startDate,endDate,funIds,flowIds
    }
  })
}
export function fundsAnalysis(deptId,startDate,endDate) {
  return request({
    url: `${BASE_REQUEST}/fundsAnalysis`,
    params: {
      deptId,
      startDate,endDate
    }
  })
}
export function cashFlow(deptId,startDate,endDate) {
  return request({
    url: `${BASE_REQUEST}/cashFlow`,
    params: {
      deptId,
      startDate,endDate
    }
  })
}
export function fundsAnalysisByDeptPlateAndDate(startDate,endDate) {
  return request({
    url: `${BASE_REQUEST}/fundsAnalysisByDeptPlateAndDate`,
    params: {
      startDate, endDate
    }
  })
}
export default { add, edit, del,treeList }
