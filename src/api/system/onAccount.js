import request from '../../utils/request'

const BASE_REQUEST = '/api/onAccount'

export function getOnAccountList(query) {
  return request({
    url: `${BASE_REQUEST}/getOnAccountList`,
    method: 'get',
    params: query
  })
}

export function getOnShipMonthList(query) {
  return request({
    url: `${BASE_REQUEST}/getOnShipMonthList`,
    method: 'get',
    params: query
  })
}
export function getOilStats(query) {
  return request({
    url: `${BASE_REQUEST}/getOilStats`,
    method: 'get',
    params: query
  })
}
export function oilWaterDetail(id) {
  return request({
    url: `${BASE_REQUEST}/oilWaterDetail`,
    method: 'get',
    params: id
  })
}
export function oilConsumeList(query) {
  return request({
    url: `${BASE_REQUEST}/oilConsumeList`,
    method: 'get',
    params: query
  })
}

export function oilAccountFlowList(query) {
  return request({
    url: `${BASE_REQUEST}/oilAccountFlowList`,
    method: 'get',
    params: query
  })
}

export function shipCostRecordDetail(id) {
  return request({
    url: `${BASE_REQUEST}/shipCostRecordDetail`,
    method: 'get',
    params: {id}
  })
}

export function getOnAccountListOtherCost(query) {
  return new Promise((resolve, reject) => {
    request({
      url: `${BASE_REQUEST}/getOnAccountListOtherCost`,
      method: 'get',
      params: query
    }).then(res => {

      for (const process of res.page.records) {
        const costs = JSON.parse(process.globalParam2)
        process.costs = costs
        // console.log(costs)
        // 取costs的costPrice的总和
        const sum = costs.reduce((total, cost) => {
          if (!cost.id) {
            return total
          }
          return total + parseFloat(cost.costPrice)
        }, 0)
        process.sumBlance = sum
      }

      resolve(res)
    }).catch(err => {
      reject(err)
    })
  });
}

export function getOnAccountListUpdateStowage(query) {
  return request({
      url: `${BASE_REQUEST}/getOnAccountListUpdateStowage`,
      method: 'get',
      params: query
    })
}

export function getShipLineSummary(query) {
  return request({
    url: `${BASE_REQUEST}/getShipLineSummary`,
    method: 'get',
    params: query
  })
}

export function updateOnAccountProcess(processId,status,reason,deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/updateOnAccountProcess`,
    method: 'get',
    params: {processId:processId,status:status,reason:reason,deptCode:deptCode}
  })
}

export function updateOnAccountOtherCostProcess(processId,nodeId,status,reason) {
  return request({
    url: `${BASE_REQUEST}/updateOnAccountOtherCostProcess`,
    method: 'get',
    params: {processId:processId,nodeId:nodeId,status:status,reason:reason}
  })
}
export function getShipLineById(id,deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/getShipLineById`,
    method: 'get',
    params: {id:id,deptCode:deptCode}
  })
}

export function taskGetNew(processId,deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/taskGet`,
    method: 'get',
    params: {processId: processId,deptCode:deptCode}
  })
}
export function getSpLogList(processId) {
  return request({
    url: `${BASE_REQUEST}/getSpLogList`,
    method: 'get',
    params: {processId: processId}
  })
}

export function getStowageDetil(shiplineid,isTaxIncluded,deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/getStowageDetil`,
    method: 'get',
    params: {
      shiplineid:shiplineid,
      isTaxIncluded: isTaxIncluded,
      deptCode:deptCode
    }
  })
}

export function getOnAccountCount() {
  return request({
    url: `${BASE_REQUEST}/getOnAccountCount`,
    method: 'get',
  })
}
export function getSecondList() {
  return request({
    url: `${BASE_REQUEST}/getSecondList`,
    method: 'get',
  })
}
export function getAccountAreaByShipLineId(shiplineid, HNHS) {
  return request({
    url: `${BASE_REQUEST}/getAccountAreaByShipLineId`,
    method: 'get',
    params: {
      shipLineId: shiplineid,
      deptCode: HNHS
    }
  })
}
export function addStowageByJsonArray(jsonArray) {
  return request({
    url: `${BASE_REQUEST}/addStowageByJsonArray`,
    method: 'post',
    data:jsonArray
  })
}
