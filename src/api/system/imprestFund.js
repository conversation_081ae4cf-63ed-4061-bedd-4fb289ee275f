import request from '@/utils/request'

const BASE_REQUEST = '/api/imprestFund'

const BASE_REQUEST_REOCRD = '/api/imprestFundReocrd'
const BASE_REQUEST_APPLY = '/api/imprestFundApply'
const BASE_REQUEST_BILL = '/api/imprestFundMonthBill'
const BASE_REQUEST_MONEY_MONTH = '/api/imprestFundAccountMoneyForMonth'
const BASE_REQUEST_CHANGE_BILL = '/api/imprestFundChangeBill'
export function fundList() {
  return request({
    url: `${BASE_REQUEST}/fundList`,
    method: 'get'
  })
}
export function fundAccountList({fundId,startMonth,endMonth}) {
  return request({
    url: `${BASE_REQUEST}/fundAccountList`,
    method: 'get',
    params: {fundId,startMonth,endMonth}
  })
}
export function fundAccountDetail({fundId,startMonth,endMonth}) {
  return request({
    url: `${BASE_REQUEST}/fundAccountDetail`,
    method: 'get',
    params: {fundId,startMonth,endMonth}
  })
}
export function applyDetail(id) {
  return request({
    url: `${BASE_REQUEST_APPLY}/applyDetail`,
    method: 'get',
    params: {id}
  })
}
export function fundDetail(fundId) {
  return request({
    url: `${BASE_REQUEST}/fundDetail`,
    method: 'get',
    params: {fundId}
  })
}
export function queryRecordSum({ fundId, state, consumptionType, monthRec,status }) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/queryRecordSum`,
    method: 'get',
    params: {
      fundId, state, consumptionType, monthRec, status
    }
  })
}
export function queryRecordSumByStartAndEnd({ fundId, state, start, end,status,isApplyBalance }) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/queryRecordSumByStartAndEnd`,
    method: 'get',
    params: {
      fundId, state, start, end, status,
      isApplyBalance: isApplyBalance || false
    }
  })
}
export function getMonthMoneyByFundIdAndMonth({ fundId, yearMonth  }) {
  return request({
    url: `${BASE_REQUEST_MONEY_MONTH}/getMonthMoneyByFundIdAndMonth`,
    method: 'get',
    params: {
      fundId, yearMonth

    }
  })
}

export function fundRecordList({ fundId, state, consumptionType, monthRec,status,start,end }) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/queryRecord`,
    method: 'get',
    params: {
      fundId, state, consumptionType, monthRec, status,start,end
    }
  })
}

export function fundRecordListPage({ fundId,startMonth,endMonth, state, consumptionType, monthRec,status,pageNum,pageSize }) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/queryRecordPage`,
    method: 'get',
    params: {
      fundId,startMonth,endMonth, state, consumptionType, monthRec, status, pageNum, pageSize
    }
  })
}

export function queryRecordAndWuLiuPage({ fundId,startMonth,endMonth, state, status,pageNum,pageSize }) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/queryRecordAndWuLiuPage`,
    method: 'get',
    params: {
      fundId,startMonth,endMonth, state, status, pageNum, pageSize
    }
  })
}

export function addRecord({ imprestFundId, consumptionDate, consumptionType, balance, state, isBill, remarks, attach, monthRec }) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/addRecord`,
    method: 'post',
    data: {
      imprestFundId,
      consumptionDate,
      consumptionType,
      balance,
      state,
      isBill, remarks, attach, monthRec
    }
  })
}
export function updateRecord({id, imprestFundId, consumptionDate, consumptionType, balance, state, isBill, remarks, attach, monthRec,param2 }) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/updateRecord`,
    method: 'post',
    data: {
      id,
      imprestFundId,
      consumptionDate,
      consumptionType,
      balance,
      state,
      param2,
      isBill, remarks, attach, monthRec
    }
  })
}
export function billAndDetailsById(id) {
  return request({
    url: `${BASE_REQUEST_BILL}/billAndDetailsById`,
    method: 'get',
    params: {
     id
    }
  })
}

export function addBill({processId, imprestFundId, amount, imprestFundApplyId, balance, companyId, companyName, expendDaily, expendHosp, expendBus,incomeBus,param1,param2,param3 }) {
  return request({
    url: `${BASE_REQUEST_BILL}/addBill`,
    method: 'post',
    data: {
      processId,
      imprestFundId,
      amount,
      imprestFundApplyId,
      balance,
      companyId,
      companyName, expendDaily, expendHosp, expendBus,incomeBus,param1,param2,param3
    }
  })
}

export function addRecordList(data) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/addRecordList`,
    method: 'post',
    data: data
  })
}

export function delRecord(id) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/delRecord`,
    method: 'get',
    params: { id }
  })
}

export function lastApply(fundId) {
  return request({
    url: `${BASE_REQUEST_APPLY}/lastApply`,
    method: 'get',
    params: { fundId: fundId }
  })
}

export function addApply({ processId, imprestFundId, companyId, companyName, applyBalance, applyAmount, applyCreateTime, accountAmount, totalExpenditure, lastApplyCreateTime,lastApplyBalance, lastApplyAmount, useRemarks, expendDaily, expendHosp, expendBus, incomeBus, param1, param2 }) {
  return request({
    url: `${BASE_REQUEST_APPLY}/addApply`,
    method: 'post',
    data: {
      processId,
      imprestFundId,
      companyId,
      companyName,
      applyBalance,
      applyAmount,
      applyCreateTime, accountAmount, totalExpenditure, lastApplyCreateTime,lastApplyBalance, lastApplyAmount, useRemarks, expendDaily, expendHosp, expendBus, incomeBus, param1, param2
    }
  })
}

export function queryShipCostByCompanyId(companyId,shipName,startShipTime,endShipTime) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/queryShipCostByCompanyId`,
    method: 'get',
    params: { companyId: companyId,shipName,startShipTime,endShipTime }
  })
}


export function updateShipLineShipCostIsDeductById(id) {
  return request({
    url: `${BASE_REQUEST_REOCRD}/updateShipLineShipCostIsDeductById`,
    method: 'get',
    params: { id: id }
  })
}
export function fundMonthStartAndEndMoney(fundId) {
  return request({
    url: `${BASE_REQUEST}/fundMonthStartAndEndMoney`,
    method: 'get',
    params: {fundId}
  })
}
export function addFundChangeBill({processId,
  imprestFundId,
  balance,
  oldBalance,
  amount,
  companyId,
  companyName,expendTotal, expendDaily, expendHosp, expendBus,incomeBus,recordInfo,oldRecordInfo,param2,param3}) {
  return request({
    url: `${BASE_REQUEST_CHANGE_BILL}/addFundChangeBill`,
    method: 'post',
    data: {
      param1:processId,
      imprestFundId,
      balance,
      oldBalance,
      amount,
      companyId,
      companyName,expendTotal, expendDaily, expendHosp, expendBus,incomeBus,recordInfo,oldRecordInfo,param2,param3
    }
  })
}
export function changeBillById(id) {
  return request({
    url: `${BASE_REQUEST_CHANGE_BILL}/changeBillById`,
    method: 'get',
    params: {id}
  })
}

export function changeRecordUnsubByFunId(fundId) {
  return request({
    url: `${BASE_REQUEST_CHANGE_BILL}/changeRecordUnsubByFunId`,
    method: 'get',
    params: {fundId}
  })
}
export function changeListByIds(ids) {
  return request({
    url: `${BASE_REQUEST_CHANGE_BILL}/changeListByIds`,
    method: 'get',
    params: {ids}
  })
}

export function changeListUpdateStatusByIds(ids,status) {
  return request({
    url: `${BASE_REQUEST_CHANGE_BILL}/changeListUpdateStatusByIds`,
    method: 'get',
    params: {ids,status}
  })
}

export function monthBillListByFundIdAndMonth({fundId,status,startMonth,endMonth}) {
  return request({
    url: `${BASE_REQUEST_BILL}/monthBillListByFundIdAndMonth`,
    method: 'get',
    params: {fundId,status,startMonth,endMonth}
  })
}
export function monthBillIdToProcessId(billId) {
  return request({
    url: `${BASE_REQUEST_BILL}/monthBillIdToProcessId`,
    method: 'get',
    params: {billId}
  })
}
