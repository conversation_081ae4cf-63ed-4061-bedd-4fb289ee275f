import request from '@/utils/request'
export function add(data) {
  return request({
    url: 'api/sysExternalCustomerPlate',
    method: 'post',
    data
  })
}

export function saveAndUpdateByCreateBy(data) {
  return request({
    url: 'api/sysExternalCustomerPlate/saveAndUpdateByCreateBy',
    method: 'post',
    data
  })
}

export function list() {
  return request({
    url: 'api/sysExternalCustomerPlate',
    method: 'get'
  })
}

export function del(ids) {
  return request({
    url: 'api/sysExternalCustomerPlate/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sysExternalCustomerPlate',
    method: 'put',
    data
  })
}

export default { add, edit, del, list }
