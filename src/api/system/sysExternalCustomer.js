import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/sysExternalCustomer',
    method: 'post',
    data
  })
}

export function createAndPlate(data) {
  return request({
    url: 'api/sysExternalCustomer/createAndPlate',
    method: 'post',
    data
  })
}

export function list(data) {
  return request({
    url: 'api/sysExternalCustomer',
    method: 'get',
    params: data
  })
}

export function qureyCustomerAllOrDictId(dictId, likename) {
  return request({
    url: 'api/sysExternalCustomer/qureyCustomerAllOrDictId',
    method: 'get',
    params: { likename, dictId }
  })
}

export function listAll(likename) {
  return request({
    url: 'api/sysExternalCustomer/list',
    method: 'get',
    params: { likename }
  })
}

export function customerListAll(likename) {
  return request({
    url: 'api/sysExternalCustomer/customerListAll',
    method: 'get',
    params: { likename }
  })
}

export function del(ids) {
  return request({
    url: 'api/sysExternalCustomer/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sysExternalCustomer',
    method: 'put',
    data
  })
}
// 根据版块查询客户
export function queryCustomerByDictId({ dictId, isCustomer, isSupplier }) {
  return request({
    url: 'api/sysExternalCustomer/queryCustomerByDictId',
    method: 'get',
    params: { dictId, isCustomer, isSupplier }
  })
}

// 根据公司id 查询版块后查询客户
export function queryCustomerByDeptId({ deptId, isCustomer, isSupplier }) {
  return request({
    url: 'api/sysExternalCustomer/queryCustomerByDeptId',
    method: 'get',
    params: { deptId, isCustomer, isSupplier }
  })
}

export function queryTree(id) {
  return request({
    url: 'api/sysExternalCustomer/queryTree',
    method: 'get',
    params: { id }
  })
}

export function detail(id) {
  return request({
    url: 'api/sysExternalCustomer/detail',
    method: 'get',
    params: { id }
  })
}
const BASE_CONTACT = 'api/sysExternalCustomerContact'
export function addContact(data) {
  return request({
    url: BASE_CONTACT,
    method: 'post',
    data
  })
}
export function addContactAndPlate(data) {
  return request({
    url: BASE_CONTACT + '/createAndPlate',
    method: 'post',
    data
  })
}
export function editContact(data) {
  return request({
    url: BASE_CONTACT,
    method: 'put',
    data
  })
}
export function editContactAndPlate(data) {
  return request({
    url: BASE_CONTACT + '/updateAndPlate',
    method: 'put',
    data
  })
}

export function contactList(id) {
  return request({
    url: BASE_CONTACT,
    method: 'get',
    params: { customerId: id }
  })
}

export function contactListAndAllPlateSort(id) {
  return request({
    url: BASE_CONTACT + '/listAndAllPlateSort',
    method: 'get',
    params: { customerId: id }
  })
}

export function externalAccountList({ spare1 }) {
  return request({
    url: '/api/sysExternalAccount',
    method: 'get',
    params: { spare1 }
  })
}
export default { add, edit, del, list, detail, addContact, editContact, queryCustomerByDeptId, queryCustomerByDictId, listAll, customerListAll }

export function listByCustomerIdAndDictId({ customerId, dictId, isCustomer, isSupplier }) {
  return request({
    url: '/api/sysExternalCustomerContact/listByCustomerIdAndDictId',
    method: 'get',
    params: { customerId, dictId, isCustomer, isSupplier }
  })
}
export function sysExternalCustomerContactPlateAdd(data) {
  return request({
    url: 'api/sysExternalCustomerContactPlate',
    method: 'post',
    data
  })
}

export function sysExternalCustomerContactPlateDel(ids) {
  return request({
    url: 'api/sysExternalCustomerContactPlate/',
    method: 'delete',
    data: ids
  })
}

export function sysExternalCustomerContactPlateAddAndUpdate(data) {
  return request({
    url: 'api/sysExternalCustomerContactPlate/createAndUpdate',
    method: 'post',
    data
  })
}

export function queryContactDictByCustomerId(customerId) {
  return request({
    url: 'api/sysExternalCustomerContactPlate/queryDictByCustomerId',
    method: 'get',
    params: { customerId }
  })
}

export function queryDictByCustomerId(customerId) {
  return request({
    url: 'api/sysExternalCustomerPlate/queryDictByCustomerId',
    method: 'get',
    params: { customerId }
  })
}
export function queryDictByCustomerIdAndCurrent(customerId) {
  return request({
    url: 'api/sysExternalCustomerPlate/queryDictByCustomerIdAndCurrent',
    method: 'get',
    params: { customerId }
  })
}
export function queryAccountDictByCustomerId(customerId) {
  return request({
    url: 'api/sysExternalAccountPlate/queryDictByCustomerId',
    method: 'get',
    params: { customerId }
  })
}
