import request from "../../utils/request";

const BASE_REQUEST = '/api/invoice'
const BASE_REQUEST_TYPE = '/api/invoiceType'

export function getList(data) {
  return request({
    url: `${BASE_REQUEST}/list`,
    method: 'post',
    data
  })
}
export function updateBilMonth(data) {
  return request({
    url: `${BASE_REQUEST}/updateBilMonth`,
    method: 'post',
    data
  })
}
export function getStatistical(data) {
  return request({
    url: `${BASE_REQUEST}/statistical`,
    method: 'post',
    data
  })
}
export function invoiceListBySales(data) {
  return request({
    url: `${BASE_REQUEST}/invoiceListBySales`,
    method: 'post',
    data
  })
}
export function invoiceListBySalesAccount(data) {
  return request({
    url: `${BASE_REQUEST}/invoiceListBySalesAccount`,
    method: 'post',
    data
  })
}

export function listBySelf(data) {
  return request({
    url: `${BASE_REQUEST}/listBySelf`,
    method: 'post',
    data
  })
}

export function invoiceTypes(model='type') {
  return request({
    url: `${BASE_REQUEST_TYPE}`,
    method: 'get',
    params: {
      model
    }
  })
}

export function updateBilMonthSlef(data) {
  return request({
    url: `${BASE_REQUEST}/updateBilMonthSlef`,
    method: 'post',
    data
  })
}

export function uploadSalesInvoice(data) {
  return request({
    url: `${BASE_REQUEST}/uploadSalesInvoice`,
    method: 'post',
    data
  })
}


export function uploadPurchaseInvoice(data) {
  return request({
    url: `${BASE_REQUEST}/uploadPurchaseInvoice`,
    method: 'post',
    data
  })
}

export function uploadReceiptInvoice(data) {
  return request({
    url: `${BASE_REQUEST}/uploadReceiptInvoice`,
    method: 'post',
    data
  })
}

export function delInvoiceDialog(data) {
  return request({
    url: `${BASE_REQUEST}/delInvoiceDialog`,
    method: 'post',
    data
  })
}
