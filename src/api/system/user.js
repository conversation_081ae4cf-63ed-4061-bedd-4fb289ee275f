import request from '@/utils/request'
import { encrypt } from '@/utils/rsaEncrypt'

export function add(data) {
  return request({
    url: 'api/users',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/users',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/users',
    method: 'put',
    data
  })
}

export function editUser(data) {
  return request({
    url: 'api/users/center',
    method: 'put',
    data
  })
}

export function updatePass(user) {
  const data = {
    oldPass: encrypt(user.oldPass),
    newPass: encrypt(user.newPass)
  }
  return request({
    url: 'api/users/updatePass/',
    method: 'post',
    data
  })
}

export function updateEmail(form) {
  const data = {
    password: encrypt(form.pass),
    email: form.email
  }
  return request({
    url: 'api/users/updateEmail/' + form.code,
    method: 'post',
    data
  })
}

export default { add, edit, del }

export function userList() {
  return request({
    url: 'api/users/userList',
    method: 'get'
  })
}

export function userBankNameListByName(name) {
  return request({
    url: 'api/users/userBankNameListByName',
    method: 'get',
    params: { name }
  })
}
export function userBankNumberListByName(name, bankName) {
  return request({
    url: 'api/users/userBankNumberListByName',
    method: 'get',
    params: { name, bankName }
  })
}
export function selectCompanyList() {
  return request({
    url: 'api/users/selectCompanyList',
    method: 'get'
  })
}

export function accModel() {
  return request({
    url: 'api/sysUser/userInfoAccModel',
    method: 'get',
  })
}


export function upAccModel(accModel) {
  return request({
    url: `api/sysUser/userInfoAccModel/${accModel}`,
    method: 'put'
  })
}
