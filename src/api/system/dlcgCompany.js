import request from '@/utils/request'
// import axios from "../jslib/http";
import qs from 'qs'
const BASE_REQUEST = '/api/dlcgCompany'

export function tableObj(query) {
  return request({
    url: `${BASE_REQUEST}/table`,
    method: 'get',
    data: query
  })
}
export function treeselect() {
  return request({
    url: `${BASE_REQUEST}/treeselect`,
    method: 'get'
  })
}
export function treeselectUser() {
  return request({
    url: `${BASE_REQUEST}/treeselect/user`,
    method: 'get'
  })
}

export function listObj(query) {
  return request({
    url: `${BASE_REQUEST}/list`,
    method: 'get',
    data: query
  })
}

export function getObj(deptId) {
  return request({
    url: BASE_REQUEST + '/' + deptId,
    method: 'get'
  })
}

export function addObj(data) {
  return request({
    url: `${BASE_REQUEST}`,
    method: 'post',
    data: data
  })
}

// export function updateObj(data) {
//   // return request({
//   //   url: `${BASE_REQUEST}`,
//   //   method: 'put',
//   //   data: data
//   // })
//
//   return axios
//     .put(`${BASE_REQUEST}`, qs.stringify(data),{
//       'headers':{
//         'Content-Type': 'application/x-www-form-urlencoded'
//       }
//     })
//     .then(res => {
//       if (res !== undefined) {
//         return Promise.resolve(res.data)
//       } else {
//         return Promise.resolve(res)
//       }
//     })
// }

