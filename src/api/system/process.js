import request from '@/utils/request'
import axios from 'axios'
const BASE_REQUEST = '/api/process'
import qs from 'qs'

export function getProcess(code) {
  return request({
    url: `${BASE_REQUEST}/start/${code}`,
    method: 'get'
  })
}

export function startProcess(code, data, processName = undefined) {
  const pstr = processName ? `?processName=${processName}` : ''
  return request({
    // headers: {
    //   'Content-Type': 'application/json'
    // },
    data,
    url: `${BASE_REQUEST}/start/${code}${pstr}`,
    method: 'post'
  })
}

export function taskGet(id) {
  return request({
    url: `${BASE_REQUEST}/task/${id}`,
    method: 'get'
  })
}

export function handleProcess(data) {
  return request({
    data,
    url: `${BASE_REQUEST}/task/process`,
    method: 'post'
  })
}
export function taskPoint() {
  return request({
    url: `${BASE_REQUEST}/task/point`,
    method: 'get'
  })
}

export function selectOptions() {
  return request({
    url: `${BASE_REQUEST}/select`,
    method: 'get'
  })
}

export function sendMessage(querydata) {
  return request({
    url: `${BASE_REQUEST}/sendMessage`,
    data: querydata
  })
}

export function getSpPerspn(processId) {
  return request({
    url: `${BASE_REQUEST}/getSpPerspn`,
    method: 'get',
    params: { processId: processId }
  })
}

export function getWuLiuSpPerspnListByProcessId(processId,deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/getWuLiuSpPerspnListByProcessId`,
    method: 'get',
    params: { processId: processId ,deptCode:deptCode}
  })
}

export function cancelApply(processId, reason) {
  return request({
    url: `${BASE_REQUEST}/cancelApply`,
    method: 'get',
    params: { processId: processId, reason: reason }
  })
}

export function processVal(id) {
  return request({
    url: `${BASE_REQUEST}/processVal/${id}`,
    method: 'get'
  })
}
export function reminder(processId) {
  return request({
    url: `${BASE_REQUEST}/reminder`,
    method: 'post',
    headers:{
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify({processId})
  })
}
export function getLsProcessList({ shipName,feeType,startDate,endDate,createByName,pageNum,pageSize,isLikeQuery }) {
  return request({
    url: `${BASE_REQUEST}/getLsProcessList`,
    method: 'get',
    params: { shipName: shipName,feeType:feeType,startDate:startDate,endDate:endDate,createByName:createByName,pageNum:pageNum,pageSize:pageSize,isLikeQuery }
  })
}
export function getLsShipPaySumList({shipName,feeType,startDate,endDate,}) {
  return request({
    url: `${BASE_REQUEST}/getLsShipPaySumList`,
    method: 'get',
    params: { shipName,feeType,startDate,endDate}
  })
}

export function getLsShipList() {
  return request({
    url: `${BASE_REQUEST}/getLsShipList`,
    method: 'get'
  })
}
