import request from '@/utils/request'
const REQUEST_BASE = 'api/ocr'

export function billByImgUrl(imgurl) {
  return request({
    url: `${REQUEST_BASE}/billByImgUrl?imgurl=${imgurl}`,
    method: 'get'
  })
}

export function billByImgBase64(base64) {
  return request({
    url: `${REQUEST_BASE}/billByImgBase64?base64=${base64}`,
    method: 'get'
  })
}

export function invoiceOcrByImgurl(imgurl) {
  return request({
    url: `${REQUEST_BASE}/invoiceOcrByImgurl?imgurl=${imgurl}`,
    method: 'get'
  })
}

export function recognizeOcrByImgurl(imgurl) {
  return request({
    url: `${REQUEST_BASE}/recognizeOcrByImgurl?imgurl=${imgurl}`,
    method: 'get'
  })
}
