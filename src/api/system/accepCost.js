import request from "../../utils/request";
import qs from 'qs'

const BASE_REQUEST = '/api/accepCost'

/** 垫资成本列表 */
export function processlist(data) {
  return request({
    url: `${BASE_REQUEST}/processlist`,
    method: 'get',
    params: data
  })
}
export function getList(data) {
  return request({
    url: `${BASE_REQUEST}/list`,
    method: 'get',
    params: data
  })
}
export function getProcessByAccepCost(data) {
  return request({
    url: `${BASE_REQUEST}/getProcessByAccepCost`,
    method: 'get',
    params: data
  })
}
export function listByIds(data) {
  return request({
    data,
    url: `${BASE_REQUEST}/listByIds`,
    method: 'post'
  })
}

// export function listByIds(query) {
//   return request({
//     url: `${BASE_REQUEST}/listByIds`,
//     method: 'get',
//     params: query
//   })
// }

// export function list(query) {
//   return request({
//     url: `${BASE_REQUEST}/list`,
//     method: 'get',
//     params: query
//   })
// }

// export function updateOnAccountProcess(processId,status,reason) {
//   return request({
//     url: `${BASE_REQUEST}/updateOnAccountProcess`,
//     method: 'get',
//     params: {processId:processId,status:status,reason:reason}
//   })
// }

// export function getShipLineById(id) {
//   return request({
//     url: `${BASE_REQUEST}/getShipLineById`,
//     method: 'get',
//     params: {id:id}
//   })
// }

// export function taskGetNew(processId) {
//   return request({
//     url: `${BASE_REQUEST}/taskGet`,
//     method: 'get',
//     params: {processId: processId}
//   })
// }

// export function getStowageDetil(shiplineid,isTaxIncluded) {
//   return request({
//     url: `${BASE_REQUEST}/getStowageDetil`,
//     method: 'get',
//     params: {
//       shiplineid:shiplineid,
//       isTaxIncluded:isTaxIncluded
//     }
//   })
// }

// export function getOnAccountCount() {
//   return request({
//     url: `${BASE_REQUEST}/getOnAccountCount`,
//     method: 'get',
//   })
// }
