import request from '@/utils/request'
const BASE_REQUEST = '/api/comeToPaymentWater'

export function add(data) {
  return request({
    url: 'api/comeToPaymentWater',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/comeToPaymentWater/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/comeToPaymentWater',
    method: 'put',
    data
  })
}

export function updateWater(processId,picList) {
  return request({
    url: `${BASE_REQUEST}/updateWater`,
    method: 'get',
    params: {processId: processId,picList:picList}
  })
}

export function updateWaterStatus(processId) {
  return request({
    url: `${BASE_REQUEST}/updateWaterStatus`,
    method: 'get',
    params: {processId: processId}
  })
}

export function getPayReceiveList(data) {
  return request({
    url: `${BASE_REQUEST}/getPayReceiveList`,
    method: 'get',
    params: data
  })
}

export function getComeToDetail(querydata) {
  return request({
    url: `${BASE_REQUEST}/getComeToDetail`,
    method: 'get',
    params: querydata
  })
}

export default { add, edit, del }
