import request from '@/utils/request'
const BASE_REQUEST = '/api/paymentWater'

export function add(data) {
  return request({
    url: 'api/paymentWater',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/paymentWater/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/paymentWater',
    method: 'put',
    data
  })
}

export function savePaymentWater(jsonData,processId,receiveCompany,costType,paymentType,occurDate,fundsClassId) {
  return request({
    url: `${BASE_REQUEST}/savePaymentWater`,
    method: 'get',
    params: {jsonData: jsonData,processId: processId,receiveCompany: receiveCompany,costType:costType,paymentType:paymentType,occurDate,fundsClassId}
  })
}

export function getReceiveProcessList(query) {
  return request({
    url: `${BASE_REQUEST}/getReceiveProcessList`,
    method: 'get',
    params: {
      code:query.code,
      pageSize:query.pageSize,
      pageNo:query.pageNo,
      keyword:query.keyword,
      content:query.content
    }
  })
}

export function getPaymentWaterList(data) {
  return request({
    url: `${BASE_REQUEST}/getPaymentWaterList`,
    method: 'get',
    params: data
  })
}

export function updatePaymentById(data) {
  return request({
    url: `${BASE_REQUEST}/updatePaymentById`,
    method: 'get',
    params: data
  })
}

export function getPaymentCount(deptId) {
  return request({
    url: `${BASE_REQUEST}/getPaymentCount`,
    method: 'get',
    params: {deptId: deptId}
  })
}
export function isRepeatPaymentByCompanyAndPrice(data) {
  return request({
    url: `${BASE_REQUEST}/isRepeatPaymentByCompanyAndPrice`,
    method: 'post',
    data
  })
}

export function addPaymentWaterOther(data) {
  return request({
    url: `${BASE_REQUEST}/addPaymentWaterOther`,
    method: 'post',
    data
  })
}

export function getTicketsByPaymentId(id) {
  return request({
    url: `${BASE_REQUEST}/getTicketsByPaymentId`,
    method: 'get',
    params: {id: id}
  })
}

export default { add, edit, del }
