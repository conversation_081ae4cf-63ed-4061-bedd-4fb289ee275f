import request from '@/utils/request'

const BASE_REQUEST = '/api/processVoucherRecord'

export function add(data) {
  return request({
    url: 'api/processVoucherRecord',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/processVoucherRecord/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/processVoucherRecord',
    method: 'put',
    data
  })
}

export function saveVoucher(voucherNum,processId) {
  return request({
    url: `${BASE_REQUEST}/saveVoucher`,
    method: 'get',
    params: {voucherNum:voucherNum,processId:processId}
  })
}

export function saveVoucherPayment(voucherNum,processId) {
  return request({
    url: `${BASE_REQUEST}/saveVoucherPayment`,
    method: 'get',
    params: {voucherNum:voucherNum,processId:processId}
  })
}

export function saveVoucherReceive(voucherNum,receiveWaterId) {
  return request({
    url: `${BASE_REQUEST}/saveVoucherPayment`,
    method: 'get',
    params: {voucherNum:voucherNum,receiveWaterId:receiveWaterId}
  })
}

export function saveVoucherOtherPayment(voucherNum,paymentId) {
  return request({
    url: `${BASE_REQUEST}/saveVoucherOtherPayment`,
    method: 'get',
    params: {voucherNum:voucherNum,paymentId:paymentId}
  })
}

export function saveVoucherComeToPayment(voucherNum,processId) {
  return request({
    url: `${BASE_REQUEST}/saveVoucherComeToPayment`,
    method: 'get',
    params: {voucherNum:voucherNum,processId:processId}
  })
}

export function getProcessVoucher(processId) {
  return request({
    url: `${BASE_REQUEST}/getProcessVoucher`,
    method: 'get',
    params: {processId: processId}
  })
}

export default { add, edit, del }
