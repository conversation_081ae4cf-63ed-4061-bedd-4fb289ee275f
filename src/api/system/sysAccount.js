import request from "../../utils/request";
const BASE_REQUEST = '/api/sysAccount'


export function selectbycode(code,type) {
  return request({
    url:`${BASE_REQUEST}/selectbycode`,
    method: 'get',
    params:{code:code,type:type}
  })
}

export function selectbyDeptId(id,type) {
  return request({
    url: `${BASE_REQUEST}/selectbyDeptId`,
    method: 'get',
    params: {id: id,type:type}
  })
}

export function selectbyId(id) {
  return request({
    url: `${BASE_REQUEST}/selectbyId`,
    method: 'get',
    params: {id: id}
  })
}

export function selectPersonalAccount(id) {
  return request({
    url:`${BASE_REQUEST}/selectPersonalAccount`,
    method: 'get',
    params:{id:id}
  })
}

export function getDicBeanbyDeptId(id,type) {
  return request({
    url: `${BASE_REQUEST}/getDicBeanbyDeptId`,
    method: 'get',
    params: {id: id,type:type}
  })
}

export function getBankNameByAccountName(accountName,type) {
  return request({
    url: `${BASE_REQUEST}/getBankNameByAccountName`,
    method: 'get',
    params: {accountName: accountName,type:type}
  })
}

export function getAccountNumByAccountId(id) {
  return request({
    url: `${BASE_REQUEST}/getAccountNumByAccountId`,
    method: 'get',
    params: {id: id}
  })
}

export function getAllDicBean(type) {
  return request({
    url: `${BASE_REQUEST}/getAllDicBean`,
    method: 'get',
    params: {type: type}
  })
}

export function getAllSysAccount() {
  return request({
    url: `${BASE_REQUEST}/getAllSysAccount`,
    method: 'get',
  })
}

export function getSysAccountInfo(deptId) {
  return request({
    url: `${BASE_REQUEST}/getSysAccountInfo`,
    method: 'get',
    params: {deptId: deptId}
  })
}

export function getAllSysAccountByDeptId(deptId) {
  return request({
    url: `${BASE_REQUEST}/getAllSysAccountByDeptId`,
    method: 'get',
    params: {deptId: deptId}
  })
}
