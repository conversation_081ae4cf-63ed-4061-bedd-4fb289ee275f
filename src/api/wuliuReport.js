import request from '@/utils/request'
import qs from 'qs'

export function getBusinessReceivables(querydata) {
  return request({
    url: `api/wuliureport/getBusinessReceivables`,
    method: 'post',
    data: qs.stringify(querydata)
  })
}
export function getGoodsDetailBusiness(querydata) {
  return request({
    url: `api/wuliureport/getGoodsDetailBusiness`,
    method: 'post',
    data: qs.stringify(querydata)
  })
}

export function getShipPayBusinessPayables(querydata) {
  return request({
    url: `api/wuliureport/getShipPayBusinessPayables`,
    method: 'post',
    data: qs.stringify(querydata)
  })
}

export function getShipCostBusinessPayables(querydata) {
  return request({
    url: `api/wuliureport/getShipCostBusinessPayables`,
    method: 'post',
    data: qs.stringify(querydata)
  })
}
