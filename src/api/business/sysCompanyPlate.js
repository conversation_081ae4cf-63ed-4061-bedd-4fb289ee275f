import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/sysCompanyPlate',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/sysCompanyPlate/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sysCompanyPlate',
    method: 'put',
    data
  })
}

export function dictIdByDeptId(deptId) {
  return request({
    url: 'api/sysCompanyPlate/dictIdByDeptId',
    method: 'get',
    params: { deptId }
  })
}

export default { add, edit, del, dictIdByDeptId }
