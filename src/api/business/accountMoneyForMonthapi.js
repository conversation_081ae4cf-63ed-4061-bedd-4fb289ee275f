import request from '@/utils/request'
const BASE_REQUEST = '/api/accountMoneyForMonth'

export function getCompanyByPlate(id) {
  return request({
    url: `${BASE_REQUEST}/getCompanyByPlate`,
    method: 'get',
    params: { id: id}
  })
}
export function selectAccount(banid,cid) {
  return request({
    url: `${BASE_REQUEST}/selectAccount`,
    method: 'get',
    params: { banid: banid,cid:cid}
  })
}
export function getAccountMoney(banid,companyid,accid,pageSize,pageNum,startTime,endTime,discount) {
  return request({
    url: `${BASE_REQUEST}/getAccountMoney`,
    method: 'get',
    params: {
      banid:banid,
      companyid:companyid,
      accid:accid,
      pageSize:pageSize ,
      pageNum:pageNum,
      startTime:startTime,
      endTime:endTime ,
      discount:discount
    }
  })
}
export function getMoneyForMonthStart(accid,startTime,endTime) {
  return request({
    url: `${BASE_REQUEST}/getMoneyForMonthStart`,
    method: 'get',
    params: {
      accid:accid,
      startTime: startTime,
      endTime: endTime
    }
  })
}
export function getAccountMoneyDetail(query) {
  return request({
    url: `${BASE_REQUEST}/getAccountMoneyDetail`,
    method: 'get',
    params: query
  })
}

export function getPaymentWater(query) {
  return request({
    url: `${BASE_REQUEST}/getPaymentWater`,
    method: 'get',
    params: query
  })
}
export function getReceiveWater(query) {
  return request({
    url: `${BASE_REQUEST}/getReceiveWater`,
    method: 'get',
    params: query
  })
}
