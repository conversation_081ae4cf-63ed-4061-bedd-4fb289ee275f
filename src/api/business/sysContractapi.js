import request from '@/utils/request'
const BASE_REQUEST = '/api/sysContract'

export function getContract(name,type,paymentid,receiveid) {
  return request({
    url: `${BASE_REQUEST}/getContract`,
    method: 'get',
    params: { name: name,type:type,paymentid:paymentid,receiveid:receiveid}
  })
}
export function saveContract(name,type,paymentid,receiveid,imageList) {
  return request({
    url: `${BASE_REQUEST}/saveContract`,
    method: 'get',
    params: { name: name,type:type,paymentid:paymentid,receiveid:receiveid,imageList:imageList}
  })
}
