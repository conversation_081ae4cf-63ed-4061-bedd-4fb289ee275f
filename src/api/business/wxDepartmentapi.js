import request from '@/utils/request'
const BASE_REQUEST = '/api/wxDepartment'

export function getuserxinxi() {
  return request({
    url: `${BASE_REQUEST}/getuserxinxi`,
    method: 'get',
    params: {}
  })
}
export function getprocessnum(companyid, process) {
  return request({
    url: `${BASE_REQUEST}/getprocessnum`,
    method: 'get',
    params: {
      companyid: companyid,
      process: process
    }
  })
}

export function getAllCompanyAccount() {
  return request({
    url: `${BASE_REQUEST}/getAllCompanyAccount`,
    method: 'get'
  })
}

export function getAllCompany(ids) {
  return request({
    url: `${BASE_REQUEST}/getAllCompany`,
    method: 'get',
    params: { ids: ids }
  })
}
export function getAllCompanyDepart() {
  return request({
    url: `${BASE_REQUEST}/getAllCompanyDepart`,
    method: 'get'
  })
}
export function getAllDepartment() {
  return request({
    url: `${BASE_REQUEST}/getAllDepartment`,
    method: 'get'
  })
}

export function getDeptInfo(params) {
  return request({
    url: `${BASE_REQUEST}/getDeptInfo`,
    method: 'get',
    params: params
  })
}

export function getPaymentNum(deptId) {
  return request({
    url: `${BASE_REQUEST}/getPaymentNum`,
    method: 'get',
    params: {
      deptId: deptId,
    }
  })
}

export function getAllPlateDept(  ) {
  return request({
    url: `${BASE_REQUEST}/getAllPlateDept`,
    method: 'get',
  })
}

