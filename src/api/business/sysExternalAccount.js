import request from '@/utils/request'
const BASE_REQUEST = '/api/sysExternalAccount'

export function add(data) {
  return request({
    url: 'api/sysExternalAccount',
    method: 'post',
    data
  })
}

export function addAndPlate(data) {
  return request({
    url: 'api/sysExternalAccount/createAndPlate',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/sysExternalAccount/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sysExternalAccount',
    method: 'put',
    data
  })
}

export function editAndPlate(data) {
  return request({
    url: 'api/sysExternalAccount/updateAndPlate',
    method: 'put',
    data
  })
}

export default { add, edit, del }

export function getAllBank(id) {
  return request({
    url: `${BASE_REQUEST}/getAllBank`,
    method: 'get',
    params: { id: id }
  })
}
export function getAllBankNum(id, bankid) {
  return request({
    url: `${BASE_REQUEST}/getAllBankNum`,
    method: 'get',
    params: { id: id, bankid: bankid }
  })
}
export function getExternalAccount(ids) {
  return request({
    url: `${BASE_REQUEST}/getExternalAccount`,
    method: 'get',
    params: { ids: ids }
  })
}

export function queryBankNameByCustomerIdAndDictId({
  customerId, bankName, dictId, isCustomer, isSupplier
}) {
  return request({
    url: `${BASE_REQUEST}/queryBankNameByCustomerIdAndDictId`,
    method: 'get',
    params: {
      customerId, bankName, dictId, isCustomer, isSupplier
    }
  })
}

export function sysExternalAccountPlateAdd({ accountId, dictId, isCustomer, isSupplier }) {
  return request({
    url: `api/sysExternalAccountPlate`,
    method: 'post',
    data: { accountId, dictId, isCustomer, isSupplier }
  })
}

export function sysExternalAccountPlateDel(ids) {
  return request({
    url: 'api/sysExternalAccountPlate/',
    method: 'delete',
    data: ids
  })
}

export function sysExternalAccountPlateAddAndUpdate({ accountId, dictId, isCustomer, isSupplier }) {
  return request({
    url: `api/sysExternalAccountPlate/createAndUpdate`,
    method: 'post',
    data: { accountId, dictId, isCustomer, isSupplier }
  })
}

export function getExternalAccountByCustomerIdAndBankAccount({ customerId, bankAccount, bankName }) {
  return request({
    url: `api/sysExternalAccount/getExternalAccountByCustomerIdAndBankAccount`,
    method: 'get',
    params: { customerId, bankAccount, bankName }
  })
}

export function savetExternalAccountByCustomerNameAndBankAccount({ name, bankAccount, bankName }) {
  return request({
    url: `api/sysExternalAccount/savetExternalAccountByCustomerNameAndBankAccount`,
    method: 'get',
    params: { name, bankAccount, bankName }
  })
}
