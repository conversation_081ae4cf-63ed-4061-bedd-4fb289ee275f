import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/contract',
    method: 'post',
    data
  })
}

export function statuslog(params) {
  return request({
    url: 'api/contract/statuslog',
    method: 'get',
    params
  })
}

export function getContractByIds(params) {
  return request({
    url: 'api/contract/getContractByIds',
    method: 'get',
    params
  })
}

export function del(ids) {
  return request({
    url: 'api/contract/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/contract',
    method: 'put',
    data
  })
}

export function shipsContractData() {
  return request({
    url: 'api/contract/shipsContractData',
    method: 'get'
  })
}

export function saveShipContract(data) {
  return request({
    url: 'api/contract/saveShipContract',
    method: 'post',
    data
  })
}
export function invalidContract(data) {
  return request({
    url: 'api/contract/invalid',
    method: 'post',
    data
  })
}

export default { add, edit, del, statuslog, getContractByIds, invalidContract }
