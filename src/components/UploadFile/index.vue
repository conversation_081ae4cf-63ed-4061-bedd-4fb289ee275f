<template>
  <el-upload
    ref="uploadFile"
    :action="action"
    :headers="headers"
    :multiple="multiple"
    :name="name"
    :show-file-list="showFileList"
    :drag="drag"
    :accept="accept"
    :list-type="listType"
    :auto-upload="autoUpload"
    :disabled="disabled"
    :limit="limit"
    :file-list="fileList"

    :on-preview="previewHandle"
    :on-remove="removeHandle"
    :before-remove="beforeRemoveHandle"
    :on-success="successHandle"
    :on-error="errorHandle"
    :on-progress="progressHandle"
    :before-upload="beforeUploadHandle"
    :on-exceed="exceedHandle"
  >
    <!-- 触发文件选择框的内容 -->
    <slot name="trigger"></slot>
    <!-- 提示说明文字 -->
    <slot name="tip"></slot>
    <!-- 预览 -->
    <el-dialog :visible.sync="previewShow" append-to-body>
      <img width="100%" :src="previewUrl" alt="">
    </el-dialog>
  </el-upload>
</template>

<script>
  import {getToken} from '@/utils/auth'
  import {mapGetters} from 'vuex'
  import {downloadFileByUrl} from '@/utils/index'
  export default {
    name: 'UploadFile',
    props: {
      uploadApi: String,
      /** 绑定值，格式为 /file/xx/xx.jpg 多个文件逗号隔开 */
      value: {
        type: String,
        default: ''
      },
      /** 是否支持多选文件 */
      multiple: {
        type: Boolean,
        required: false,
        default: true
      },
      /** 上传时附带的额外参数 */
      data: {
        type: Object,
        required: false
      },
      /** 上传的文件字段名 */
      name: {
        type: String,
        required: false
      },
      /** 是否显示已上传文件列表 */
      showFileList: {
        type: Boolean,
        required: false,
        default: true
      },
      /** 是否启用拖拽上传 */
      drag: {
        type: Boolean,
        required: false,
        default: false
      },
      /** 接受上传的文件类型 */
      accept: {
        type: String,
        required: false
      },
      /** 文件列表的类型 可选值：text/picture/picture-card */
      listType: {
        type: String,
        required: false
      },
      /** 是否在选取文件后立即进行上传 */
      autoUpload: {
        type: Boolean,
        required: false,
        default: true
      },
      /** 是否禁用 */
      disabled: {
        type: Boolean,
        required: false,
        default: false
      },
      /** 最大允许上传个数 */
      limit: {
        type: Number,
        required: false,
        default: 20
      },
      /** 文件大小限制：单位MB */
      sizeLimit: {
        type: Number,
        required: false,
        default: 100
      },
      /** 是否禁用删除: 默认不禁用 */
      delEnable: {
        type: Boolean,
        required: false,
        default: true
      }
    },
    data() {
      return {
        headers: {
          'Authorization': getToken()
        },
        temps: [],
        files: [],

        previewShow: false,
        previewUrl: '',

        ts: []
      }
    },
    computed: {
      ...mapGetters([
        'baseApi'
      ]),
      action() {
        return this.baseApi + this.uploadApi
      },
      fileList() {
        this.temps.splice(0,this.temps.length)
        if (this.value){
          this.value.split(',').forEach(item => {
            this.temps.push(item)
          })
        }
        if (this.temps.length > 0){
          this.files.filter(item => !this.temps.includes(item.response.link)).forEach(item => {
            this.files.splice(this.files.indexOf(item),1)
          })
          let links = this.files.map(item => {return item.response.link})
          this.temps.forEach(item => {
            if (!links.includes(item)){
              this.files.push({
                name: item.substring(item.lastIndexOf('/') + 1,item.length),
                url: this.baseApi + item,
                using: true,
                response: {
                  link: item
                }
              })
            }
          })
        }else{
          this.files.splice(0,this.files.length)
        }
        return this.files
      }
    },
    methods: {
      /** 点击文件列表中已上传的文件时的钩子 */
      previewHandle(){
        let link = arguments[0].response.link
        let s_1 = link.lastIndexOf('/') + 1
        let s_2 = link.lastIndexOf('.')
        let ext = link.substring(s_2 + 1,link.length)
        let fileName = link.substring(s_1,s_2)
        if (['png', 'jpg', 'jpeg', 'gif'].indexOf(ext.toLowerCase()) === -1){
          // 非图片类型，下载
          downloadFileByUrl(this.baseApi + link,fileName,ext)
        }else{
          this.previewShow = true
          this.previewUrl = this.baseApi + link
        }
      },
      /** 删除文件之前的钩子，参数为上传的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止删除。 */
      beforeRemoveHandle(file, fileList) {
        return this.delEnable
      },
      /** 文件列表移除文件时的钩子 */
      removeHandle(file, fileList) {
        this.temps.splice(this.temps.findIndex(item => item === file.response.link),1)
        this.files.splice(this.files.findIndex(item => item.name === file.name),1)
        this.$emit('input',this.temps.join(','))
      },
      /** 文件上传成功时的钩子 */
      successHandle(response, file, fileList) {
        console.log(response)
        if (!!response.link){
          this.ts.push(response.link)
          if (fileList.every(item => item.status === 'success')){
            let t = this.value
            fileList.filter(item => !item.using).forEach(item => {
              let u = item.response.link || ''
              if (!!t){
                t += ',' + u
              }else{
                t = u
              }
              item.url = this.baseApi + item.response.link
              item.using = true
              this.files.push(item)
            })
            this.ts.splice(0,this.ts.length)
            this.$emit('input', t)
          }
        }
      },
      /** 文件上传失败时的钩子 */
      errorHandle(e, file, fileList){
        const msg = JSON.parse(e.message)
        this.$notify({
          title: msg.message,
          type: 'error',
          duration: 2500
        })
      },
      /** 文件上传时的钩子 */
      progressHandle(event, file, fileList){

      },
      /** 上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。 */
      beforeUploadHandle(file) {
        const isLtM = file.size / 1024 / 1024 < this.sizeLimit
        if (!isLtM) {
          this.$message.error('上传文件大小不能超过 ' + this.sizeLimit + 'MB!')
        }
        return isLtM
      },
      /** 文件超出个数限制时的钩子 */
      exceedHandle(files, fileList){
        this.$message.warning(`文件个数不能超出${this.limit}个`)
      }
    }
  }
</script>

<style scoped>

</style>

<style>

</style>
