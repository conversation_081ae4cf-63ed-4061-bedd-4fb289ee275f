<template>
  <!--  这是一个用于输入信息时弹出平铺选择面板组件，包含常用、历史和输入查询结果的展示  -->
  <el-popover
    v-model="visibleAdd"
    placement="right"
    :width="componentsWidth"
    trigger="click"
    @show="showAdd"
    @hide="hideAdd"
  >
    <div>
      <div class="kh_title_lr">
        <div class="title_left_name">选择{{ modeName }}</div>
        <div class="title_right_clear" @click="clearnData">清空</div>
      </div>

           <div v-if="unQueryName">
             <div class="nodata-imgbg">
               <!-- noimg -->
               <!-- <img class="nodata-img" src="../../assets/img/nodata.png"/> -->
             </div>

             <div class="one-text">
               未查询到关于"{{showName}}"的数据！点击下方"确定"，添加此数据。
             </div>
             <div class="two-text">
               快捷新增数据
             </div>
             <div class="add-info-txt grid2" @mousedown.stop="cancelDown">
               <div>新增{{modeName}}：</div>
               <div><span class="kh-name">{{showName}}</span></div>
               <div v-show="accountType && accountType==2">开户行：</div>
               <div v-show="accountType && accountType==2"><el-input class="wd20" v-model="priBankName" placeholder="请输入开户行"></el-input></div>
               <div v-show="accountType && accountType==2">账号：</div>
               <div v-show="accountType && accountType==2"><el-input class="wd20" v-model="priBankAccount" placeholder="请输入银行账号"></el-input></div>

             </div>
             <div class="add-info-btnbg" @mousedown.stop="cancelDown">
               <el-button size="small" :loading="reqing" @click="adddata" type="primary">确认</el-button>
             </div>
                            <div class="add-info-txt2">您可以通过点击上方"确认"按钮直接新增"{{showName}}"到系统{{modeName}}中</div>
           </div>

      <section>
        <div v-if="querylist!=null&&querylist.length>0">
          <div style="color: #46A0FC;">
            查询结果：
          </div>
          <div
            class="kh_content"
            style="border-bottom: 1px solid #bcbec252;margin-bottom: 10px;padding-bottom: 10px"
            @mousedown.stop="cancelDown"
          >
            <el-tag
              v-for="(item,index) in querylist"
              :key="index"
              class="kh_item1"
              :type="valObj[keyStr]==item[keyStr]?'success':'info'"
              @click="clickTag(item[keyStr],0)"
            >
              {{ fmtVarStr(item,querylist) }}
            </el-tag>
          </div>
        </div>

        <div v-if="show" style="color: #46A0FC;">
          常用{{ modeName }}：
        </div>
        <div v-else-if="showyewu" style="color: #46A0FC;">
          常用{{ modeName }}：
        </div>
        <div
          class="kh_content"
          style="border-bottom: 1px solid #bcbec252;margin-bottom: 10px;padding-bottom: 10px"
          @mousedown.stop="cancelDown"
        >
          <el-tag
            v-for="(item,index) in commonlist"
            v-if="getValueStr(item) == '--'? false: true "
            ref="divtag"
            :key="index"
            class="kh_item1"
            :type="valObj[keyStr]==item?'success':'info'"
            @click="clickTag(item,0)"
          >
            {{ getValueStr(item,1,commonlist) }}
            <!-- {{item}} -->
          </el-tag>
        </div>

        <div style="color: #6AC144;">
          历史记录：
        </div>
        <div class="kh_content" :style="showName && !unQueryName && !haveData?'border-bottom: 1px solid #bcbec252;margin-bottom: 10px;padding-bottom: 10px':''" @mousedown.stop="cancelDown">
          <el-tag
            v-for="(item,index) in recordlist"
            :key="index"
            class="kh_item1"
            :type="valObj[keyStr]==item?'success':'info'"
            @click="clickTag(item,0)"
          >
            {{ getValueStr(item,0,recordlist,true) }}
          </el-tag>
          <div
            v-if="recordlist==null||recordlist.length==0"
            class="zanwujilu"
          >
            暂无记录
          </div>
        </div>

        <!--        <div v-if="showName && !unQueryName && !haveData" @mousedown.stop="cancelDown">-->
        <!--          <div class="nodata-imgbg">-->
        <!--            <img class="nodata-img" src="../../assets/img/nodata.png"/>-->
        <!--          </div>-->

        <!--          <div class="one-text">-->
        <!--            未查询到"{{showName}}"！点击下方"确定"，添加此数据。-->
        <!--          </div>-->
        <!--          <div class="two-text">-->
        <!--            快捷新增数据-->
        <!--          </div>-->
        <!--          <div class="add-info-txt">-->
        <!--            新增{{modeName}}：<span class="kh-name">{{showName}}</span>-->
        <!--          </div>-->
        <!--          <div class="add-info-btnbg">-->
        <!--            <el-button size="small" :loading="reqing" @click="adddata" type="primary">确认</el-button>-->
        <!--          </div>-->
        <!--          &lt;!&ndash;                <div class="add-info-txt2">您可以通过点击上方"确认"按钮直接新增"{{showName}}"到系统{{modeName}}中</div>&ndash;&gt;-->
        <!--        </div>-->
      </section>

    </div>
    <el-input slot="reference" v-model="showName" :placeholder="'请录入'+modeName+'名称'" @input="inputShowName" @clear="clearInput" />
  </el-popover>
</template>

<script>

import { commonreq } from '../../api/common/commonreq'
import { getCommonAndRecordByKey, saveRecord } from '../../api/system/baseInit'
import BankAccount from '@/components/ExternalCustomerPlate/account.vue';

export default {
  props: {
    yewu: false,
    isAutoSel: true,
    allData: Array, // 全部对象列表
    modeName: String,
    tablerow: Object,
    keyStr: String, // 数据对象中的key对应的字符
    valStr: String, // 数据对象中展示文本对应的字符
    dataKey: String, // 后台常用数据、用户记录数据储存对应的key
    isYewu: false,
    accountType: [String, Number],
    deptCode: String,
    addUrl: String, // 新增数据接口地址，上传参数{name:***}，返回结果格式{id:***}。需要返回新增数据的id字符串
    addUrlMethod: String, // 新增数据接口请求方式
    defName: String, // 默认值
    defId:[String,Number],
    componentsWidth: {
      type: String,
      required: false,
      default: '400'
    }
  },
  data() {
    return {
      visibleAdd: false, // 是否显示弹出选择面板
      showName: this.defName, // 输入框中展示选中的名称
      priBankName: '',
      priBankAccount: '',
      valObj: {}, // 用户最终选择的对象
      commonlist: [], // 常用数据列表
      recordlist: [], // 用户使用记录列表
      querylist: [], // 用户输入后查询列表
      tempShow: null,
      unQueryName: false,
      reqing: false,
      show: false,
      showyewu: false,
      haveData: false
    }
  },
  watch: {
    defName: {
      handler(val) {
        console.log('def name',val)
        if (val ) {
          this.showName = val
        }else{
          this.showName = ''
        }
      }
    },
    defId:{
      handler(val){
        if(val){
          this.setObjById(val)
        }
      },
      immediate: true
    },
    allData: function (val) {
          this.allMap={}
        if(val && val.length){
          val.forEach(item=>{
            this.allMap[item[this.keyStr]]=item
          })
        }
    },
    deptCode: function (val) {
      getCommonAndRecordByKey(this.dataKey,this.deptCode).then(res => {
        if (res != undefined) {
          this.commonlist = res.commonlist
          this.recordlist = res.recordlist && res.recordlist.length && res.recordlist.filter(item => item)
        }
      })
    }
    },
  created() {
    if (this.yewu) {
      setTimeout(() => {
        this.visibleAdd = true
      }, 10)
    }
    if(this.allData && this.allData.length){
      this.allMap={}
      this.allData.forEach(item=>{
        this.allMap[item[this.keyStr]]=item
      })
    }
  },
  mounted() {
    console.log(this.isYewu)
    getCommonAndRecordByKey(this.dataKey,this.deptCode).then(res => {
      if (res != undefined) {
        this.commonlist = res.commonlist
        this.recordlist = res.recordlist && res.recordlist.length && res.recordlist.filter(item => item)
      }
    })
  },
  methods: {
    cancelDown(e) {
      // console.log(e)
    },
    clearInput() {
      this.$emit('clearInput', null)
    },
    adddata() {
      if (this.addUrl == undefined || this.addUrl.length <= 0) {
        this.$message({
          message: '当前面板未设置新增接口请设置新增接口！',
          type: 'warning'
        })
        return
      }
      this.reqing = true
      const fromdata = {
        name: this.showName
      }
      if(this.priBankAccount){
        fromdata.bankAccount = this.priBankAccount
        fromdata.bankName = this.priBankName
      }
      if(this.accountType){
        fromdata.accountType = this.accountType
      }
      if(this.deptCode){
        fromdata.deptCode = this.deptCode
      }
      commonreq(fromdata, this.addUrl,this.addUrlMethod?this.addUrlMethod:'get').then(res => {
        if (res !== undefined && res.id) {
          var obj = {}
          obj[this.keyStr] = res.id
          if (this.valStr) {
            obj[this.valStr.split('+')[0]] = this.showName
          }
          if(this.priBankAccount){
            obj.bankAccount = this.priBankAccount
            obj.bankName = this.priBankName
          }
          this.allData.push(obj)
          this.allMap[res.id]=obj
          this.clickTag(res.id, 0)
          this.reqing = false
          this.unQueryName=false
        }else{
          this.$message.error('新增失败,请稍后再试')
          this.reqing = false
        }
      }).catch(err => {
        this.$message.error('新增失败,请稍后再试')
        this.reqing = false
      })
    },
    showAddDataView() {
      if (this.addUrl == undefined || this.addUrl.length <= 0) {
        console.log('当前面板未设置新增接口！不在显示快捷新增界面')
        return
      }

      this.unQueryName = true
    },
    clean() {
      this.valObj = {}
      this.showName = ''
    },
    clearnData() {
      this.clean()
      this.clickTag(this.valObj, 1)
    },
    getValueStr(keyVal, inde, list = [], his = false) {
      if (!this.allMap || Object.keys(this.allMap).length == 0) {
        this.allMap = {}
        for (var i = 0; i < this.allData.length; i++) {
          var obj = this.allData[i]
          var key = obj[this.keyStr]
          var val = obj
          this.allMap[key] = val
        }
      }
      if (his && list && list.length > 0) {
        const nlist = []
        // 拼装历史
        for (let i = 0; i < list.length; i++) {
          const tm = list[i]
          if (this.allMap[tm] && this.allMap[tm][this.keyStr] == tm) {
            nlist.push(this.allMap[tm])
          }
        }
        list = nlist
      }
       var v = "--";
        if (keyVal != undefined) {
          if (this.allMap != undefined) {
            var obj = this.allMap[keyVal]
            if (obj != undefined) {
               if (this.isYewu == undefined){
                  this.show = true
               }else{
                  if ((obj[this.valStr] != null || obj[this.valStr] != '')  && inde == 1){
                  this.showyewu = true
                }
               }
              // v = obj[this.valStr]
              v = this.fmtVarStr(obj,list)
            }
          }
        }
        return v
      // var v = '--'
      // // for (var i = 0; i < this.allData.length; i++) {
      // var tmp
      // if (keyVal) {
      //   tmp = this.hisListData[keyVal]
      // }
      // if (tmp && tmp[this.keyStr] == keyVal) {
      //   if (this.isYewu == undefined) {
      //     this.show = true
      //     return this.fmtVarStr(tmp, list)
      //   } else {
      //     if ((this.fmtVarStr(tmp) != null || this.fmtVarStr(tmp) != '') && inde == 1) {
      //       this.showyewu = true
      //     }
      //     return this.fmtVarStr(tmp, list)
      //   }
      // }
      // // }
      // return v
    },
    showAdd() {
      this.querylist = []
      if (this.showName == undefined || this.showName.length <= 0) {
        this.unQueryName = false
      } else {
        this.inputShowName()
      }

      if (this.recordlist != undefined && this.recordlist != null && this.recordlist.length > 0) {
        // 删除历史数据中，已经查询不到名称的id
        var newrecordlist = []
        for (var i = 0; i < this.recordlist.length; i++) {
          var tmp = this.recordlist[i]
           if(!tmp){
              continue
            }
            var vs = this.getValueStr(tmp);
            if (vs != '--') {
              newrecordlist.push(tmp);
            }
        }
        this.recordlist = newrecordlist
      }
      this.notifyParentopen()
    },
    hideAdd() {
      console.log('执行了关闭窗口的方法！',this.showName,this.valObj)
       if(!this.isAutoSel){
          // 可以填写
          if (this.valObj == null || !this.valObj[this.keyStr] ) {
            this.valObj={
              [this.keyStr]:'',
              [this.valStr.split('+')[0]]:this.showName
            }
            this.notifyParent()
            return
          }

        }
      if (this.valObj == null || this.valObj[this.keyStr] == undefined) {
        if (this.showName != undefined && this.showName != null && this.showName.length > 0) {
          this.$message({
            message: '请点击选择一个' + this.modeName + '!',
            type: 'warning'
          })
        }
        this.showName = ''
      } else {
        this.showName = this.fmtVarStr(this.valObj)
      }
      this.notifyParent()
    },
    choseKey(id) {
      var that = this
      if (this.allData == undefined ||
          this.allData == null ||
          this.allData.length <= 0) {
        setTimeout(function() {
          that.choseKey(id)
        }, 100)
        return
      }
      this.setObjById(id)
    },
    setObjById(id){
      if(this.allMap && Object.keys(this.allMap).length){
        if(this.allMap.hasOwnProperty(id)){
          this.valObj = this.allMap[id]
        }
      }else{
         for (var i = 0; i < this.allData.length; i++) {
            var tmp = this.allData[i]
            if (tmp[this.keyStr] == id) {
              this.valObj = tmp
              break
            }
          }
      }
      if(this.valObj && Object.keys(this.valObj).length){
        this.showName = this.fmtVarStr(this.valObj)
      }else{
        // id未找到 清除id
        this.valObj = {
          [this.keyStr]:'',
          [this.valStr.split('+')[0]]:this.showName
        }
        this.notifyParent()
      }
    },
    clickTag(id, item) {
      this.choseKey(id)
      // if(item == 0){
      this.visibleAdd = false
      // }else {
      //   this.visibleAdd =true
      // }

      if (!this.recordlistHave(this.valObj[this.keyStr])) {
        var newarr = []
        newarr.push(this.valObj[this.keyStr])

        if (this.recordlist != undefined && this.recordlist != null && this.recordlist.length > 0) {
          for (var i = 0; i < this.recordlist.length; i++) {
            if (newarr.length <= 10) {
              newarr.push(this.recordlist[i])
            }
          }
        }

        this.recordlist = newarr
        // 调用后台接口储存到历史记录
        saveRecord(this.dataKey, this.valObj[this.keyStr])
      }
      this.notifyParent()
      // console.log("返回的结果是："+id)
    },
    recordlistHave(id) {
      if (this.recordlist == undefined || this.recordlist == null || this.recordlist.length <= 0) {
        return false
      }
      for (var i = 0; i < this.recordlist.length; i++) {
        var tmp = this.recordlist[i]
        if (tmp == id) {
          return true
        }
      }
      return false
    },
    fmtVarStr(tmp, list = []) {
      if (tmp == undefined || tmp == null || tmp.length <= 0) {
        return ''
      }
      if (this.valStr == undefined || this.valStr == null || this.valStr.length <= 0) {
        return ''
      }
      let boo = false// 是否有重值
      let countRname = 0
      if (list && list.length > 0) {
        for (let i = 0; i < list.length; i++) {
          const tt = list[i]
          if (tt[this.valStr.split('+')[0]] == tmp[this.valStr.split('+')[0]]) {
            countRname++
          }
          if (countRname > 1) {
            boo = true
            break
          }
        }
      }
      if (boo && this.valStr.split('+').length > 1) {
        var arr = this.valStr.split('+')
        var str = ''
        for (var i = 0; i < arr.length; i++) {
          let sv = ''
          sv = tmp[arr[i].split('|')[0]] || ''
          if (arr[i].split('|').length > 1) {
            if (arr[i].split('|')[1]) {
              let fv = arr[i].split('|')[1]
              if (fv.indexOf('e') == fv.length - 1) {
                fv = fv.substring(0, fv.length - 1)
                if(sv){
                  sv = sv.substring(sv.length - fv, sv.length)
                }
              }
            }
          }
          str += sv + ' '
        }
        str = str.substring(0, str.length - 1)
        return str
      }
      return tmp[this.valStr.split('+')[0]]
    },
    timeRunOne(callback) {
      if (this._timeRun) {
        clearTimeout(this._timeRun)
      }
      this._timeRun = setTimeout(() => {
        callback && callback()
      }, 100)
    },
    inputShowName() {
      if (this.showName == undefined || this.showName == null || this.showName == '') {
        this.querylist = []
        this.unQueryName = false
        return
      }
      if (this.visibleAdd == false) {
        this.visibleAdd = true
      }
      this.timeRunOne(()=>{
       var qlist = []
      for (var i = 0; i < this.allData.length; i++) {
        var tmp = this.allData[i]
        if (this.fmtVarStr(tmp) == null) {
          continue
        }
        var fdStart = this.fmtVarStr(tmp).indexOf(this.showName)
        if (fdStart != -1) {
          // console.log('查询：', tmp)
          qlist.push(tmp)
        }
        // console.log('查询结果为：', qlist)
        if (qlist.length >= 10) {
          this.querylist = qlist
          this.checkQueryList()
          return
        }
      }

      this.querylist = qlist
      for (var i = 0; i < this.querylist.length; i++) {
        var tmp = this.querylist[i]
        if (this.showName == this.fmtVarStr(tmp)) {
          this.haveData = true
          return
        } else {
          this.haveData = false
        }
      }
      console.info(this.haveData)
      this.checkQueryList()
      })

    },
    checkQueryList() {
      if (this.querylist === undefined || this.querylist.length <= 0) {
        this.showAddDataView()
      } else {
        console.log(this.querylist)
        this.unQueryName = false
      }
    },
    notifyParent() {
      this.$emit('setVal', this.valObj, this.tablerow)
    },
    notifyParentopen() {
      this.$emit('openCom', this.valObj)
    }
  }
}
</script>

<style scoped>
  .kh-name {
    color: #F56C6C;
  }

  .add-info-btnbg {
    margin-top: 10px;
    text-align: center;
    margin-bottom: 20px;
  }

  .add-info-txt {
    margin-top: 20px;
    text-align: center;
  }

  .add-info-txt2 {
    font-size: 0.7rem;
    color: #47cc389c;
    margin-top: 20px;
  }

  .two-text {
    border-top: 1px dashed #e2e2e2;
    padding-top: 10px;
    margin-top: 10px;
    color: #409EFF;
  }

  .one-text {
    color: rgb(154, 154, 154);
    text-align: center;
    margin-top: 4px;
    font-size: 0.7rem;
  }

  .nodata-imgbg {
    text-align: center;
    margin-top: 20px;
  }

  .nodata-img {
    width: 80px;
  }

  .kh_item1 {
    margin: 5px 5px;
  }

  .kh_title {
    font-size: 1.2rem;
    color: #BCBEC2;
    border-bottom: 1px solid #bcbec252;
    margin-bottom: 10px;
  }

  .kh_content {
    display: flex;
    flex-wrap: wrap;
  }
.title_left_name{
    width: 50%;
    text-align: left;
  }
  .title_right_clear{
    width: 50%;
    text-align: right;
    font-size: 1rem;
    color: #409eff;
    cursor: pointer;
  }
  .kh_title_lr{
    display: flex;
    font-size: 1.2rem;
    color: #BCBEC2;
    /* border-bottom: 1px solid #bcbec252; */
    margin-bottom: 10px;
  }
  .zanwujilu {
    width: 100%;
    text-align: center;
    padding: 10px 0px;
    color: #BCBEC2;
  }
  .el-input{
    width:100%;
  }
  .flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .wd20{
    width:240px;
  }
  .grid2{
    width: 70%;
    margin: 0 auto;
    display:grid;
    grid-template-columns:100px 200px;
  }
</style>
