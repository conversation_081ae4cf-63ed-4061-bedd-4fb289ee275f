<template>
  <el-popover
    v-model="visibleAdd"
    placement="right"
    :width="panelWidth"
    trigger="click"
    @show="showAdd"
    @hide="hideAdd"
  >
    <div>
      <div v-if="!isshowAddDetail" class="kh_title_lr">
        <div class="title_left_name">选择{{ modeName }}</div>
        <div class="title_right_clear" @click="clearnData">清空</div>
      </div>
      <!--      <div v-if="isshowAddDetail" class="kh_title">新增{{modeName}}</div>-->

      <!--      <div v-if="unQueryName">-->
      <!--        <div class="nodata-imgbg">-->
      <!--          <img class="nodata-img" src="../../assets/img/nodata.png"/>-->
      <!--        </div>-->

      <!--        <div class="one-text">-->
      <!--          未查询到关于"{{showName}}"的数据！点击下方"确定"，添加此数据。-->
      <!--        </div>-->
      <!--        <div class="two-text">-->
      <!--          快捷新增数据-->
      <!--        </div>-->
      <!--        <div class="add-info-txt">-->
      <!--          新增{{modeName}}：<span class="kh-name">{{showName}}</span>-->
      <!--        </div>-->
      <!--        <div class="add-info-btnbg">-->
      <!--          <el-button size="small" :loading="reqing" @click="adddata" type="primary">确认</el-button>-->
      <!--        </div>-->
      <!--        &lt;!&ndash;                <div class="add-info-txt2">您可以通过点击上方"确认"按钮直接新增"{{showName}}"到系统{{modeName}}中</div>&ndash;&gt;-->
      <!--      </div>-->

      <div class="kh_content" style="margin-bottom: 10px;padding-bottom: 10px">
        <div v-if="!isshowAddDetail">
          <div style="color: #46A0FC;">
            查询结果：
          </div>
          <div v-if="!allData || allData.length<=0">
            <!--            <div class="nodata-imgbg">-->
            <!--              <img class="nodata-img" src="../../assets/img/nodata.png"/>-->
            <!--            </div>-->
            <!--            <div class="one-text">-->
            <!--              未查询到"{{modeName}}"数据！-->
            <!--            </div>-->
          </div>
          <el-tag v-for="(item,index) in allData" v-if="item.dictionaryKey != undefined ? true :( item.email === undefined ? true : false)" :key="index" class="kh_item1" :type="valObj[keyStr]==item[keyStr]?'success':'info'" @click="clickTag(item,0)">
            {{ item[valStr] }}
          </el-tag>
          <div v-if="!commonlist || commonlist.length<=0">
            <!--            <div class="nodata-imgbg">-->
            <!--              <img class="nodata-img" src="../../assets/img/nodata.png"/>-->
            <!--            </div>-->
            <!--            <div class="one-text">-->
            <!--              未查询到"{{modeName}}"数据！-->
            <!--            </div>-->
          </div>
          <el-tag
            v-for="(item,index) in commonlist"
            v-if="getValueStr(item) == '--'? false: true "
            :key="index"
            class="kh_item1"
            :type="valObj[keyStr]==item[keyStr]?'success':'info'"
            @click="clickTag(item,0)"
          >
            <!--          {{item[valStr]}}-->
            {{ getValueStr(item) }}
          </el-tag>
        </div>
      </div>
    </div>
    <el-input slot="reference" v-model="showName" :readonly="onlyclick" :placeholder="'点击选择'+modeName" @input="inputShowName" @clear="clearInput" />
  </el-popover>
</template>

<script>
import { commonreq } from '../../api/common/commonreq'
import { getCommonAndRecordByKey, saveRecord } from '../../api/system/baseInit'

export default {
  components: {
  },
  props: {
    onlyclick: Boolean, // 是否只可点击
    allData: Array, // 全部对象列表
    modeName: String,
    keyStr: String, // 数据对象中的key对应的字符
    valStr: String, // 数据对象中展示文本对应的字符
    addUrl: String, // 新增数据接口地址，上传参数{name:***}，返回结果格式{id:***}。需要返回新增数据的id字符串
    addDictionaryKey: String, // 如果添加类型是字典类型需要设置一个key值
    dataKey: String, // 后台常用数据、用户记录数据储存对应的key
    componentName: String, // 自定义弹窗组件名称
    addCallback: Boolean, // 是否开启新增按钮一直展示，点击新增回调父组件方法。注：开启后搜索新增功能会自动关闭。一版开启这个功能需求是要新增一条比较完善的数据信息
    addCallbackCus: Boolean,
    defCode: [String,Number]// 默认值
  },
  data() {
    return {
      readonly: true,
      commonlist: [], // 常用数据列表
      visibleAdd: false, // 是否显示弹出选择面板
      showName: '', // 输入框中展示选中的名称
      valObj: {}, // 用户最终选择的对象

      querylist: [],
      unQueryName: false,
      reqing: false,
      isshowAddDetail: false,

      minWidth: 400,
      mediumWidth: 700,
      maxWidth: 900,

      panelWidth: 400,
      filterData: []
    }
  },
  created() {
    getCommonAndRecordByKey(this.dataKey).then(res => {
      if (res != undefined && res.resultCode === '0') {
        this.commonlist = res.commonlist
      }
    })
    if (this.defCode) {
      this.defSelCode(this.defCode)
    }
  },
  mounted() {
  },
  methods: {
    defSelCode(keyval) {
      for (var i = 0; i < this.allData.length; i++) {
        var tmp = this.allData[i]
        if (tmp[this.keyStr] == keyval) {
          this.valObj = tmp
          break
        }
      }
      if (this.valObj && Object.keys(this.valObj).length > 0) {
        this.showName = this.valObj[this.valStr]
      }
    },
    getValueStr(keyVal) {
      var v = '--'
      if (this.filterData.length != 0) {
        for (var i = 0; i < this.filterData.length; i++) {
          var tmp = this.filterData[i]
          if (tmp[this.keyStr] == keyVal) {
            return tmp[this.valStr]
          }
        }
      } else {
        for (var i = 0; i < this.allData.length; i++) {
          var tmp = this.allData[i]

          if (tmp[this.keyStr] == keyVal) {
            return tmp[this.valStr]
          }
        }
      }
      return v
    },
    clearInput() {
      this.filterData = []
      this.$emit('clearInput', null)
    },

    clickAddDetailback() {
      this.isshowAddDetail = false
      this.panelWidth = this.minWidth
    },
    clickAddDetail() {
      this.$emit('addMethod')
      // this.isshowAddDetail = true
      // this.panelWidth = this.mediumWidth
    },
    adddata() {
      if (!this.addCallback) {
        if ((this.addUrl == undefined || this.addUrl.length <= 0) &&
              (this.addDictionaryKey == undefined || this.addDictionaryKey.length <= 0)) {
          this.$message({
            message: '当前面板未设置新增接口请设置新增接口或新增字典key值！',
            type: 'warning'
          })
          return
        }
        var that = this
        this.reqing = true

        if (this.addUrl !== undefined && this.addUrl.length > 0) {
          commonreq({
            name: this.showName
          }, this.addUrl).then(res => {
            if (res !== undefined && res.resultCode === '0') {
              var obj = {}
              obj[that.keyStr] = res.id
              obj[that.valStr] = that.showName
              that.allData.push(obj)
              that.clickTag(obj, 0)
              that.reqing = false
            }
          })
        } else if (this.addDictionaryKey !== undefined && this.addDictionaryKey.length > 0) {
          commonreq({
            key: this.addDictionaryKey,
            name: this.showName
          }, '/oa/sysDictionary/saveDefSysDictionary').then(res => {
            if (res !== undefined && res.resultCode === '0') {
              var obj = {}
              obj[that.keyStr] = res.code
              obj[that.valStr] = that.showName
              that.allData.push(obj)
              that.clickTag(obj, 0)
              that.reqing = false
            }
          })
        }
      }
    },
    showAddDataView() {
      if (!this.addCallback) {
        // if ((this.addUrl == undefined || this.addUrl.length <= 0)&&
        //   (this.addDictionaryKey == undefined || this.addDictionaryKey.length <= 0)) {
        //   this.$message({
        //     message: '当前面板未设置新增接口请设置新增接口或新增字典key值！',
        //     type: 'warning'
        //   });
        //   return;
        // }
        this.unQueryName = true
      }
    },
    clean() {
      this.valObj = {}
      this.showName = null
      // this.inputShowName()
    },
    clearnData() {
      this.clean()
      this.clickTag(this.valObj, 1)
      this.filterData = []
    },
    showAdd() {
      this.querylist = []
      if (this.showName == undefined || this.showName.length <= 0) {
        this.unQueryName = false
      } else {
        this.inputShowName()
      }
      this.clickAddDetailback()
      // this.notifyParentopen()
    },
    notifyParentopen() {
      this.$emit('openCom', this.valObj)
    },
    hideAdd() {
      console.log('执行了关闭窗口的方法！')
      if (this.valObj == null || this.valObj[this.keyStr] == undefined) {
        if (this.showName != undefined && this.showName != null && this.showName.length > 0) {
          this.$message({
            message: '请点击选择一个' + this.modeName + '!',
            type: 'warning'
          })
        }
        this.showName = ''
      } else {
        this.showName = this.valObj[this.valStr]
      }
      this.notifyParent()
    },

    choseKey(keyval) {
      var that = this
      if (this.allData == undefined ||
          this.allData == null ||
          this.allData.length <= 0) {
        setTimeout(function() {
          that.choseKey(keyval)
        }, 100)
        return
      }

      for (var i = 0; i < this.allData.length; i++) {
        var tmp = this.allData[i]
        if (tmp[this.keyStr] == keyval) {
          this.valObj = tmp
          break
        }
      }
      this.showName = this.valObj[this.valStr]
    },

    choseObj(obj) {
      for (var i = 0; i < this.allData.length; i++) {
        var tmp = this.allData[i]
        if (tmp[this.keyStr] == obj[this.keyStr]) {
          this.valObj = tmp
          break
        }
      }
      this.showName = this.valObj[this.valStr]
    },
    clickTag(value) {
      if (typeof value === 'string') {
        this.choseKey(value)
        this.notifyParent()
      } else {
        this.choseObj(value)
      }
      this.visibleAdd = false
    },
    inputShowName() {
      if (this.addCallback) {
        this.$emit('selectDataList', this.showName)
        if (this.showName == undefined || this.showName == null || this.showName == '') {
          this.unQueryName = false
          return
        }
        if (this.visibleAdd == false) {
          this.visibleAdd = true
        }
        var qlist = []
        for (var i = 0; i < this.allData.length; i++) {
          var tmp = this.allData[i]

          var fdStart = tmp[this.valStr].indexOf(this.showName)
          if (fdStart != -1) {
            qlist.push(tmp)
          }
          console.log(qlist.length)
          if (qlist.length >= 10) {
            this.querylist = qlist
            this.checkQueryList()
            return
          }
        }
        this.querylist = qlist
        this.checkQueryList()
      } else {
        if (this.showName == undefined || this.showName == null || this.showName == '') {
          this.querylist = []
          this.filterData = []
          this.unQueryName = false
          return
        }
        if (this.visibleAdd == false) {
          this.visibleAdd = true
        }
        var qlist = []
        for (var i = 0; i < this.allData.length; i++) {
          var tmp = this.allData[i]
          if (tmp[this.valStr] == null) {
            continue
          }

          var fdStart = tmp[this.valStr].indexOf(this.showName)
          if (fdStart >= 0) {
            qlist.push(tmp)
          }
          if (qlist.length >= 10) {
            this.querylist = qlist
            this.checkQueryList()
            return
          }
        }
        this.querylist = qlist
        this.checkQueryList()
      }
    },
    checkQueryList() {
      console.log('------------')
      console.log(this.querylist.length)
      if (this.querylist === undefined || this.querylist.length <= 0) {
        this.showAddDataView()
      } else {
        this.unQueryName = false
        this.filterData = this.querylist
      }
    },
    notifyParent() {
      this.$emit('setVal', this.valObj)
    },
    addSuccess(obj) {
      this.allData.push(obj)
      this.clickTag(obj, 0)
    }
  }
}
</script>

<style scoped>
  .add_txtbtn{
    cursor: pointer;
    color: #3377FB;
    text-align: right;
    margin-top: 30px;
  }
  .kh-name {
    color: #F56C6C;
  }

  .add-info-btnbg {
    margin-top: 10px;
    text-align: center;
    margin-bottom: 20px;
  }

  .add-info-txt {
    margin-top: 20px;
    text-align: center;
  }

  .add-info-txt2 {
    font-size: 0.7rem;
    color: #47cc389c;
    margin-top: 20px;
  }

  .two-text {
    border-top: 1px dashed #e2e2e2;
    padding-top: 10px;
    margin-top: 10px;
    color: #409EFF;
  }

  .one-text {
    color: rgb(154, 154, 154);
    text-align: center;
    margin-top: 4px;
    font-size: 0.7rem;
  }

  .nodata-imgbg {
    text-align: center;
    margin-top: 20px;
  }

  .nodata-img {
    width: 80px;
  }

  .kh_item1 {
    margin: 5px 5px;
  }
  .title_left_name{
    width: 50%;
    text-align: left;
  }
  .title_right_clear{
    width: 50%;
    text-align: right;
    font-size: 1rem;
    color: #409eff;
    cursor: pointer;
  }
  .kh_title_lr{
    display: flex;
    font-size: 1.2rem;
    color: #BCBEC2;
    /* border-bottom: 1px solid #bcbec252; */
    margin-bottom: 10px;
  }
  .kh_title {
    font-size: 1.2rem;
    color: #BCBEC2;
    border-bottom: 1px solid #bcbec252;
    margin-bottom: 10px;
  }
  .el-input{
    width:100%;
  }

  .disabledInput >>> .el-input__inner{
    background-color: #ffffff;
    cursor: pointer;
    color: #606266;
  }
</style>
