<template>
  <div>
    <el-select v-model="selData" clearable filterable placeholder="请选择" @change="changeVal">
      <el-option
        v-for="item in data"
        :key="item.id"
        :label="item.name"
        :value="item.id+''"
      />
    </el-select>
  </div>
</template>

<script>
import { queryCustomerByDictId, queryCustomerByDeptId } from '@/api/system/sysExternalCustomer'
import { add } from '@/api/system/sysExternalCustomerPlate'
import { dictIdByDeptId } from '@/api/business/sysCompanyPlate'
export default {
  model: {
    prop: 'valnamecustomer',
    event: 'changeVal'
  },
  props: {
    valnamecustomer: {
      type: String,
      default: () => ''
    },
    dictId: {
      type: String,
      default: () => ''
    },
    deptId: {
      type: String,
      default: () => ''
    },
    isCustomer: {
      type: String,
      default: () => ''
    },
    isSupplier: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      data: [],
      selData: this.valnamecustomer || ''
    }
  },
  watch: {
    dictId: function(newval) {
      this.loadData()
    },
    deptId: function(newval) {
      this.loadData()
    },
    valnamecustomer: function(newval) {
      this.selData = newval
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    changeVal() {
      // 保存相关
      if (this.dictId) {
        const fromData = {
          dictId: this.dictId,
          customerId: this.selData || '',
          isCustomer: this.isCustomer,
          isSupplier: this.isSupplier
        }
        add(fromData)
      }
      if (this.deptId) {
        dictIdByDeptId(this.deptId).then(res => {
          // console.log('dictId', res)
          const fromData = {
            dictId: res,
            customerId: this.selData || '',
            isCustomer: this.isCustomer,
            isSupplier: this.isSupplier
          }
          add(fromData)
        })
      }
      // 客户id 客户名称
      let name = ''
      for (let i = 0; i < this.data.length; i++) {
        if (this.selData === this.data[i].id + '') {
          name = this.data[i].name
          break
        }
      }
      this.$emit('changeVal', this.selData)
      this.$emit('customerNameChange', name)
    },
    loadData() {
      // 加载数据
      if (this.dictId) {
        queryCustomerByDictId({ dictId: this.dictId, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(res => {
          this.data = res
        })
        return
      }
      if (this.deptId) {
        // console.log('dept', this.deptId)
        queryCustomerByDeptId({ deptId: this.deptId, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(res => {
          // console.log('dept res', res)
          this.data = res
        })
        return
      }
      // console.log('no dict and dept')
      queryCustomerByDictId({ isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(res => {
        this.data = res
      })
    }
  }
}
</script>

<style scoped>

</style>
