<template>
  <div>
    <el-select v-model="selData" clearable filterable placeholder="请选择" @change="changeVal">
      <el-option
        v-for="item in data"
        :key="item.id"
        :label="item.department+'-'+item.contactName"
        :value="item.id+''"
      />
    </el-select>
  </div>
</template>

<script>
import { listByCustomerIdAndDictId, sysExternalCustomerContactPlateAdd } from '@/api/system/sysExternalCustomer'
import { dictIdByDeptId } from '@/api/business/sysCompanyPlate'
export default {
  model: {
    prop: 'contactIdvalue',
    event: 'changeVal'
  },
  props: {
    // 根据 客户id 选择 联系人
    contactIdvalue: {
      type: String,
      default: () => ''
    },
    dictId: {
      type: String,
      default: () => ''
    },
    deptId: {
      type: String,
      default: () => ''
    },
    isCustomer: {
      type: String,
      default: () => ''
    },
    isSupplier: {
      type: String,
      default: () => ''
    },
    customerId: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      selData: this.contactIdvalue,
      data: []
    }
  },
  watch: {
    customerId: function(newval) {
      this.loadData()
    }
  },
  methods: {
    changeVal() {
      if (this.selData) {
        let id = ''
        let item = {}
        for (let i = 0; i < this.data.length; i++) {
          if (this.data[i].id == this.selData) {
            item = this.data[i]
            id = this.selData
            break
          }
        }
        this.$emit('contactDataChange', item)
        if (!id) {
          this.selData = ''
        }
      }

      if (this.selData) {
        sysExternalCustomerContactPlateAdd({
          contactId: this.selData,
          dictId: this.dictId || this.tmpDictId,
          isCustomer: this.isCustomer,
          isSupplier: this.isSupplier
        })
      }

      this.$emit('changeVal', this.selData)
    },
    loadData() {
      this.data = []
      // 根据客户id 加载联系人
      if (this.deptId) {
        dictIdByDeptId(this.deptId).then(dictId => {
          this.tmpDictId = dictId
          listByCustomerIdAndDictId({ customerId: this.customerId, dictId: dictId, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(res => {
            this.data = res
            this.changeVal()
          })
        })
        return
      }
      listByCustomerIdAndDictId({ customerId: this.customerId, dictId: this.dictId, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(res => {
        this.data = res
        this.changeVal()
      })
    }
  }

}
</script>

<style>

</style>
