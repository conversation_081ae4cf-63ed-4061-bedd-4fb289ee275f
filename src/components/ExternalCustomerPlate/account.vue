<template>
  <div>
    <el-select v-model="selData" clearable filterable placeholder="请选择" @change="changeVal">
      <el-option
        v-for="item in data"
        :key="item.id"
        :label="item.bankAccount"
        :value="item.bankAccount"
      />
    </el-select>
  </div>
</template>

<script>
import { queryBankNameByCustomerIdAndDictId, sysExternalAccountPlateAdd } from '@/api/business/sysExternalAccount'
import { dictIdByDeptId } from '@/api/business/sysCompanyPlate'
export default {
  model: {
    prop: 'accountName',
    event: 'changeval'
  },
  props: {
    accountName: { // 默认卡号
      type: String,
      default: () => ''
    },
    bankName: { // 开户行
      type: String,
      default: () => ''
    },
    dictId: { // 版块id
      type: String,
      default: () => ''
    },
    deptId: { // 公司id 获取版块id
      type: String,
      default: () => ''
    },
    isCustomer: { // 类型：客户
      type: String,
      default: () => ''
    },
    isSupplier: { // 类型：供应商
      type: String,
      default: () => ''
    },
    customerId: { // 客户id
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      selData: this.accountName,
      data: []
    }
  },
  watch: {
    bankName: function(newval) {
      // console.log('account bankname', newval)
      this.loadData()
    },
    customerId: function(newval) {
      this.loadData()
    }
  },
  methods: {
    changeVal() {
      // 保存id
      // 根据名称
      if (this.selData) {
        let id = ''
        for (let i = 0; i < this.data.length; i++) {
          if (this.selData == this.data[i].bankAccount) {
            id = this.data[i].id
            break
          }
        }
        if (id) {
          sysExternalAccountPlateAdd({ accountId: id, dictId: this.dictId || this.tmpDictId, isCustomer: this.isCustomer, isSupplier: this.isSupplier })
        } else {
          this.selData = ''
        }
      }

      this.$emit('changeval', this.selData)
    },
    loadData() {
      this.data = []
      if (!this.bankName) {
        return
      }
      if (this.deptId) {
        dictIdByDeptId(this.deptId).then(res => {
          this.tmpDictId = res
          queryBankNameByCustomerIdAndDictId({ customerId: this.customerId, bankName: this.bankName, dictId: res, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(qres => {
            // console.log('bank', qres)
            this.data = qres
            this.changeVal()
          })
        })
        return
      }
      queryBankNameByCustomerIdAndDictId({ customerId: this.customerId, bankName: this.bankName, dictId: this.dictId, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(qres => {
        // console.log('bank', qres)
        this.data = qres
        this.changeVal()
      })
    }
  }
}
</script>

<style>

</style>
