
<template>
  <div>
    <el-select v-model="selData" clearable filterable placeholder="请选择" @change="changeVal">
      <el-option
        v-for="item in data"
        :key="item.id"
        :label="item.bankName"
        :value="item.bankName"
      />
    </el-select>
  </div>
</template>

<script>
import { queryBankNameByCustomerIdAndDictId } from '@/api/business/sysExternalAccount'
import { dictIdByDeptId } from '@/api/business/sysCompanyPlate'
export default {
  // 根据 客户id 客户｜供应商 版块or公司
  model: {
    prop: 'vname',
    event: 'changeVal'
  },
  props: {
    vname: {
      type: String,
      default: () => ''
    },
    dictId: {
      type: String,
      default: () => ''
    },
    deptId: {
      type: String,
      default: () => ''
    },
    isCustomer: {
      type: String,
      default: () => ''
    },
    isSupplier: {
      type: String,
      default: () => ''
    },
    customerId: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      selData: this.vname || '',
      data: []
    }
  },
  watch: {
    customerId: function(newval) {
      this.loadData()
    }
  },
  created() {
    // this.loadData()
  },
  methods: {
    changeVal() {
      if (this.selData) {
        let id = ''
        for (let i = 0; i < this.data.length; i++) {
          if (this.selData == this.data[i].bankName) {
            id = this.data[i].bankName
            break
          }
        }
        if (!id) {
          this.selData = ''
        }
      }
      this.$emit('changeVal', this.selData)
    },
    initData() {
      this.changeVal()
    },
    loadData() {
      // console.log('bank data', this.customerId, this.dictId, this.deptId)
      this.data = []

      if (this.deptId) {
        dictIdByDeptId(this.deptId).then(res => {
          queryBankNameByCustomerIdAndDictId({ customerId: this.customerId, dictId: res, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(qres => {
            // console.log('bank', qres)
            this.data = qres
            this.initData()
          })
        })
        return
      }
      if (this.dictId) {
        queryBankNameByCustomerIdAndDictId({ customerId: this.customerId, dictId: this.dictId, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(res => {
          // console.log('bank', res)
          this.data = res
          this.initData()
        })
        return
      }
      // 根据客户id 查询开户行
      queryBankNameByCustomerIdAndDictId({ customerId: this.customerId, dictId: this.dictId, isCustomer: this.isCustomer, isSupplier: this.isSupplier }).then(res => {
        // console.log('bank', res)
        this.data = res
        this.initData()
      })
    }
  }
}
</script>

<style>

</style>
