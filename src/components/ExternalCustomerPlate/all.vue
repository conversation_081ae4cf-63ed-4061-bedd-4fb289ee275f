<template>
  <section>
    <el-form :inline="inline">
      <el-form-item :label="customerLabel">
        <ExternalCustomerPlate v-model="customerId" :is-customer="isCustomer" :is-supplier="isSupplier" :dept-id="deptId" :dict-id="dictId" @customerNameChange="changeCustomerName" />
      </el-form-item>
      <el-form-item :label="bankLabel">
        <BankName v-model="bankNameVal" :is-customer="isCustomer" :is-supplier="isSupplier" :dept-id="deptId" :dict-id="dictId" :customer-id="customerId" />
      </el-form-item>
      <el-form-item :label="accountLabel">
        <BankAccount v-model="bankAccountVal" :is-customer="isCustomer" :is-supplier="isSupplier" :dept-id="deptId" :dict-id="dictId" :customer-id="customerId" :bank-name="bankNameVal" />
      </el-form-item>
      <el-form-item v-if="showContact" :label="contactLabel">
        <Contact v-model="contactId" :is-customer="isCustomer" :is-supplier="isSupplier" :dept-id="deptId" :dict-id="dictId" :customer-id="customerId" @contactDataChange="changeContactData" />
      </el-form-item>
    </el-form>
  </section>
</template>

<script>
import ExternalCustomerPlate from '@/components/ExternalCustomerPlate/index.vue'
import BankName from '@/components/ExternalCustomerPlate/bankName.vue'
import BankAccount from '@/components/ExternalCustomerPlate/account.vue'
import Contact from '@/components/ExternalCustomerPlate/contact.vue'

export default {
  components: {
    ExternalCustomerPlate, BankName, BankAccount, Contact
  },
  props: {
    showContact: {
      type: Boolean,
      default: false
    },
    inline: { // 可以在一行内放置表单项
      type: Boolean,
      default: () => true
    },
    isCustomer: { // 是否客户 1是 0否
      type: String,
      default: () => ''
    },
    isSupplier: { // 是否供应商 1 是 0 否
      type: String,
      default: () => ''
    },
    dictId: { // 版块id
      type: String,
      default: () => ''
    },
    deptId: { // 公司id
      type: String,
      default: () => ''
    },
    customerLabel: {
      type: String,
      default: () => '客户公司'
    },
    bankLabel: {
      type: String,
      default: () => '开户行'
    },
    accountLabel: {
      type: String,
      default: () => '卡号'
    },
    contactLabel: {
      type: String,
      default: () => '联系人'
    }
  },
  data() {
    return {
      customerName: '',
      customerId: '',
      bankNameVal: '',
      bankAccountVal: '',
      contactId: '',
      contactSelData: {}
    }
  },
  watch: {
    customerId: function(val) {
      this.$emit('customerIdChange', val)
    },
    customerName: function(newval) {
      this.$emit('customerNameChange', newval)
    },
    bankNameVal: function(newval) {
      this.$emit('bankNameChange', newval)
    },
    bankAccountVal: function(newval) {
      this.$emit('bankAccountChange', newval)
    },
    contactSelData: function(newval) {
      this.$emit('contactDataChange', newval)
    },
    contactId: function(newval) {
      this.$emit('contactIdChange', newval)
    }
  },
  methods: {
    changeCustomerName(val) {
      this.customerName = val
    },
    changeContactData(data) {
      console.log(data)
      this.contactSelData = data
      // this.$emit('contactDataChange', data)
    }
  }
}
</script>

<style>

</style>
