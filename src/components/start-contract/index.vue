<template>
  <el-dialog
    v-if="isDialog"
    custom-class="contract-iframe-dialog"
    :title="title"
    width="800px"
    @opened="openIframe"
    @close="closeLayer"
    :before-close="handleClose"
    :visible.sync="innerShow"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div id="iframeBox">
      <div v-if="iframeLoading" class="iframeLoading">加载中...</div>
    </div>
  </el-dialog>
  <div v-else id="iframeBox">
    <div v-if="iframeLoading" class="iframeLoading">加载中...</div>
  </div>
</template>

<script>
import { getUserId } from '@/api/login'
export default {
  data() {
    return {
      innerShow: false,
      iframeLoading: false,
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    isDialog: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '创建合同',
    }
  },
  watch: {
    show(newVal,oldVal){
      this.innerShow = newVal;
      if (!this.isDialog) {
        if (newVal) {
          this.openIframe()
        } else {
          this.closeLayer()
        }
      }
    }
  },
  mounted() {
    //监听message事件
    window.addEventListener("message", this.receiveMessage, false);
  },
  methods: {
    handleClose(done) {
      console.log('close dialog');
      this.$confirm('确认关闭？')
        .then(_ => {
          // 根据关闭页面处理
          // 审批页面关闭 代表合同已生成 绑定合同

          // 审批完成页面关闭 开始走流程
          done();
        })
        .catch(_ => {});
    },
    //回调函数
    receiveMessage(event) {
      // console.log("接收到子页面的消息:", event.data);
      if (typeof event.data !== "object") {
        return;
      }
      if (!["contractIframe","sendMsgUpContract","sendMsgUpProcess"].includes(event.data.source)) {
        return;
      }
      if (typeof event.data.payload !== "object") {
        return;
      }
      if (event.data.source === "contractIframe" && event.data.key === 'successBackPage') {
        this.$emit("success-back-page", event.data.payload, event.data.callbackData);
      }
      console.log(event.data);
      this.$emit("complete", event.data.payload, event.data.callbackData);
      this.closeLayer();
    },
    openIframe() {
      const _this = this;

      let iframeBox = document.querySelector("#iframeBox");
      // 如果已经存在iframe节点，先删除
      iframeBox.querySelector('iframe') && iframeBox.removeChild(iframeBox.querySelector('iframe'));

      _this.iframeLoading = true;

      // 获取payload参数：配置发起合同的数据
      let payloadPromise = null
      _this.$emit('payload',(res) => {
        payloadPromise = res
      })

      // 获取iframe请求额外的参数
      let extraParams = {}
      _this.$emit('extra-params',(res) => {
        extraParams = res
      })

      Promise.all([
        // 获取用户信息
        getUserId(),
        // payload参数
        payloadPromise
      ]).then(ress => {
        if(!ress[0]) {
          throw new Error('获取用户数据失败')
        }
        let wxuserId = ress[0]
        let payload = null;
        if(ress[1]) {
          payload = ress[1]
        }
        console.log(ress)
        this.$nextTick(() => {
          // 因为 v-if 会删除dom，所以不用清空子节点。如果使用v-show需要手动清空子节点
          // 创建iframe节点，并设置onload函数
          let iframe = document.createElement("iframe");
          //iframe加载完毕后再发送消息，否则子页面接收不到message
          iframe.onload = function () {
            _this.iframeLoading = false;
            //iframe加载完立即发送一条消息
            let data = {
              source: "caiwu",
              payload: payload
            };
            iframe.contentWindow.postMessage(data, "*");
          };
          let src = process.env.VUE_APP_BASE_API + "/api/contract/view/create?source=caiwu&tplModule=1&wxuid="+wxuserId;
          // 拼接额外参数
          if (extraParams) {
            for (let key in extraParams) {
              src += `&${key}=${extraParams[key]}`
            }
          }
          iframe.src = src;
          iframe.style.width = "100%";
          iframe.style.height = "100%";
          iframe.style.border = "0";
          // 把iframe节点添加到dom树上
          iframeBox.appendChild(iframe);
        });
      }).catch(err => {
        console.error(err)
        _this.iframeLoading = false;
        this.$message.error(err.message)
        this.closeLayer()
      }).finally(() => {

      })
    },
    /**
     * 关闭合同iframe
     */
    closeLayer() {
      this.innerShow = false;
      this.$emit("update:show", false);
    },
  },
  destroyed() {
    console.log("父页面 destroyed ...");
    // 在组件生命周期结束的时候销毁。
    window.removeEventListener("message", this.receiveMessage);
  },
};
</script>

<style scoped>
/deep/.contract-iframe-dialog {
  margin: 0px auto;
  margin-top: 10px !important;
}
/deep/.contract-iframe-dialog .el-dialog__body {
  padding: 0px;
}
#iframeBox {
  /* width: 400px;
            height: 400px; */
  /* width: 800px; */
  width: 100%;
  min-height: 600px;
  height: 90vh;
  position: relative;
  background-color: white;
}

.iframeLoading {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 20px;
  transform: translate(-50%, -50%);
}
</style>
