<!-- eslint-disable vue/require-v-for-key -->
<template >
  <tbody>
  <template v-for="(item,index) in paymentWaterList">
        <tr :style="trStyle"  >
          <td style="text-align: center" v-if="index==0" :colspan="tdColspans.split(',')[0]" :rowspan="paymentWaterList.length*3">付款<br/>银行信息</td>
          <td style="text-align: center" :colspan="tdColspans.split(',')[1]">付款机构</td>
          <td style="width: 100%;text-align: left;padding-left: 10px;" :colspan="tdColspans.split(',')[2]">{{ item.accountName }}</td>
        </tr>
        <tr :style="trStyle">
          <td style="text-align: center" :colspan="tdColspans.split(',')[1]">付款银行</td>
          <td style="width: 100%;text-align: left;padding-left: 10px;" :colspan="tdColspans.split(',')[2]">{{ item.accountBank }}</td>
        </tr>
        <tr :style="trStyle">
          <td style="text-align: center" :colspan="tdColspans.split(',')[1]">付款账号</td>
          <td style="width: 100%;text-align: left;padding-left: 10px;" :colspan="tdColspans.split(',')[2]">{{ item.accountNo }}</td>
        </tr>
      </template>
    </tbody>
</template>
<script>
export default {
  name: 'PaymentWaterList',
  props: {
    dataObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    trStyle: {
      type: String,
      default: ''
    },
    tdColspans: {
      type: String,
      default: '1,2,9'
    }
  },
  computed: {
    paymentWaterList() {
      if(this.dataObj && this.dataObj.bakPaymentWaterList) {
        return this.dataObj.bakPaymentWaterList
      }
      return []
    }
  }
}
</script>
<style scoped>
td{
  color: #606266;
}
</style>
