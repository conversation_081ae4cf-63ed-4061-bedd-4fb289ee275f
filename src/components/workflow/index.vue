<template>
  <div>
    <el-drawer
      :title="title"
      :before-close="handleClose"
      :visible.sync="drawer"
      :size="componentsWidth"
      direction="rtl"
    >
      <div
        id="drawerBody"
        v-loading="loadingComponent"
      >

        <!-- 表单渲染之前的插槽 -->
        <slot name="beforeFormSlot" />

        <!-- 表单 -->
        <es-form v-if="!!formSchema" ref="form" v-model="formValue" :schema="formSchema" />
         <div v-if="useComp">
          <component :is="comp" ref="componentRef" :datas="datas" />
        </div>

        <!-- 表单渲染之后的插槽 -->
        <slot name="afterFormSlot" />

        <!-- 审批流程 -->
        <el-divider content-position="left">审批流程</el-divider>
        <el-timeline id="timelineBody">
          <el-timeline-item
            :timestamp="activities.timestamp"
            placement="top"
          >
            <div>
              <div style="color: #909399;font-size: 10px">
                {{ activities.peoples.length }}人审批
              </div>
              <el-row class="timelineContent">
                <div v-for="people in activities.peoples" style="margin-left:10px">
                  <el-tooltip class="item" effect="light" :content="people.sp_type=='0'?people.name:people.avatar" placement="top-start">
                    <el-avatar v-if="people.sp_type=='0'" shape="square" size="large" :src="people.avatar" />
                    <span v-if="people.sp_type=='1'" class="diy-avatar">{{ people.avatar.substring(0,1) }}</span>
                  </el-tooltip>
                  <!--                  <el-tooltip class="item" effect="light" :content="people.sp_type=='1'?people.avatar:people.name" placement="top-start">-->
                  <!--                    <span class="diy-avatar" v-if="people.sp_type=='1'">{{people.avatar.substring(0,1)}}</span>-->
                  <!--                  </el-tooltip>-->
                </div>
              </el-row>
            </div>
          </el-timeline-item>
          <el-timeline-item
            :timestamp="ccs.timestamp"
            placement="top"
          >
            <div>
              <div v-if="ccs.peoples.length > 0" style="color: #909399;font-size: 10px">
                抄送给{{ ccs.peoples.length }}人
              </div>
              <div v-else style="color: #909399;font-size: 10px">
                请选择抄送人
              </div>
              <el-row class="timelineContent">
                <div v-for="people in ccs.peoples" style="margin-left:10px">
                  <div style="position: relative">
                    <el-avatar shape="square" size="large" :src="people.avatar" />
                    <div class="el-icon-error" style="color:red;position: absolute;top:0;right: 10px;transform: translateY(-50%) translateX(100%);" @click="removeCc(people.id)" />
                  </div>
                </div>
                <div style="margin-left:10px;padding-bottom: 3px">
                  <el-button class="el-icon-plus wh40" @click="addCc" />
                </div>
              </el-row>
              <div v-if="ccs.roles.length > 0" style="color: #909399;font-size: 10px">
                抄送角色：
              </div>
              <el-row class="timelineContent">
                <div v-for="tag in ccs.roles" style="margin-left: 10px;">
                  <el-tag
                    closable
                    @close="removeRole(tag.id)"
                  >
                    {{ tag.name }}
                  </el-tag>
                </div>
              </el-row>
            </div>
          </el-timeline-item>
        </el-timeline>

        <div class="timelineContent">
          <el-button size="small" @click="cancelForm">取 消</el-button>
          <el-button size="small" type="primary" :loading="loading" @click="submitForm">{{ loading ? '提交中 ...' : '确 定' }}</el-button>
        </div>
      </div>
    </el-drawer>
    <depart-component ref="depart" @child-cc="childCheckedCc" />
  </div>
</template>

<script>
import esForm from 'vue-easy-form'
import Vue from 'vue'
import DepartComponent from './depart'
const processApi = require('@/api/system/process')

Vue.use(esForm)

export default {
  name: 'WorkFlowComponent',
  components: { DepartComponent },
  props: {
    componentsWidth: {
      type: String,
      required: false,
      default: '40%'
    }
  },
  data() {
    return {
      title: '审批',
      drawer: false,
      loading: false,
      loadingComponent: false,

      activities: {
        timestamp: '审批人',
        peoples: [
          // {
          //   id: 1,
          //   name: 'sunzs',
          //   avatar: 'https://wework.qpic.cn/wwhead/duc2TvpEgSQO4BpE0WZSZ8UQwKs1xRibCJLiaWayXITKUe6aRaruiacDy32HKIwTnlR6BBzWiaVfq6E/0'
          // },
          // {
          //   id: 2,
          //   name: 'luoyh',
          //   avatar: 'http://wework.qpic.cn/bizmail/PqYXeYnKUc9icFFUvxBE8tyUt0sqmdTNg6eW4jicicX9dic1yqsWW0XBJQ/0'
          // }
        ]
      },
      ccs: {
        timestamp: '抄送人',
        peoples: [
          // {
          //   id: 1,
          //   name: 'sunzs',
          //   avatar: 'https://wework.qpic.cn/wwhead/duc2TvpEgSQO4BpE0WZSZ8UQwKs1xRibCJLiaWayXITKUe6aRaruiacDy32HKIwTnlR6BBzWiaVfq6E/0'
          // },
          // {
          //   id: 2,
          //   name: 'luoyh',
          //   avatar: 'http://wework.qpic.cn/bizmail/PqYXeYnKUc9icFFUvxBE8tyUt0sqmdTNg6eW4jicicX9dic1yqsWW0XBJQ/0'
          // }
        ],
        roles: [
          // {
          //   id: '1',
          //   name: 'CEO'
          // }
        ]
      },

      formValue: {},
      formSchema: {
        properties: {
          type: {
            label: '备注',
            component: 'el-input'
          }
        }
      },

      // 摘要
      briefContent: undefined,
      processCode: undefined,
      // 全局参数，请传入map
      // {
      //   params1: '',
      // ...
      //   params10: ''
      // }
      globalParams: undefined,
       // 自定义显示组件
      comp: resolve => require([`@/components/approval/DefaultComponent.vue`], resolve),
      useComp: false,
      datas: {}
    }
  },
  methods: {
    doInit() {
      if (!this.processCode) {
        this.msgError('请传递流程Code')
        return
      }

      this.loadingComponent = true
      processApi.getProcess(this.processCode).then(res => {
        if (res.data.formSchema) {
          this.formSchema = JSON.parse(res.data.formSchema)
        } else {
          this.formSchema = null
        }

        if (res.data.sp) {
          this.activities.peoples = res.data.sp
        }
        if (res.data.cc) {
          this.ccs.peoples = res.data.cc
        }
        if (res.data.viewName) {
          this.useComp = true
          this.comp = resolve => require([`@/components/approval/${res.data.viewName}.vue`], resolve)
        }
      }).catch(e => {

      }).finally(() => {
        this.loadingComponent = false
      })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    },
    cancelForm() {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.drawer = false
        })
        .catch(_ => {
        })
    },
    submitForm() {
      if (undefined === this.$refs.form || this.$refs.form.checkAll()) {
        // 提交之前提供给调用者的回调，可以用来自定义校验
        let flag = true
        this.$emit('onBeforeSubmit', (res) => {
          if (!res) {
            flag = false
          }
        })
        if (!flag) return

        this.loading = true

        // 表单值
        const params = {
          value: this.formValue
        }
        // 全局参数
        if (this.globalParams) {
          params.global = this.globalParams
        }
        // 抄送人
        if (this.ccs.peoples) {
          params.ccs = this.ccs.peoples.map(item => item.id)
        }
        // 抄送角色
        if (this.ccs.roles) {
          params.roles = this.ccs.roles.map(item => item.id)
        }
        // 摘要
        if (this.briefContent) {
          params.briefContent = this.briefContent
        }

        processApi.startProcess(this.processCode, params).then(res => {
          console.log('270',res)
          if (res.data) {
            this.$message.success('提交成功')
            this.drawer = false
            this.$emit('onCallback', res.data)
          } else {
            this.msgError(res.data.resultMsg || 'Error')
          }
        }).catch(e => {

        }).finally(() => {
          this.loading = false
        })
      }
    },

    addCc() {
      this.$refs.depart.dialog = true
      this.$refs.depart.doInit()
    },
    childCheckedCc(data) {
      if (data.cc) {
        this.ccs.peoples = this.unique(this.ccs.peoples.concat(data.cc))
      }
      if (data.role) {
        this.ccs.roles = this.unique(this.ccs.roles.concat(data.role))
      }
    },
    // 对象数组去重
    unique(objArray) {
      const hash = {}
      objArray = objArray.reduce(function(item, next) {
        hash[next.name] ? '' : hash[next.name] = true && item.push(next)
        return item
      }, [])
      return objArray
    },
    removeCc(id) {
      this.ccs.peoples = this.ccs.peoples.filter(item => item.id !== id)
    },
    removeRole(id) {
      this.ccs.roles = this.ccs.roles.filter(item => item.id !== id)
    }
  }
}
</script>
<style>
  .el-drawer{
    overflow: scroll
  }
  .el-drawer :focus{
    outline:0;
  }
  .el-drawer .el-drawer__header span{
    font-size: 22px;
    color: #55575C;
  }
</style>
<style scoped>
  #drawerBody{
    padding: 10px;
  }
  #timelineBody > * {
    text-align: left !important;
  }
  .wh40{
    width: 40px;
    height: 42px;
    padding: 0 !important;
    margin: 0 !important;
  }
  .timelineContent {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    align-items: center;
    align-content: space-around;
  }

  .diy-avatar{
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #C0C4CC;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 5px;
  }
</style>
