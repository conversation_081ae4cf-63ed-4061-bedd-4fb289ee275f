<template>
  <el-dialog :title="title" :visible.sync="dialog">
    <el-divider content-position="left">选择抄送人</el-divider>
    <el-checkbox-group
      v-model="groupData"
      @change="checkpeople"
      >
      <el-checkbox v-for="city in treeOptions" :label="city.nickName" :key="city.id">{{city.nickName}}</el-checkbox>
    </el-checkbox-group>
<!--    <el-divider content-position="left">选择抄送角色</el-divider>-->
<!--    <el-row>-->
<!--      <el-col :span="12">-->
<!--        <el-table-->
<!--          ref="roleTable"-->
<!--          v-loading="roleLoading"-->
<!--          :data="roleList"-->
<!--          size="mini"-->
<!--          :show-header="false"-->
<!--          @selection-change="handleRoleSelectionChange"-->
<!--        >-->
<!--          <el-table-column-->
<!--            type="selection"-->
<!--            width="55"-->
<!--          />-->
<!--          <el-table-column-->
<!--            label="角色"-->
<!--            width="120"-->
<!--            prop="name"-->
<!--          />-->
<!--        </el-table>-->
<!--        <pagination-->
<!--          v-show="roleTotal>0"-->
<!--          :total="roleTotal"-->
<!--          :pager-count="pagerCount"-->
<!--          small-->
<!--          :page.sync="roleQueryParams.pageNum"-->
<!--          :limit.sync="roleQueryParams.pageSize"-->
<!--          layout="prev, pager, next"-->
<!--          @pagination="getRoleList"-->
<!--        />-->
<!--      </el-col>-->
<!--      <el-col :span="12">-->
<!--        <div v-for="(role,index) in changeRole" style="display: flex;justify-content: space-around;align-items: center">-->
<!--          <div>{{ role.name }}</div>-->
<!--          <div class="el-icon-error" style="color:red" @click="removeRole(role.id)" />-->
<!--        </div>-->
<!--        <div v-if="changeRole.length < 1">-->
<!--          请选择抄送角色-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row>-->

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="cancelForm">取 消</el-button>
      <el-button size="small" type="primary" @click="submitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
// TODO 对接企业微信获取组织结构
import { getAllJob } from '@/api/system/job'
const dlcgCompanyApi = require('@/api/system/dlcgCompany')
export default {
  name: 'DepartComponent',
  data() {
    return {
      title: '',
      dialog: false,

      userLoading: false,

      oneLoading: false,
      changeOne: [],
      groupData:[],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      nodeName: undefined,
      treeOptions: undefined,
      pagerCount: 5,
      roleLoading: false,
      roleTotal: 0,
      roleQueryParams: {
        pageNum: 1,
        pageSize: 5
      },
      roleList: [],
      changeRole: []

    }
  },
  watch: {
    nodeName(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    doInit() {
      this.getOneList()
      // this.getRoleList()
    },
    cancelForm() {
      this.dialog = false
    },
    submitForm() {
      this.dialog = false
      const data = {
        cc: this.changeOne,
        role: this.changeRole
      }
      this.$emit('child-cc', data)
    },

    getRoleList() {
      this.roleLoading = true
      getAllJob().then(response => {
        this.roleList = response.data.rows
        this.roleTotal = response.data.total
      }).finally(() => {
        this.roleLoading = false
      })
    },
    handleRoleSelectionChange(val) {
      this.changeRole = val
    },
    removeRole(id) {
      this.changeRole = this.changeRole.filter(item => item.id !== id)
      for (let i = 0; i < this.roleList.length; i++) {
        if (this.roleList[i].id === id) {
          this.$refs.roleTable.toggleRowSelection(this.roleList[i], false)
          break
        }
      }
    },

    getOneList() {
      this.userLoading = true
      dlcgCompanyApi.treeselectUser().then(response => {
        console.log(response)
        this.treeOptions = response.data
      }).finally(() => {
        this.userLoading = false
      })
    },
    removeOne(id) {
      this.changeOne = this.changeOne.filter(item => item.id !== id)
      this.$refs.tree.setCheckedKeys(this.changeOne.map(item => item.nodeId))
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    //选择抄送人
    checkpeople(data){
      this.changeOne = []
      for (var a = 0;a<data.length;a++){
        var item = data[a]
        for (var b = 0;b<this.treeOptions.length;b++){
          var items = this.treeOptions[b]
          if (item === items.nickName){
            console.log(items)
            this.changeOne.push({
              id: items.userId,
              nodeId: items.userId,
              name: items.nickName,
              avatar: items.avatarPath
            })
          }
        }
      }
      console.log(this.changeOne)
    },
    // 节点单击事件
    handleNodeChange(data, checked, indeterminate) {
      if (data.nodeType === 2) {
        if (checked) {
          // 选中
          this.changeOne.push({
            id: data.businessId,
            nodeId: data.id,
            name: data.label,
            avatar: data.avatar
          })
        } else {
          // 未选中
          this.changeOne = this.changeOne.filter(item => item.nodeId !== data.id)
        }
      }
    }
  }
}
</script>

<style scoped>
.kh_item1 {
  margin: 5px 5px;
  cursor: pointer;
}
</style>
