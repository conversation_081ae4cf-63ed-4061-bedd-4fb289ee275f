<template>
  <div class="process-drawer">
    <el-dialog
      id="printDiglog"
      :title="title"
      :before-close="handleClose"
      :visible.sync="drawer"
      width="80%"
    >
      <div id="drawerBody"
           v-loading="loadingComponent">
        <!--startprint1-->

        <!-- 审批状态图片 -->
        <!--        <div style="position: absolute;right: 20px;top: 50px;z-index: 99999">-->
        <!--          <img width="150" height="150" src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1596003145760&di=5fd9d50ada578f10652a6d471e502bf8&imgtype=0&src=http%3A%2F%2Fku.90sjimg.com%2Felement_origin_min_pic%2F01%2F41%2F16%2F99573d2cce230db.jpg" alt="">-->
        <!--        </div>-->
        <div id="dataform">
          <!-- 表单渲染之前的插槽 -->
          <slot id="beforeFormSlot" name="beforeFormSlot"></slot>

          <!-- 审批内容 -->
          <div v-if="useComp">
            <component :is="comp" :datas="datas" :dept-code="deptCode"></component>
          </div>
          <div v-else>
            <!-- 表单 -->
            <es-form v-if="!!formSchema" ref="form" :schema="formSchema" v-model="formValue"></es-form>
          </div>

          <!-- 表单渲染之后的插槽 -->
          <slot name="afterFormSlot"></slot>
          <!-- 签字 -->

          <div style="margin-top:20px;display:flex;justify-content: space-around;">
            <div v-for="litem in spLogList" :key="litem.id">
              <span >{{ litem.nodeLabel }}:{{ litem.userName || '--' }}</span>
            </div>
          </div>
        </div>


        <!--endprint1-->
        <!-- 审批流程 -->
        <el-divider content-position="left" v-if="!spLogList || spLogList.length==0">审批流程</el-divider>
        <el-timeline id="timelineBody" v-if="!spLogList || spLogList.length==0">
          <!-- 发起申请 -->
          <el-timeline-item
            :timestamp="timeline.start.content"
            color="#67C23A"
            size="large"
            placement="top">
            <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
              <el-avatar shape="square" size="large" :src="timeline.start.item.avatar"></el-avatar>
              <div style="margin-left: 10px;color: #333333;">{{ timeline.start.item.name }}</div>
            </div>
          </el-timeline-item>

          <!-- 审批人 -->
          <el-timeline-item
            v-for="(activity, index) in timeline.sp"
            :timestamp="activity.content"
            :key="index"
            :color="activity.color"
            :size="activity.size"
            placement="top">
            <div style="display: flex;justify-content: space-between;">
              <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
                <el-avatar shape="square" size="large" :src="activity.item.avatar"></el-avatar>
                <div style="margin-left: 10px;color: #333333;">{{ activity.item.name }}</div>
              </div>
              <div style="display: flex;align-items: flex-end;flex-direction: column">
                <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{ activity.commentTime }}</div>
                <el-tooltip v-if="!!activity.comment && activity.comment.length > 22" effect="dark" placement="top">
                  <div slot="content">
                    <div v-html="activity.comment"></div>
                  </div>
                  <div style="font-size: 12px;color: #909399;cursor: default;">{{ activity.comment.substr(0, 20) }}...
                  </div>
                </el-tooltip>
                <div v-else style="font-size: 12px;color: #909399">{{ activity.comment }}</div>
              </div>
            </div>
          </el-timeline-item>

          <!-- 抄送人 -->
          <el-timeline-item
            :timestamp="timeline.ccs.content"
            :color="timeline.ccs.color"
            :size="timeline.ccs.size"
            placement="top">
            <el-row class="timelineContent">
              <div v-for="people in timeline.ccs.items" style="margin-right:10px">
                <el-avatar shape="square" size="large" :src="people.avatar"></el-avatar>
              </div>
            </el-row>
          </el-timeline-item>
        </el-timeline>

        <div v-if="btnIsShow" class="timelineContent" style="text-align: right;display: block;">
          <el-button size="small" @click="jujueHandle">拒 绝</el-button>
          <el-button size="small" type="primary" @click="tongyiHandle">同 意</el-button>
        </div>

        <el-dialog :title="approveDialogTitle" :visible.sync="approveDialog" append-to-body>
          <el-form ref="ruleForm" :model="approveDialogForm">
            <el-form-item prop="comment">
              <el-input v-model="approveDialogForm.comment" type="textarea" placeholder="请输入审批意见"></el-input>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="cancelForm">取 消</el-button>
            <el-button size="small" type="primary" @click="submitForm" :loading="loading">{{
                loading ? '提交中 ...' : '确 定'
              }}
            </el-button>
          </div>
        </el-dialog>
      </div>
      <div style="width: 100%;text-align: right;">
        <el-button type="primary" v-if="isShowPrite" @click="verifyPrintBefore">打印</el-button>
<!--        <el-button type="primary" @click="printApply">打印</el-button>-->
      </div>
    </el-dialog>
    <!--打印操作历史-->
    <el-dialog title="打印历史" :visible.sync="isShowPrintHistory">
      <el-table :data="printHistoryData">
        <el-table-column property="createByName" label="打印人"></el-table-column>
        <el-table-column property="createTime" label="打印时间"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isShowPrintHistory = false">取 消</el-button>
        <el-button type="primary" @click="printApply()">继续打印</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import esForm from "vue-easy-form";
import Vue from 'vue';
// import { checkProcessSuccessByCodeAndStatus } from '@/jslib/utils'
import {
  getSpLogList,
  taskGetNew
} from "@/api/system/onAccount.js";
import {getWuLiuSpPerspnListByProcessId} from '@/api/system/process'
const OperationLogApi = require('@/api/system/operationLog.js')
const processApi = require('@/api/system/process')

Vue.use(esForm)

export default {
  name: "WorkFlowProcessDiglog",
  props: {
    componentsWidth: {
      type: String,
      required: false,
      default: '80%'
    },
    deptCode: {
      type: String,
      required: false,
      default: ''
    },
  },
  data() {
    return {
      spLogList: [],
      title: '审批',
      drawer: false,
      loading: false,
      finishTime: null,
      isShowPrite: false, //是否显示打印按钮
      loadingComponent: false,

      timeline: {
        start: {
          content: '发起申请',
          item: {
            id: 0,
            name: '',
            avatar: ''
          }
        },
        sp: [],
        ccs: {
          content: '抄送(0人)',
          items: []
        }
      },

      formValue: {},
      formSchema: undefined,
      processId: undefined,
      statusId: undefined,
      viewName: '',
      onAccountNum: '',
      btnIsShow: false,
      isShowPrintHistory: false,
      printHistoryData: [],
      approveDialogTitle: '审批同意意见',
      approveDialog: false,
      approveDialogForm: {
        type: undefined,
        comment: undefined
      },

      // 自定义显示组件
      comp: resolve => require([`@/components/approval/DefaultComponent.vue`], resolve),
      useComp: false,
      datas: {},
      saveInvoiceAttachSendStatusByProcessId: false,
    };
  },
  methods: {
    loadSpListByProcessId(processId) {
      this.spLogList = []
      getWuLiuSpPerspnListByProcessId(processId,this.deptCode).then(res => {
        if (res.data ) {
          this.spLogList = res.data
        }
      })
    },
    loadFinishTimeByProcessIds(processId) {

      console.log('this.finishTime ===>',this.finishTime );
      this.datas.finishTime = this.finishTime;
      // getFinishTime(processId).then(res => {
      //   if (res.data && res.data.data) {
      //     // this.finishTime = res.data.data
      //     this.datas.finishTime = res.data.data
      //   }
      // })
    },
    saveInvoiceAttachSendStatusByProcessIdSubmit() {
      console.log('saveInvoiceAttachSendStatusByProcessIdSubmit-1761')
      this.saveInvoiceAttachSendStatusByProcessId = true;
    },
    doInit() {
      this.datas = {}
      if (!this.processId) {
        this.$message({showClose: true, message: '请传递流程Id', type: 'error'})
        return
      }
      this.loadSpListByProcessId(this.processId)
      this.loadingComponent = true;
      taskGetNew(this.processId,this.deptCode).then(res => {
        if (res.data.formSchema) this.formSchema = JSON.parse(res.data.formSchema)
        if (res.data.formValue && res.data.formValue.formValue) this.formValue = JSON.parse(res.data.formValue.formValue)
        this.timeline.start = res.data.spFlow.start
        this.timeline.sp = res.data.spFlow.sp
        this.statusId = res.data.nodeId
        this.timeline.ccs = res.data.spFlow.ccs
        this.btnIsShow = res.data.process
        this.viewName = res.data.viewName
        // 自定义显示组件
        if (!!res.data.useComponent && res.data.useComponent && !!res.data.viewName) {
          this.useComp = true
          if (!!res.data.globalParams) {
            // this.datas = Object({},this.datas,res.data.globalParams)
            this.datas = res.data.globalParams
            this.datas.mgProcessValue = res.data.formValue
            let applyObjData =  JSON.parse(res.data.globalParams.params2)
            this.loadFinishTimeByProcessIds(this.processId);
            let ship_line = applyObjData["ShipLine"];
            this.onAccountNum = ship_line["onAccountNum"] ? ship_line["onAccountNum"] : "";
          }
          this.comp = resolve => require([`@/components/approval/${res.data.viewName}.vue`], resolve)
        }
        this.$emit('onCallback', res.data.globalParams)
      }).catch(e => {

      }).finally(() => {
        this.loadingComponent = false
      });
    },
    handleClose(done) {
      done();
    },
    cancelForm() {
      this.approveDialog = false
    },
    submitForm() {
      // 判断拒绝 并且未添加原因
      if (this.approveDialogForm.type == 3 && !this.approveDialogForm.comment) {
        this.msgError("请输入拒绝原因")
        return
      }
      this.loading = true;
      this.approveDialogForm.processId = this.processId;
      this.approveDialogForm.nodeId = this.statusId;
      this.approveDialogForm.formValue = this.formValue
      processApi.handleProcess(this.approveDialogForm).then(res => {
        this.msgSuccess("提交成功")
        this.approveDialog = false
        this.drawer = false
        this.$emit('refresh')
      }).finally(() => {
        this.loading = false
      })
    },
    jujueHandle() {
      this.approveDialogTitle = '审批拒绝意见'
      this.approveDialog = true
      this.approveDialogForm = {}
      this.approveDialogForm.type = 3
    },
    verifyPrintBefore() {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      OperationLogApi.query({
        // type = 1：查看合值
        type: 2,
        params1: this.datas.params4
      }).then(res => {
        if (res.content.length < 1) {
          // 首次打印
          this.printApply()
          return
        }
        // 以前打印过，展示操作历史
        this.isShowPrintHistory = true
        this.printHistoryData = res.content
      }).catch(err => {
        // nothing.
      }).finally(_ => {
        loading.close()
      })
    },
    printHtmlId(div) {

      // const html = document.querySelector('#' + id).innerHTML

      // 新建一个 DOM
      // const div = document.createElement('div')
      // const printDOMID = 'printDOMElement'
      // div.id = printDOMID
      // div.innerHTML = html

      // 提取第一个表格的内容 即表头
      const ths = div.querySelectorAll('.el-table__header-wrapper th')
      const ThsTextArry = []
      for (let i = 0, len = ths.length; i < len; i++) {
        if (ths[i].innerText !== '') ThsTextArry.push(ths[i].innerText)
      }

      // 删除多余的表头
      div.querySelector('.hidden-columns').remove()
      // 第一个表格的内容提取出来后已经没用了 删掉
      div.querySelector('.el-table__header-wrapper').remove()

      // 将第一个表格的内容插入到第二个表格
      let newHTML = '<thead><tr>'
      for (let i = 0, len = ThsTextArry.length; i < len; i++) {
        newHTML += '<th style="text-align: center;" >' + ThsTextArry[i] + '</th>'
      }

      newHTML += '</tr></thead>'
      console.log('newHTML', newHTML)
      // 删除 colgroup
      div.querySelector('.el-table__body-wrapper colgroup').remove()
      // div.querySelector('.el-table__body-wrapper table').insertAdjacentHTML('afterbegin', newHTML)
      // table>tbody 插入 thead  变成 table>thead>tbody
      div.querySelector('.el-table__body-wrapper table').insertAdjacentHTML('afterbegin', newHTML)
      // 宽度修改
      div.querySelector('.el-table__body-wrapper table').style.width = '100%'
      // border = 1
      // div.querySelector('.el-table__body-wrapper table').style.border = '1px solid #ebeef5'
      div.querySelector('.el-table__body-wrapper table').setAttribute('border', '1')

      return div.innerHTML
    },

    printApply() {
      var input = window.document.getElementsByTagName('input')
      for (var i = 0; i < input.length; i++) {
        input[i].setAttribute("value", input[i].value)
      }
      this.$nextTick(function () {
        let str = `<div style="margin-left:180px;font-size:22px;">`
        const defEle = window.document.getElementById("dataform")
        const div = document.createElement('div')
        div.innerHTML = defEle.innerHTML
        // 修改表格宽度
        // 多个table .el-table__header-wrapper 内容放到  .el-table__body-wrapper 里
        // const table = defEle.getElementsByTagName('table')
        const tables = div.querySelectorAll('.el-table')
        for (let i = 0; i < tables.length; i++) {
          // table[i].style.width = '100%'
          // table[i].style.border = '1px solid #ebeef5'
          this.printHtmlId(tables[i])
        }
        str += div.innerHTML
        // str += window.document.getElementById("dataform").innerHTML

        // str = str.replace(/width: 100%;/g, 'width: 100%;')
        str += `</div>
          <style>
            .print-hidden{
              display: none;
            }
            table {
              border-collapse: collapse;
              border-spacing: 0;
              width: 100%;
              border: 1px solid #000;
            }
            th, td {
              text-align: center;
              padding: 8px;
              border: 1px solid #000;
            }
          </style>
          `
        // 打印
        this.$XPrint({
          sheetName: '打印改账 ' + this.onAccountNum,
          content: str,
          beforePrintMethod: ({ content }) => {
            // 拦截打印之前，记录操作日志,应该监听打印后的操作，但是没有找到合适的方法
            OperationLogApi.add({
              type: 2,
              params1: this.datas.params4
            }).then(res => {
              // save success
            }).catch(err => {
              // nothing...
            }).finally(_ => {
              this.isShowPrintHistory = false
            })
            return content
          }
        })
        console.log("aaaaa====>asadasdasdadsad")
        // window.document.body.innerHTML=str;
        // window.print();
        // // this.preview(1)
        // window.location.reload()
      })
    },
    preview(oper) {
      if (oper < 10) {
        var bdhtml = window.document.body.innerHTML;//获取当前页的html代码
        var sprnstr = "<!--startprint" + oper + "-->";//设置打印开始区域
        var eprnstr = "<!--endprint" + oper + "-->";//设置打印结束区域
        var prnhtml = bdhtml.substring(bdhtml.indexOf(sprnstr) + 18); //从开始代码向后取html
        var prnhtml = prnhtml.substring(0, prnhtml.indexOf(eprnstr));//从结束代码向前取html
        window.document.body.innerHTML = prnhtml;
        window.print();
        window.document.body.innerHTML = bdhtml;
      } else {
        window.print();
      }
    },
  }
}
</script>
<style>
.el-dialog {
  overflow: scroll
}

.el-dialog :focus {
  outline: 0;
}
</style>
<style scoped>
.process-drawer {
  text-align: left;
}

#drawerBody {
  padding: 10px;
}

#timelineBody > * {
  text-align: left !important;
}

.wh40 {
  width: 40px;
  height: 42px;
  padding: 0 !important;
  margin: 0 !important;
}

.timelineContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  align-content: space-around;
}
</style>
