<template>
  <div class="process-drawer">
    <el-dialog
      id="printDiglog"
      :title="title"
      :before-close="handleClose"
      :visible.sync="drawer"
      width="80%"
    >
      <div
        id="drawerBody"
        v-loading="loadingComponent"
      >
        <!--startprint1-->

        <!-- 审批状态图片 -->
        <!--        <div style="position: absolute;right: 20px;top: 50px;z-index: 99999">-->
        <!--          <img width="150" height="150" src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1596003145760&di=5fd9d50ada578f10652a6d471e502bf8&imgtype=0&src=http%3A%2F%2Fku.90sjimg.com%2Felement_origin_min_pic%2F01%2F41%2F16%2F99573d2cce230db.jpg" alt="">-->
        <!--        </div>-->
        <div id="dataform">
          <!-- 表单渲染之前的插槽 -->
          <slot id="beforeFormSlot" name="beforeFormSlot" />

          <!-- 审批内容 -->
          <div v-if="useComp">
            <component :is="comp" :datas="datas" />
          </div>
          <div v-else>
            <!-- 表单 -->
            <es-form v-if="!!formSchema" ref="form" v-model="formValue" :schema="formSchema" />
          </div>

          <!-- 表单渲染之后的插槽 -->
          <slot name="afterFormSlot" />
        </div>
        <!--endprint1-->
        <!-- 审批流程 -->
        <el-divider content-position="left">审批流程</el-divider>
        <el-timeline id="timelineBody">
          <!-- 发起申请 -->
          <el-timeline-item
            :timestamp="timeline.start.content"
            color="#67C23A"
            size="large"
            placement="top"
          >
            <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
              <el-avatar shape="square" size="large" :src="timeline.start.item.avatar" />
              <div style="margin-left: 10px;color: #333333;">{{ timeline.start.item.name }}</div>
            </div>
          </el-timeline-item>

          <!-- 审批人 -->
          <el-timeline-item
            v-for="(activity, index) in timeline.sp"
            :key="index"
            :timestamp="activity.content"
            :color="activity.color"
            :size="activity.size"
            placement="top"
          >
            <div style="display: flex;justify-content: space-between;">
              <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
                <el-avatar shape="square" size="large" :src="activity.item.avatar" />
                <div style="margin-left: 10px;color: #333333;">{{ activity.item.name }}</div>
              </div>
              <div style="display: flex;align-items: flex-end;flex-direction: column">
                <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{ activity.commentTime }}</div>
                <el-tooltip v-if="!!activity.comment && activity.comment.length > 22" effect="dark" placement="top">
                  <div slot="content">
                    <div v-html="activity.comment" />
                  </div>
                  <div style="font-size: 12px;color: #909399;cursor: default;">{{ activity.comment.substr(0,20) }}...</div>
                </el-tooltip>
                <div v-else style="font-size: 12px;color: #909399">{{ activity.comment }}</div>
              </div>
            </div>
          </el-timeline-item>

          <!-- 抄送人 -->
          <el-timeline-item
            :timestamp="timeline.ccs.content"
            :color="timeline.ccs.color"
            :size="timeline.ccs.size"
            placement="top"
          >
            <el-row class="timelineContent">
              <div v-for="people in timeline.ccs.items" style="margin-right:10px">
                <el-avatar shape="square" size="large" :src="people.avatar" />
              </div>
            </el-row>
          </el-timeline-item>
        </el-timeline>

        <div v-if="btnIsShow" class="timelineContent" style="text-align: right;display: block;">
          <el-button size="small" @click="jujueHandle">拒 绝</el-button>
          <el-button size="small" type="primary" @click="tongyiHandle">同 意</el-button>
        </div>

        <el-dialog :title="approveDialogTitle" :visible.sync="approveDialog" append-to-body>
          <el-form ref="ruleForm" :model="approveDialogForm">
            <el-form-item prop="comment">
              <el-input v-model="approveDialogForm.comment" type="textarea" placeholder="请输入审批意见" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="cancelForm">取 消</el-button>
            <el-button size="small" type="primary" :loading="loading" @click="submitForm">{{ loading ? '提交中 ...' : '确 定' }}</el-button>
          </div>
        </el-dialog>
      </div>
      <div style="width: 100%;text-align: right;">
        <el-button type="primary" @click="printApply">打印</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import esForm from 'vue-easy-form'
import Vue from 'vue'
const processApi = require('@/api/system/process')

Vue.use(esForm)

export default {
  name: 'WorkFlowProcessDiglog',
  props: {
    componentsWidth: {
      type: String,
      required: false,
      default: '80%'
    }
  },
  data() {
    return {
      title: '审批',
      drawer: false,
      loading: false,
      loadingComponent: false,

      timeline: {
        start: {
          content: '发起申请',
          item: {
            id: 0,
            name: '',
            avatar: ''
          }
        },
        sp: [],
        ccs: {
          content: '抄送(0人)',
          items: []
        }
      },

      formValue: {},
      formSchema: undefined,
      processId: undefined,
      statusId: undefined,

      btnIsShow: false,

      approveDialogTitle: '审批同意意见',
      approveDialog: false,
      approveDialogForm: {
        type: undefined,
        comment: '审批通过'
      },

      // 自定义显示组件
      comp: resolve => require([`@/components/approval/DefaultComponent.vue`], resolve),
      useComp: false,
      datas: {}

    }
  },
  methods: {
    doInit() {
      if (!this.processId) {
        return
      }

      this.loadingComponent = true
      processApi.taskGet(this.processId).then(res => {
        if (res.data.formSchema) this.formSchema = JSON.parse(res.data.formSchema)
        if (res.data.formValue && res.data.formValue.formValue) this.formValue = JSON.parse(res.data.formValue.formValue)
        this.timeline.start = res.data.spFlow.start
        this.timeline.sp = res.data.spFlow.sp
        this.statusId = res.data.nodeId
        this.timeline.ccs = res.data.spFlow.ccs
        this.btnIsShow = res.data.process

        // 自定义显示组件
        if (!!res.data.useComponent && res.data.useComponent && !!res.data.viewName) {
          this.useComp = true
          if (res.data.globalParams) {
            this.datas = res.data.globalParams
          }
          this.comp = resolve => require([`@/components/approval/${res.data.viewName}.vue`], resolve)
        }

        this.$emit('onCallback', res.data.globalParams)
      }).catch(e => {

      }).finally(() => {
        this.loadingComponent = false
      })
    },
    handleClose(done) {
      done()
    },
    cancelForm() {
      this.approveDialog = false
    },
    submitForm() {
       // 判断拒绝 并且未添加原因
      if (this.approveDialogForm.type == 3 && !this.approveDialogForm.comment) {
        this.$message({ showClose: true, message: '请输入拒绝原因', type: 'error' })
        return
      }
      this.loading = true
      this.approveDialogForm.processId = this.processId
      this.approveDialogForm.nodeId = this.statusId
      this.approveDialogForm.formValue = this.formValue
      processApi.handleProcess(this.approveDialogForm).then(res => {
        this.msgSuccess('提交成功')
        this.approveDialog = false
        this.drawer = false
        this.$emit('refresh')
      }).finally(() => {
        this.loading = false
      })
    },
    jujueHandle() {
      this.approveDialogTitle = '审批拒绝意见'
      this.approveDialog = true
      this.approveDialogForm = {
        comment: '',
        type: 3
      }
      // this.approveDialogForm.type = 3
    },
    tongyiHandle() {
      this.approveDialogTitle = '审批同意意见'
      this.approveDialog = true
      this.approveDialogForm = {
        comment: '审批通过',
        type: 2
      }
      // this.approveDialogForm.type = 2
    },
    printApply() {
      var str = window.document.getElementById('dataform').innerHTML
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    },
    preview(oper) {
      if (oper < 10) {
        var bdhtml = window.document.body.innerHTML// 获取当前页的html代码
        var sprnstr = '<!--startprint' + oper + '-->'// 设置打印开始区域
        var eprnstr = '<!--endprint' + oper + '-->'// 设置打印结束区域
        var prnhtml = bdhtml.substring(bdhtml.indexOf(sprnstr) + 18) // 从开始代码向后取html
        var prnhtml = prnhtml.substring(0, prnhtml.indexOf(eprnstr))// 从结束代码向前取html
        window.document.body.innerHTML = prnhtml
        window.print()
        window.document.body.innerHTML = bdhtml
      } else {
        window.print()
      }
    }
  }
}
</script>
<style>
  .el-dialog{
    overflow: scroll
  }
  .el-dialog :focus{
    outline:0;
  }
</style>
<style scoped>
  .process-drawer{
    text-align: left;
  }
  #drawerBody{
    padding: 10px;
  }
  #timelineBody > * {
    text-align: left !important;
  }
  .wh40{
    width: 40px;
    height: 42px;
    padding: 0 !important;
    margin: 0 !important;
  }
  .timelineContent {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    align-content: space-around;
  }
</style>
