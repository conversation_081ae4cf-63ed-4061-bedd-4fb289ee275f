<template>
  <section class="company-cert-container">
    <!-- 公司帐套管理区域 -->
    <div class="company-account-section">
      <div class="section-header">
        <h2 class="section-title">公司帐套管理</h2>
        <el-button type="primary" size="medium" icon="el-icon-edit" @click="showCompanyAccountDialog">
          管理帐套
        </el-button>
      </div>

      <!-- 层级切换按钮 -->
      <div class="hierarchy-switches">
        <!-- 第一级：公司选择 -->
        <div class="switch-level">
          <span class="switch-label">公司：</span>
          <el-select v-model="selectedCompanyId" placeholder="选择公司" @change="onCompanyChange" clearable>
            <el-option
              v-for="company in companyOptions"
              :key="company.companyId"
              :label="company.companyName"
              :value="company.companyId">
            </el-option>
          </el-select>
        </div>

        <!-- 第二级：核算类型选择 -->
        <div class="switch-level" v-if="selectedCompanyId">
          <span class="switch-label">核算类型：</span>
          <el-select v-model="selectedAccModel" placeholder="选择核算类型" @change="onAccModelChange" clearable>
            <el-option label="税务核算" :value="1"></el-option>
            <el-option label="股东核算" :value="2"></el-option>
          </el-select>
        </div>

        <!-- 第三级：帐套选择 -->
        <div class="switch-level" v-if="selectedCompanyId && selectedAccModel">
          <span class="switch-label">帐套：</span>
          <el-select v-model="selectedAppCode" placeholder="选择帐套" @change="onAppCodeChange" clearable>
            <el-option
              v-for="app in filteredAppOptions"
              :key="app.appCode"
              :label="app.appName"
              :value="app.appCode">
            </el-option>
          </el-select>
        </div>
      </div>

      <!-- 当前选择信息展示 -->
      <div class="current-selection" v-if="currentSelection.companyName">
        <div class="selection-info">
          <span class="info-item">
            <strong>公司：</strong>{{ currentSelection.companyName }}
          </span>
          <span class="info-item">
            <strong>核算类型：</strong>{{ currentSelection.accModelText }}
          </span>
          <span class="info-item" v-if="currentSelection.appName">
            <strong>帐套：</strong>{{ currentSelection.appName }}
          </span>
        </div>
      </div>

      <!-- 公司帐套列表展示 -->
      <div class="company-account-list">
        <h3 class="list-title">公司帐套列表</h3>
        <div class="account-cards-grid">
          <div
            v-for="item in filteredTokenCompanyList"
            :key="item.id"
            class="account-card"
            :class="{ 'selected': isAccountSelected(item) }"
            @click="selectAccount(item)"
          >
            <div class="account-card-header">
              <h4 class="company-name">{{ item.companyName }}</h4>
              <el-tag :type="item.accModel === 1 ? 'success' : 'warning'" size="small">
                {{ item.accModel === 1 ? '税务核算' : '股东核算' }}
              </el-tag>
            </div>
            <div class="account-card-body">
              <div class="account-info">
                <div class="info-row">
                  <span class="label">帐套名称：</span>
                  <span class="value">{{ item.appName }}</span>
                </div>
                <div class="info-row">
                  <span class="label">帐套编码：</span>
                  <span class="value">{{ item.appCode }}</span>
                </div>
                <div class="info-row">
                  <span class="label">账簿ID：</span>
                  <span class="value">{{ item.bookid }}</span>
                </div>
              </div>
            </div>
            <div class="account-card-footer">
              <span class="create-time">{{ formatTime(item.createTime) }}</span>
              <div class="card-actions">
                <el-button type="text" size="mini" @click.stop="editCompanyAccount(item)">
                  编辑
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 公司帐套管理对话框 -->
    <el-dialog
      title="公司帐套管理"
      :visible.sync="companyAccountDialogVisible"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="company-account-management">
        <!-- 公司帐套列表 -->
        <div class="account-list-section">
          <div class="list-header">
            <h3>公司帐套列表</h3>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addCompanyAccount">
              添加帐套
            </el-button>
          </div>

          <el-table :data="tokenCompanyList" border style="width: 100%" max-height="400">
            <el-table-column prop="companyName" label="公司名称" width="200" show-overflow-tooltip />
            <el-table-column prop="appName" label="帐套名称" width="150" />
            <el-table-column prop="appCode" label="帐套编码" width="120" />
            <el-table-column prop="accModel" label="核算类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.accModel === 1 ? 'success' : 'warning'">
                  {{ scope.row.accModel === 1 ? '税务核算' : '股东核算' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="bookid" label="账簿ID" width="150" show-overflow-tooltip />
            <el-table-column prop="orderNum" label="排序" width="80" />
            <el-table-column prop="createTime" label="创建时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="editCompanyAccount(scope.row)">
                  编辑
                </el-button>
                <el-button type="text" size="small" style="color: #f56c6c;" @click="deleteCompanyAccount(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="companyAccountDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 帐套编辑对话框 -->
    <el-dialog
      :title="accountDialogMode === 'add' ? '添加帐套' : '编辑帐套'"
      :visible.sync="accountDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="accountForm"
        :model="accountForm"
        :rules="accountRules"
        label-width="120px"
      >
        <el-form-item label="公司ID" prop="companyId">
          <el-input v-model="accountForm.companyId" placeholder="请输入公司ID" />
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="accountForm.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="帐套编码" prop="appCode">
          <el-input v-model="accountForm.appCode" placeholder="请输入帐套编码" />
        </el-form-item>
        <el-form-item label="帐套名称" prop="appName">
          <el-input v-model="accountForm.appName" placeholder="请输入帐套名称" />
        </el-form-item>
        <el-form-item label="账簿ID" prop="bookid">
          <el-input v-model="accountForm.bookid" placeholder="请输入账簿ID" />
        </el-form-item>
        <el-form-item label="核算类型" prop="accModel">
          <el-select v-model="accountForm.accModel" placeholder="请选择核算类型">
            <el-option label="税务核算" :value="1" />
            <el-option label="股东核算" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序号" prop="orderNum">
          <el-input-number v-model="accountForm.orderNum" :min="1" placeholder="排序号" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="accountDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAccount" :loading="saving">
          {{ accountDialogMode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import { getTokenCompanyList, updateTokenCompany } from '@/api/system/baseInit'

export default {
  name: 'CompanyCert',
  data() {
    return {
      // 公司帐套相关数据
      tokenCompanyList: [],
      selectedCompanyId: '',
      selectedAccModel: null,
      selectedAppCode: '',
      companyOptions: [],
      filteredAppOptions: [],
      currentSelection: {
        companyName: '',
        accModelText: '',
        appName: ''
      },
      selectedAccountItem: null, // 当前选中的帐套项

      // 公司帐套管理对话框
      companyAccountDialogVisible: false,
      accountDialogVisible: false,
      accountDialogMode: 'add', // 'add' | 'edit'
      accountForm: {
        id: null,
        companyId: '',
        companyName: '',
        appCode: '',
        appName: '',
        bookid: '',
        accModel: 1,
        orderNum: 1
      },
      accountRules: {
        companyId: [
          { required: true, message: '请输入公司ID', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' }
        ],
        appCode: [
          { required: true, message: '请输入帐套编码', trigger: 'blur' }
        ],
        appName: [
          { required: true, message: '请输入帐套名称', trigger: 'blur' }
        ],
        bookid: [
          { required: true, message: '请输入账簿ID', trigger: 'blur' }
        ],
        accModel: [
          { required: true, message: '请选择核算类型', trigger: 'change' }
        ],
        orderNum: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },

      saving: false
    }
  },
  computed: {
    // 过滤后的公司帐套列表
    filteredTokenCompanyList() {
      if (!this.selectedCompanyId && !this.selectedAccModel && !this.selectedAppCode) {
        // 没有任何选择时，显示所有数据
        return this.tokenCompanyList
      }

      return this.tokenCompanyList.filter(item => {
        let match = true

        if (this.selectedCompanyId) {
          match = match && item.companyId === this.selectedCompanyId
        }

        if (this.selectedAccModel !== null) {
          match = match && item.accModel === this.selectedAccModel
        }

        if (this.selectedAppCode) {
          match = match && item.appCode === this.selectedAppCode
        }

        return match
      })
    }
  },
  created() {
    this.loadTokenCompanyList()
  },
  methods: {
    loadTokenCompanyList(){
      getTokenCompanyList().then(res=>{
        if (res && res.list) {
          this.tokenCompanyList = res.list
          this.processCompanyData()
        }
      })
    },

    // 处理公司数据，生成选项列表
    processCompanyData() {
      // 生成公司选项（去重）
      const companyMap = new Map()
      this.tokenCompanyList.forEach(item => {
        if (!companyMap.has(item.companyId)) {
          companyMap.set(item.companyId, {
            companyId: item.companyId,
            companyName: item.companyName
          })
        }
      })
      this.companyOptions = Array.from(companyMap.values())
    },

    // 公司选择变化
    onCompanyChange() {
      this.selectedAccModel = null
      this.selectedAppCode = ''
      this.filteredAppOptions = []
      this.updateCurrentSelection()
    },

    // 核算类型选择变化
    onAccModelChange() {
      this.selectedAppCode = ''
      this.updateFilteredAppOptions()
      this.updateCurrentSelection()
    },

    // 帐套选择变化
    onAppCodeChange() {
      this.updateCurrentSelection()
    },

    // 更新过滤后的帐套选项
    updateFilteredAppOptions() {
      if (this.selectedCompanyId && this.selectedAccModel !== null) {
        const filteredApps = this.tokenCompanyList.filter(item =>
          item.companyId === this.selectedCompanyId &&
          item.accModel === this.selectedAccModel
        )

        // 去重帐套
        const appMap = new Map()
        filteredApps.forEach(item => {
          if (!appMap.has(item.appCode)) {
            appMap.set(item.appCode, {
              appCode: item.appCode,
              appName: item.appName
            })
          }
        })
        this.filteredAppOptions = Array.from(appMap.values())
      } else {
        this.filteredAppOptions = []
      }
    },

    // 更新当前选择信息
    updateCurrentSelection() {
      const company = this.companyOptions.find(c => c.companyId === this.selectedCompanyId)
      const app = this.filteredAppOptions.find(a => a.appCode === this.selectedAppCode)

      this.currentSelection = {
        companyName: company ? company.companyName : '',
        accModelText: this.selectedAccModel === 1 ? '税务核算' : this.selectedAccModel === 2 ? '股东核算' : '',
        appName: app ? app.appName : ''
      }
    },

    // 选择帐套
    selectAccount(item) {
      this.selectedAccountItem = item
      // 同步更新选择器的值
      this.selectedCompanyId = item.companyId
      this.selectedAccModel = item.accModel
      this.selectedAppCode = item.appCode
      this.updateCurrentSelection()
    },

    // 判断帐套是否被选中
    isAccountSelected(item) {
      return this.selectedAccountItem && this.selectedAccountItem.id === item.id
    },

    // 显示公司帐套管理对话框
    showCompanyAccountDialog() {
      this.companyAccountDialogVisible = true
    },

    // 添加帐套
    addCompanyAccount() {
      this.accountDialogMode = 'add'
      this.resetAccountForm()
      this.accountDialogVisible = true
    },

    // 编辑帐套
    editCompanyAccount(account) {
      this.accountDialogMode = 'edit'
      this.accountForm = {
        id: account.id,
        companyId: account.companyId,
        companyName: account.companyName,
        appCode: account.appCode,
        appName: account.appName,
        bookid: account.bookid,
        accModel: account.accModel,
        orderNum: account.orderNum
      }
      this.accountDialogVisible = true
    },

    // 删除帐套
    deleteCompanyAccount(account) {
      this.$confirm('确定要删除这个帐套吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用删除API
        this.$message.success('删除成功')
        this.loadTokenCompanyList()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 重置帐套表单
    resetAccountForm() {
      this.accountForm = {
        id: null,
        companyId: '',
        companyName: '',
        appCode: '',
        appName: '',
        bookid: '',
        accModel: 1,
        orderNum: 1
      }
      if (this.$refs.accountForm) {
        this.$refs.accountForm.clearValidate()
      }
    },

    // 保存帐套
    saveAccount() {
      this.$refs.accountForm.validate((valid) => {
        if (valid) {
          this.saving = true

          // 这里应该调用保存或更新API
          const apiCall = this.accountDialogMode === 'add'
            ? updateTokenCompany(this.accountForm) // 使用现有的API
            : updateTokenCompany(this.accountForm)

          apiCall.then(() => {
            this.$message.success(this.accountDialogMode === 'add' ? '添加成功' : '保存成功')
            this.accountDialogVisible = false
            this.loadTokenCompanyList()
          }).catch(err => {
            this.$message.error(err.message || '操作失败')
          }).finally(() => {
            this.saving = false
          })
        }
      })
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return time.replace(/T/, ' ').replace(/\.\d{3}Z$/, '')
    }
  }
}
</script>

<style scoped>
.company-cert-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 公司帐套管理区域样式 */
.company-account-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.hierarchy-switches {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.switch-level {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.current-selection {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.selection-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.info-item {
  font-size: 14px;
  color: #606266;
}

.info-item strong {
  color: #303133;
  margin-right: 4px;
}

/* 公司帐套列表样式 */
.company-account-list {
  margin-top: 20px;
}

.list-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.account-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.account-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.account-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.account-card.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.account-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.company-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-card-body {
  margin-bottom: 12px;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.info-row .label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.info-row .value {
  color: #606266;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.create-time {
  font-size: 12px;
  color: #c0c4cc;
}

.card-actions {
  display: flex;
  gap: 8px;
}

/* 公司帐套管理对话框样式 */
.company-account-management {
  padding: 0;
}

.account-list-section {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}
</style>
