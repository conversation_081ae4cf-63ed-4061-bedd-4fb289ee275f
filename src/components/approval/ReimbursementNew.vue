<template>
  <section id="dataform" >
    <div style="width: 100%;text-align: right;margin-top: -42px;margin-bottom: 10px">
      <el-button type="primary" @click="printApply">打印</el-button>
    </div>
    <table rules="all" frame="box" class="record-table">
      <tr>
        <td style="text-align: left" colspan="12">
          <div style="text-align: center; 1.4em">
            {{ title }}
          </div>
          <div style=";text-align: left">
            <span style="padding-left: 20%">{{ datas.year }}年{{ datas.month }}月{{ datas.ri }}日</span>
            <span style="padding-left: 50%">附件 {{ filelength }} 张</span>
          </div>
        </td>
      </tr>
      <!--    <div style="width: 100%;text-align: left">-->
      <tr>
        <td style="width: 35%;text-align: center" rowspan="2">品名及用途</td>
        <td style="width: 45%;text-align: center" colspan="10"> 金额</td>
        <td style="width: 20%;text-align: center" rowspan="2"> 原始凭证</td>
      </tr>
      <tr>
        <td> 千</td>
        <td> 百</td>
        <td> 十</td>
        <td> 万</td>
        <td> 千</td>
        <td> 百</td>
        <td> 十</td>
        <td> 元</td>
        <td> 角</td>
        <td> 分</td>
      </tr>
      <tr v-for="item in moneydetail">
        <td style="text-align: center">
          <div >
            <span class="pt-show">{{ (item.show ? item.show : '') }}</span>
            <span class="pt-print">{{ (item.show ? item.show : '').replace(/\#.*\#/g, '') }}</span>
          </div>
        </td>
        <td>{{ item.qianwan }}</td>
        <td>{{ item.baiwan }}</td>
        <td>{{ item.shiwan }}</td>
        <td>{{ item.wan }}</td>
        <td>{{ item.qian }}</td>
        <td>{{ item.bai }}</td>
        <td>{{ item.shi }}</td>
        <td>{{ item.yuan }}</td>
        <td>{{ item.mao }}</td>
        <td>{{ item.fen }}</td>
        <td style="text-align: center">{{ item.fileNum }}</td>
      </tr>

      <tr>
        <td style="text-align: center">总计</td>
        <td>{{ allmoney.qianwan }}</td>
        <td>{{ allmoney.baiwan }}</td>
        <td>{{ allmoney.shiwan }}</td>
        <td>{{ allmoney.wan }}</td>
        <td>{{ allmoney.qian }}</td>
        <td>{{ allmoney.bai }}</td>
        <td>{{ allmoney.shi }}</td>
        <td>{{ allmoney.yuan }}</td>
        <td>{{ allmoney.mao }}</td>
        <td>{{ allmoney.fen }}</td>
        <td style="text-align: center" />
      </tr>
      <tr>
        <td style="text-align: center" rowspan="3">报销人银行信息</td>
        <td style="text-align: center" colspan="2">户名</td>
        <td style="width: 100%;text-align: left;padding-left: 10px;" colspan="9">{{ datas.opositeUnit }}</td>
      </tr>
      <tr>
        <td style="text-align: center" colspan="2">银行卡号</td>
        <td style="width: 100%;text-align: left;padding-left: 10px;" colspan="9">{{ datas.cardNum }}</td>
      </tr>
      <tr>
        <td style="text-align: center" colspan="2">开户行</td>
        <td style="width: 100%;text-align: left;padding-left: 10px;" colspan="9">{{ datas.BankNum }}</td>

      </tr>

      <tr>
        <td style="width:100%;text-align:left;" colspan="12">
          金额合计（大写）人民币：{{ datas.sumChinese }}
        </td>
      </tr>
      <tr>
        <td style="width: 100%;height: 80px;text-align: left" colspan="12">
          <div style="margin-top: -30px">
            备注：
            <span class="pt-show">{{ (datas.reason ? datas.reason : '') }}</span>
            <span class="pt-print">{{ (datas.reason ? datas.reason : '').replace(/\#.*\#/g, '') }}</span>
          </div>
        </td>
      </tr>
        <!-- 付款信息 -->
        <PaymentWaterList :data-obj="datas"  tdColspans="1,2,9"></PaymentWaterList>

      <tr v-show="splist && splist.length > 0">
        <td style="height: 60px;text-align: left" colspan="8">
          <div>
            <span>报销人：{{ splist[0] }} </span>
            <span style="margin-left: 15%">部门负责人：{{ splist[1] }} </span>
            <span style="margin-left: 15%">会计审核：{{ splist[2] }} </span>
          </div>
        </td>
        <td style="height: 60px;text-align: left" colspan="4" rowspan="2">
          <div style="margin-left: 15px">
            总裁：{{ splist[6] == '洪安前' ? splist[6]:" " }}
          </div>
        </td>
      </tr>
      <tr v-show="splist && splist.length > 0">
        <td style="height: 60px;text-align: left" colspan="8">
          <div>
            <span>财务经理：{{ splist[3] }} </span>
            <span style="margin-left: 15%">总经理：{{ splist[4] }} </span>
            <span style="margin-left: 15%">财务总监：{{ splist[5] }} </span>
          </div>
        </td>
      </tr>

      <tr v-show="spLabelList && spLabelList.length > 0">
        <td style="height: 60px;text-align: left" colspan="8">
          <div>
            <span v-for="(sitem,sindex) in spLabelOne" :key="sindex" :style="{'margin-left':sindex==0?'':'15%'}">{{sitem.label}}：{{ sitem.userName }} </span>
          </div>
        </td>
        <td style="height: 60px;text-align: left" colspan="4" rowspan="2">
          <div style="margin-left: 15px">
           总裁：{{ spLabelEnd.label && spLabelEnd.label=='总裁'? spLabelEnd.userName:" " }}
          </div>
        </td>
      </tr>
      <tr v-show="spLabelList && spLabelList.length > 0">
        <td style="height: 60px;text-align: left" colspan="8">
          <div>
            <span v-for="(sitem,sindex) in spLabelTwo" :key="sindex" :style="{'margin-left':sindex==0?'':'15%'}">{{sitem.label}}：{{ sitem.userName }} </span>
            <!-- <span>财务经理：{{ splist[3] }} </span>
            <span style="margin-left: 15%">总经理：{{ splist[4] }} </span>
            <span style="margin-left: 15%">财务总监：{{ splist[5] }} </span> -->
          </div>
        </td>
      </tr>
      <!--    </div>-->
    </table>
  </section>
</template>

<script>
import { getprocesspeople } from '@/api/business/processapi'
import PaymentWaterList from '@/components/paymentWaterList/index.vue'
import currency from 'currency.js'
export default {
  components: {
    PaymentWaterList
  },
  name: 'ReimbursementNew',
  props: {
    datas: {
      type: Object
    }
  },
  computed: {

    spLabelOne() {
      if (this.spLabelList && this.spLabelList.length > 0) {
       return this.spLabelList.slice(0, 3)
      }
      return []
    },
    spLabelTwo() {
      if (this.spLabelList && this.spLabelList.length > 3) {
        if (this.spLabelEnd && this.spLabelEnd.label == '总裁') {
            return this.spLabelList.slice(3,-1)
        }
        return this.spLabelList.slice(3)
      }
      return []
    },
    spLabelEnd() {
      if (this.spLabelList && this.spLabelList.length > 0) {
        return this.spLabelList[this.spLabelList.length-1]
      }
      return {}
    }
  },
  data() {
    return {
      title: '',
      moneydetail: [],
      allmoney: [],
      splist: [],
      spLabelList:[],
      filelength: 0,
      costTypeData : [
      {
              "label": "交通费-火车",
              "value": 1
            },
            {
              "label": "交通费-飞机票",
              "value": 2
            },
            {
              "label": "交通费-轮船票",
              "value": 3
            },
            {
              "label": "交通费-市内交通",
              "value": 4
            },
            {
              "label": "住宿费",
              "value": 5
            },
            {
              "label": "车辆费",
              "value": 6
            },
            {
              "label": "伙食补助费",
              "value": 7
            },
            {
              "label": "其它费用",
              "value": 8
            },
            {
              "label": "餐饮费",
              "value": 10
            }
      ],
      feiyongData: [
        {
          label: '快递费',
          value: 2
        },
        {
          label: '水电费',
          value: 3
        },
        {
          label: '办公用品',
          value: 4
        },
        {
          label: '办公费',
          value: 5
        },
        {
          label: '交通差旅费',
          value: 6
        },
        {
          label: '招待费',
          value: 7
        },
        {
          label: '电话宽带费',
          value: 8
        },
        {
          label: '员工费用',
          value: 9
        },
        {
          label: '工资.奖金',
          value: 10
        },
        {
          label: '社保公积金',
          value: 11
        },
        {
          label: '工会经费',
          value: 12
        },
        {
          label: '福利费',
          value: 13
        },
        {
          label: '房租物业费',
          value: 14
        },
        {
          label: '汽车费用',
          value: 15
        },
        {
          label: '油费',
          value: 16
        },
        {
          label: '保险',
          value: 17
        },
        {
          label: '维修费',
          value: 18
        },
        {
          label: '检车',
          value: 19
        },
        {
          label: '停车',
          value: 20
        },
        {
          label: '其他',
          value: 21
        },
        {
          label: '折旧费',
          value: 22
        },
        {
          label: '集团管理费',
          value: 23
        },
        {
          label: '购买茶',
          value: 24
        },
        {
          label: '购买酒',
          value: 25
        },
        {
          label: '公关费',
          value: 26
        },
        {
          label: '伙食费',
          value: 27
        },
        {
          label: '咨询服务费',
          value: 28
        },
        {
          label: '装修费',
          value: 29
        },
        {
          label: '其他',
          value: 30
        }
      ],
      jiaotongdata: [
        {
          label: '飞机',
          value: 1
        },
        {
          label: '轮船',
          value: 0
        },
        {
          label: '汽车',
          value: 2
        },
        {
          label: '火车',
          value: 3
        }
      ],
      unit: new Array('仟', '佰', '拾', '', '仟', '佰', '拾', '', '角', '分')
    }
  },
  created() {
    if (this.datas.flowName === '差旅报销申请') {
      this.title = '差旅费报销单'
    } else if (this.datas.flowName === '费用报销申请') {
      this.title = '费用报销单'
    }
    getprocesspeople(this.datas.processid).then(res => {
       if (res.user) {
        this.splist = res.user
      }
      if (res.spList) {
        this.spLabelList = res.spList
      }

      // this.splist = res.user
       if ((this.datas.sponsorCompany === 'CGWL' || this.datas.sponsorCompany === 'SDNM') && this.splist && this.splist.length > 2) {
        // 因为成功网联的流程没有财务审批这一步，所以手动添加一个空节点
        // 往 this.splist 的第3个元素前插入一个''
        this.splist.splice(2, 0, '')
      }

    })
    this.chuli()
  },
  methods: {
    printApply() {
      var str = window.document.getElementById('dataform').innerHTML
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    },
    chuli() {
      console.log('405',this.datas)
      for (const fliekey of Object.keys(this.datas.fileListg)) {
        const filelist = this.datas.fileListg[fliekey]
        if (filelist) {
          this.filelength += filelist.length
        }
      }
      // for (const fliekey of Object.keys(this.datas.fileList)) {
      //   const filelist = this.datas.fileList[fliekey]
      //   if (filelist) {
      //     this.filelength += filelist.length
      //   }
      // }
      if(this.datas.fileList && this.datas.fileList.length){
        this.filelength += this.datas.fileList.length
      }
      const alljiaotong = []
      alljiaotong.sum = 0
      const allchailv = []
      allchailv.sum = 0
      const allzhusu = []
      allzhusu.sum = 0
      for (const key of Object.keys(this.datas.cruddata)) {
        const data = this.datas.cruddata[key]
        if (data.length <= 0) {
          continue
        }
        let datafile = 0
        if (key.length > 5) {
          const filekey = key.substr(key.length - 1, 1)
          console.log(filekey)
          console.log(this.datas.fileListg[`${filekey}`])
          if (this.datas.fileListg[`${filekey}`]) {
            datafile = this.datas.fileListg[`${filekey}`].length
          }
          if (this.datas.fileList[`${filekey}`]) {
            datafile = this.datas.fileList[`${filekey}`].length
          }
        }
        for (let z = 0; z < data.length; z++) {
          const dataitem = data[z]

         console.log('dataitem',dataitem)

          // if (dataitem.type && dataitem.purpose) {
          //   dataitem.show = this.typeFmt(dataitem.type) + '-' + dataitem.purpose
          // } else if (dataitem.purpose) {
          //   dataitem.show = dataitem.purpose
          // } else
          if (dataitem.costType) {
            dataitem.show = this.typeFmt(dataitem.costType)
          }else{
            dataitem.show = '交通费'
          }
          let isD = true
          if (dataitem.show.indexOf('交通费') > -1 || dataitem.show.indexOf('车辆费') > -1 ){
            // alljiaotong.sum += Number(dataitem.sum)
            alljiaotong.sum = currency(alljiaotong.sum).add(dataitem.sum).value
            alljiaotong.fileNum = datafile
            isD = false
          }
          if (dataitem.show.indexOf('住宿费') > -1 ) {
            // allzhusu.sum += Number(dataitem.sum)
            allzhusu.sum = currency(allzhusu.sum).add(dataitem.sum).value
            allzhusu.fileNum = datafile
            isD = false
          }
          if (dataitem.show.indexOf('补助费') > -1 ) {
            // allchailv.sum += Number(dataitem.sum)
            allchailv.sum = currency(allchailv.sum).add(dataitem.sum).value
            allchailv.fileNum = datafile
            isD = false
          }
          dataitem.fileNum = datafile
          if (isD) {
            this.moneydetail.push(dataitem)
          }
        }
      }
      if (alljiaotong.sum) {
        alljiaotong.show = '交通费汇总'
        this.moneydetail.push(alljiaotong)
      }
      if (allzhusu.sum) {
        allzhusu.show = '住宿费汇总'
        this.moneydetail.push(allzhusu)
      }
      if (allchailv.sum) {
        allchailv.show = '差旅补助汇总'
        this.moneydetail.push(allchailv)
      }
      console.log(alljiaotong)
      for (let a = 0; a < this.moneydetail.length; a++) {
        const item = this.moneydetail[a]
        this.quzheng(item)
      }
      this.allmoney.sum = this.datas.sumNum
      this.quzheng(this.allmoney)
      this.NumberToChinese(this.datas.sumNum)
      this.datas.year = this.datas.date.slice(0, 4)
      this.datas.month = this.datas.date.slice(5, 7)
      this.datas.ri = this.datas.date.slice(8, 10)
    },
    toDx(n) { // 阿拉伯数字转换函数
      switch (n) {
        case '0':
          return '零'
        case '1':
          return '壹'
        case '2':
          return '贰'
        case '3':
          return '叁'
        case '4':
          return '肆'
        case '5':
          return '伍'
        case '6':
          return '陆'
        case '7':
          return '柒'
        case '8':
          return '捌'
        case '9':
          return '玖'
      }
    },
    NumberToChinese(money) {
      var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆',
        '柒', '捌', '玖')
      // 基本单位
      var cnIntRadice = new Array('', '拾', '佰', '仟')
      // 对应整数部分扩展单位
      var cnIntUnits = new Array('', '万', '亿', '兆')
      // 对应小数部分单位
      var cnDecUnits = new Array('角', '分', '毫', '厘')
      // 整数金额时后面跟的字符
      var cnInteger = '整'
      // 整型完以后的单位
      var cnIntLast = '元'
      // 最大处理的数字
      var maxNum = 999999999999999.9999
      // 金额整数部分
      var integerNum
      // 金额小数部分
      var decimalNum
      // 输出的中文金额字符串
      var chineseStr = ''
      // 分离金额后用的数组，预定义
      var parts
      if (money == '') {
        return ''
      }
      money = Number.parseFloat(money)
      if (money >= maxNum) {
        // 超出最大处理数字
        return ''
      }
      if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger
        return chineseStr
      }
      // 转换为字符串
      money = money.toString()
      if (money.indexOf('.') == -1) {
        integerNum = money
        decimalNum = ''
      } else {
        parts = money.split('.')
        integerNum = parts[0]
        decimalNum = parts[1].substr(0, 4)
      }
      // 获取整型部分转换
      if (Number.parseInt(integerNum, 10) > 0) {
        var zeroCount = 0
        var IntLen = integerNum.length
        for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1)
          var p = IntLen - i - 1
          var q = p / 4
          var m = p % 4
          if (n == '0') {
            zeroCount++
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0]
            }
            // 归零
            zeroCount = 0
            chineseStr += cnNums[Number.parseInt(n)] +
              cnIntRadice[m]
          }
          if (m == 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q]
          }
        }
        chineseStr += cnIntLast
      }
      // 小数部分
      if (decimalNum != '') {
        var decLen = decimalNum.length
        for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1)
          if (n != '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i]
          }
        }
      }
      if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger
      } else if (decimalNum == '') {
        chineseStr += cnInteger
      }

      this.datas.sumChinese = chineseStr
    },

    quzheng(item) {
      item.qianwan = Math.floor(item.sum / 10000000)
      item.baiwan = Math.floor((item.sum - item.qianwan * 10000000) / 1000000)
      item.shiwan = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000) / 100000)
      item.wan = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000) / 10000)
      item.qian = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000) / 1000)
      item.bai = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000) / 100)
      item.shi = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000 - item.bai * 100) / 10)
      item.yuan = Math.floor(item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10)
      // 计算小数点后边的数字
      if(item.sum.toString().split('.')[1] != undefined){
        const jfen=item.sum.toString().split('.')[1]
        if(jfen.toString().length <1){
          item.mao = 0
          item.fen = 0
        } else {
          item.mao = Math.floor(jfen.toString().substring(0,1))
          if(jfen.toString().length>1){
            item.fen = Math.floor(jfen.toString().substring(1,2))
          } else {
            item.fen = 0
          }
        }
      } else {
        item.mao = 0
        item.fen = 0
      }
      // item.mao = Math.floor(item.sum * 10 - item.qianwan * 100000000 - item.baiwan * 10000000 - item.shiwan * 1000000 - item.wan * 100000 - item.qian * 10000 - item.bai * 1000 - item.shi * 100 - item.yuan * 10)
      // item.fen = Math.floor(item.sum * 100 - item.qianwan * 1000000000 - item.baiwan * 100000000 - item.shiwan * 10000000 - item.wan * 1000000 - item.qian * 100000 - item.bai * 10000 - item.shi * 1000 - item.yuan * 100 - item.mao * 10)
      if (item.qianwan > 0) {
        return
      } else if (item.qianwan === 0 && item.baiwan > 0) {
        item.qianwan = '￥'
      } else if (item.baiwan === 0 && item.shiwan > 0) {
        item.qianwan = ''
        item.baiwan = '￥'
      } else if (item.shiwan === 0 && item.wan > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = '￥'
      } else if (item.wan === 0 && item.qian > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = '￥'
      } else if (item.qian === 0 && item.bai > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = ''
        item.qian = '￥'
      } else if (item.bai === 0 && item.shi > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = ''
        item.qian = ''
        item.bai = '￥'
      } else if (item.shi === 0 && item.yuan > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = ''
        item.qian = ''
        item.bai = ''
        item.shi = '￥'
      }
    },
    typeFmt(id) {
      for (let a = 0; a < this.costTypeData.length; a++) {
        const item = this.costTypeData[a]
        if (item.value === id) {
          return item.label
        }
      }
      return id
    },
    carFmt(id) {
      for (let a = 0; a < this.jiaotongdata.length; a++) {
        const item = this.jiaotongdata[a]
        if (item.value === id) {
          return item.label
        }
      }
      return id
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
#dataform{
  padding-top: 10px;padding-left: 10px;padding-right: 10px;
  /* margin:10; */
}
@media print {
  #dataform{
    padding: 0px;
  }
  .record-table {
    margin-top: 30px;
    margin-left: 90px;
    width:calc(100vw - 90px) !important;
    font-size: 13px;
    line-height: 1.5em;
  }
}
</style>

