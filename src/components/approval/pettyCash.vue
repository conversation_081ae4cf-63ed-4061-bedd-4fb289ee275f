<template>
  <section id="dataform" style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div style="width: 100%;text-align: right;margin-top: -42px;margin-bottom: 10px">
      <el-button type="primary" @click="printApply">打印</el-button>
    </div>
    <table rules="all" frame="box" class="record-table">
      <tr>
        <td style="text-align: left" colspan="13">
          <div style="text-align: center;font-size: 1.4em">
            {{pettyFmt(datas.cash) + "事处备用金使用明细"}}
          </div>
          <div style=";text-align: left">
            <span style="padding-left: 20%">{{ datas.year }}年{{ datas.month }}月{{ datas.ri }}日</span>
            <span style="padding-left: 50%">附件 {{ filelength }} 张</span>
          </div>
        </td>
      </tr>
      <!--    <div style="width: 100%;text-align: left">-->
      <tr>
        <td style="width: 20%;text-align: center" rowspan="2">日期</td>
        <td style="width: 25%;text-align: center" rowspan="2"> 摘要</td>
        <td style="width: 40%;text-align: center" colspan="10"> 金额</td>
        <td style="width: 15%;text-align: center" rowspan="1"> 发票</td>

      </tr>
      <tr>
        <td> 千</td>
        <td> 百</td>
        <td> 十</td>
        <td> 万</td>
        <td> 千</td>
        <td> 百</td>
        <td> 十</td>
        <td> 元</td>
        <td> 角</td>
        <td> 分</td>
      </tr>
      <tr v-for="item in moneydetail">
        <td style="text-align: center">{{ item.startdate }}</td>
        <td style="text-align: center">{{ item.show }}</td>
        <td>{{ item.qianwan }}</td>
        <td>{{ item.baiwan }}</td>
        <td>{{ item.shiwan }}</td>
        <td>{{ item.wan }}</td>
        <td>{{ item.qian }}</td>
        <td>{{ item.bai }}</td>
        <td>{{ item.shi }}</td>
        <td>{{ item.yuan }}</td>
        <td>{{ item.mao }}</td>
        <td>{{ item.fen }}</td>
        <td style="text-align: center" >{{item.moneytype}}</td>
      </tr>

      <tr>
        <td style="text-align: center">总计</td>
        <td style="text-align: center" />
        <td>{{ allmoney.qianwan }}</td>
        <td>{{ allmoney.baiwan }}</td>
        <td>{{ allmoney.shiwan }}</td>
        <td>{{ allmoney.wan }}</td>
        <td>{{ allmoney.qian }}</td>
        <td>{{ allmoney.bai }}</td>
        <td>{{ allmoney.shi }}</td>
        <td>{{ allmoney.yuan }}</td>
        <td>{{ allmoney.mao }}</td>
        <td>{{ allmoney.fen }}</td>
        <td style="text-align: center" />
      </tr>

      <tr>
        <td style="width: 100%;text-align: left" colspan="13">金额合计（大写）人民币：{{ datas.sumChinese }}</td>
      </tr>
      <tr>
        <td style="width: 100%;height: 80px;text-align: left" colspan="13">
          <div style="margin-top: -30px">
            备注：{{ datas.usefor }}
          </div>
        </td>
      </tr>
      <tr v-show="splist && splist.length > 0">
        <td style="height: 60px;text-align: left" colspan="4">
          <div>
            <span>办事处：{{ splist[0] }} </span>
          </div>
        </td>
        <td style="height: 60px;text-align: left" colspan="9" >
          <span>财务出纳：{{ splist[1] }} </span>
        </td>
      </tr>
      <tr v-show="splist && splist.length > 0">
        <td style="height: 60px;text-align: left" colspan="4">
          <div>
            <span>航运部：{{ splist[2] }} </span>
          </div>
        </td>
        <td style="height: 60px;text-align: left" colspan="9" >
          <span>总经理：{{ splist[3] }} </span>
        </td>
      </tr>
      <!--    </div>-->
        <tr v-show="spLabelList && spLabelList.length > 0">
          <!-- <span v-for="(sitem,sindex) in spLabelOne" :key="sindex" :style="{'margin-left':sindex==0?'':'15%'}">{{sitem.label}}：{{ sitem.userName }} </span> -->

        <td style="height: 60px;text-align: left" v-for="(sitem,sindex) in spLabelOne" :key="sindex" :colspan="sindex==0?4:9">
          <div>
            <!-- <span>办事处：{{ splist[0] }} </span> -->
            <span>{{sitem.label}}：{{ sitem.userName }}</span>
          </div>
        </td>
        <!-- <td style="height: 60px;text-align: left" colspan="9" >
          <span>财务出纳：{{ splist[1] }} </span>
        </td> -->
      </tr>
      <tr v-show="spLabelList && spLabelList.length > 0">
        <td style="height: 60px;text-align: left" v-for="(sitem,sindex) in spLabelTwo" :key="sindex" :colspan="sindex==0?4:9">
          <div>
            <!-- <span>办事处：{{ splist[0] }} </span> -->
            <span>{{sitem.label}}：{{ sitem.userName }}</span>
          </div>
        </td>
        <!-- <td style="height: 60px;text-align: left" colspan="4">
          <div>
            <span>航运部：{{ splist[2] }} </span>
          </div>
        </td>
        <td style="height: 60px;text-align: left" colspan="9" >
          <span>总经理：{{ splist[3] }} </span>
        </td> -->
      </tr>
    </table>
  </section>
</template>

<script>
import { getprocesspeople } from '@/api/business/processapi'
import { getPettycash } from '@/api/business/sysPettycashapi'

export default {
  name: 'pettyCash',
  props: {
    datas: {
      type: Object
    }
  },
  computed: {
    spLabelOne() {
      if (this.spLabelList && this.spLabelList.length > 0) {
       return this.spLabelList.slice(0, 2)
      }
      return []
    },
    spLabelTwo() {
      if (this.spLabelList && this.spLabelList.length > 2) {
        // if (this.spLabelEnd && this.spLabelEnd.label == '总裁') {
        //     return this.spLabelList.slice(2,-1)
        // }
        return this.spLabelList.slice(2)
      }
      return []
    },
    spLabelEnd() {
      if (this.spLabelList && this.spLabelList.length > 0) {
        return this.spLabelList[this.spLabelList.length-1]
      }
      return {}
    }
  },
  data() {
    return {
      title:'',
      pettyCashList:[],
      moneydetail: [],
      allmoney: [],
      spLabelList:[],
      splist: [],
      filelength: 0,
      feiyongData: [
        {
          label: '快递费',
          value: 2
        },
        {
          label: '水电费',
          value: 3
        },
        {
          label: '办公用品',
          value: 4
        },
        {
          label: '办公费',
          value: 5
        },
        {
          label: '交通差旅费',
          value: 6
        },
        {
          label: '招待费',
          value: 7
        },
        {
          label: '电话宽带费',
          value: 8
        },
        {
          label: '员工费用',
          value: 9
        },
        {
          label: '工资.奖金',
          value: 10
        },
        {
          label: '社保公积金',
          value: 11
        },
        {
          label: '工会经费',
          value: 12
        },
        {
          label: '福利费',
          value: 13
        },
        {
          label: '房租物业费',
          value: 14
        },
        {
          label: '汽车费用',
          value: 15
        },
        {
          label: '油费',
          value: 16
        },
        {
          label: '保险',
          value: 17
        },
        {
          label: '维修费',
          value: 18
        },
        {
          label: '检车',
          value: 19
        },
        {
          label: '停车',
          value: 20
        },
        {
          label: '其他',
          value: 21
        },
        {
          label: '折旧费',
          value: 22
        },
        {
          label: '集团管理费',
          value: 23
        },
        {
          label: '购买茶',
          value: 24
        },
        {
          label: '购买酒',
          value: 25
        },
        {
          label: '公关费',
          value: 26
        },
        {
          label: '伙食费',
          value: 27
        },
        {
          label: '咨询服务费',
          value: 28
        },
        {
          label: '装修费',
          value: 29
        },
        {
          label: '其他',
          value: 30
        }
      ],
      jiaotongdata:[
        {
          label:"飞机",
          value:1
        },
        {
          label:"轮船",
          value:0
        },
        {
          label:"汽车",
          value:2
        },
        {
          label:"火车",
          value:3
        }
      ],
      unit: new Array('仟', '佰', '拾', '', '仟', '佰', '拾', '', '角', '分')
    }
  },
  created() {
    getprocesspeople(this.datas.processid).then(res => {
      // this.splist = res.user
       if (res.user) {
        this.splist = res.user
      }
      if (res.spList) {
        this.spLabelList = res.spList
      }
    })
    getPettycash().then(res =>{
      this.pettyCashList = res.data
    })
    this.chuli()
  },
  methods: {
    printApply() {
      var str = window.document.getElementById('dataform').innerHTML
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    },
    chuli() {
      console.log(this.datas)
      for (const fliekey of Object.keys(this.datas.fileListg)) {
        const filelist = this.datas.fileListg[fliekey]
        if (filelist) {
          this.filelength += filelist.length
        }
      }
      var alljiaotong = []
      alljiaotong.sum = 0
      var allchailv = []
      allchailv.sum = 0
      var allzhusu = []
      allzhusu.sum = 0
      for (const key of Object.keys(this.datas.cruddata)) {
        const data = this.datas.cruddata[key]
        if (data.length <= 0) {
          continue
        }
        var datafile = 0
        if (key.length > 5) {
          var filekey = key.substr(key.length - 1, 1)
          console.log(filekey)
          console.log(this.datas.fileListg[`${filekey}`])
          if (this.datas.fileListg[`${filekey}`]) {
            datafile = this.datas.fileListg[`${filekey}`].length
          }
        }
        for (var z = 0; z < data.length; z++) {
          var dataitem = data[z]


          if (dataitem.type && dataitem.purpose) {
            dataitem.show = this.typeFmt(dataitem.type) + '-' + dataitem.purpose
          } else if (dataitem.purpose) {
            dataitem.show = dataitem.purpose
          } else if (dataitem.type) {
            dataitem.show = this.typeFmt(dataitem.type)
          }
          if (dataitem.purpose === '交通费'){
            alljiaotong.sum += Number(dataitem.sum)
            alljiaotong.fileNum = datafile
          }
          if (dataitem.purpose === '住宿费'){
            allzhusu.sum += Number(dataitem.sum)
            allzhusu.fileNum = datafile
          }
          if (dataitem.purpose === '差旅补助'){
            allchailv.sum += Number(dataitem.sum)
            allchailv.fileNum = datafile
          }
          dataitem.fileNum = datafile
          if (dataitem.purpose !== '交通费' && dataitem.purpose !== '住宿费' && dataitem.purpose !== '差旅补助') {
            this.moneydetail.push(dataitem)
          }
        }
      }
      if (alljiaotong.sum){
        alljiaotong.show = '交通费汇总'
        this.moneydetail.push(alljiaotong)
      }
      if (allzhusu.sum){
        allzhusu.show = '住宿费汇总'
        this.moneydetail.push(allzhusu)
      }
      if (allchailv.sum){
        allchailv.show = '差旅补助汇总'
        this.moneydetail.push(allchailv)
      }
      console.log(alljiaotong)
      for (var a = 0; a < this.moneydetail.length; a++) {
        var item = this.moneydetail[a]
        this.quzheng(item)
      }
      this.allmoney.sum = this.datas.sumNum
      this.quzheng(this.allmoney)
      this.NumberToChinese(this.datas.sumNum)
      this.datas.year = this.datas.date.slice(0, 4)
      this.datas.month = this.datas.date.slice(5, 7)
      this.datas.ri = this.datas.date.slice(8, 10)
    },
    toDx(n) { // 阿拉伯数字转换函数
      switch (n) {
        case '0':
          return '零'
        case '1':
          return '壹'
        case '2':
          return '贰'
        case '3':
          return '叁'
        case '4':
          return '肆'
        case '5':
          return '伍'
        case '6':
          return '陆'
        case '7':
          return '柒'
        case '8':
          return '捌'
        case '9':
          return '玖'
      }
    },
    NumberToChinese(money) {
      var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆',
        '柒', '捌', '玖');
      // 基本单位
      var cnIntRadice = new Array('', '拾', '佰', '仟');
      // 对应整数部分扩展单位
      var cnIntUnits = new Array('', '万', '亿', '兆');
      // 对应小数部分单位
      var cnDecUnits = new Array('角', '分', '毫', '厘');
      // 整数金额时后面跟的字符
      var cnInteger = '整';
      // 整型完以后的单位
      var cnIntLast = '元';
      // 最大处理的数字
      var maxNum = 999999999999999.9999;
      // 金额整数部分
      var integerNum;
      // 金额小数部分
      var decimalNum;
      // 输出的中文金额字符串
      var chineseStr = '';
      // 分离金额后用的数组，预定义
      var parts;
      if (money == '') {
        return '';
      }
      money = parseFloat(money);
      if (money >= maxNum) {
        // 超出最大处理数字
        return '';
      }
      if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger;
        return chineseStr;
      }
      // 转换为字符串
      money = money.toString();
      if (money.indexOf('.') == -1) {
        integerNum = money;
        decimalNum = '';
      } else {
        parts = money.split('.');
        integerNum = parts[0];
        decimalNum = parts[1].substr(0, 4);
      }
      // 获取整型部分转换
      if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0;
        var IntLen = integerNum.length;
        for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1);
          var p = IntLen - i - 1;
          var q = p / 4;
          var m = p % 4;
          if (n == '0') {
            zeroCount++;
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0];
            }
            // 归零
            zeroCount = 0;
            chineseStr += cnNums[parseInt(n)]
              + cnIntRadice[m];
          }
          if (m == 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q];
          }
        }
        chineseStr += cnIntLast;
      }
      // 小数部分
      if (decimalNum != '') {
        var decLen = decimalNum.length;
        for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1);
          if (n != '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i];
          }
        }
      }
      if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger;
      } else if (decimalNum == '') {
        chineseStr += cnInteger;
      }

      this.datas.sumChinese =  chineseStr;
    },

    quzheng(item) {
      item.qianwan = Math.floor(item.sum / 10000000)
      item.baiwan = Math.floor((item.sum - item.qianwan * 10000000) / 1000000)
      item.shiwan = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000) / 100000)
      item.wan = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000) / 10000)
      item.qian = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000) / 1000)
      item.bai = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000) / 100)
      item.shi = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000 - item.bai * 100) / 10)
      item.yuan = Math.floor(item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10)
      item.mao = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10 - item.yuan) * 10)
      item.fen = Math.floor((item.sum - item.qianwan * 10000000 - item.baiwan * 1000000 - item.shiwan * 100000 - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10 - item.yuan - item.mao / 10) * 100)
      if (item.qianwan > 0) {
        return
      } else if (item.qianwan === 0 && item.baiwan > 0) {
        item.qianwan = '￥'
      } else if (item.baiwan === 0 && item.shiwan > 0) {
        item.qianwan = ''
        item.baiwan = '￥'
      } else if (item.shiwan === 0 && item.wan > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = '￥'
      } else if (item.wan === 0 && item.qian > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = '￥'
      } else if (item.qian === 0 && item.bai > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = ''
        item.qian = '￥'
      } else if (item.bai === 0 && item.shi > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = ''
        item.qian = ''
        item.bai = '￥'
      } else if (item.shi === 0 && item.yuan > 0) {
        item.qianwan = ''
        item.baiwan = ''
        item.shiwan = ''
        item.wan = ''
        item.qian = ''
        item.bai = ''
        item.shi = '￥'
      }
    },
    typeFmt(id) {
      for (var a = 0; a < this.feiyongData.length; a++) {
        var item = this.feiyongData[a]
        if (item.value === id) {
          return item.label
        }
      }
      return id
    },
    carFmt(id) {
      for (var a = 0; a < this.jiaotongdata.length; a++) {
        var item = this.jiaotongdata[a]
        if (item.value === id) {
          return item.label
        }
      }
      return id
    },
    pettyFmt(id) {
      for (var a = 0; a < this.pettyCashList.length; a++) {
        var item = this.pettyCashList[a]
        if (item.id === Number(id)) {
          return item.name
        }
      }
      return id
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
@media print {
  #dataform{
    padding: 0px;
  }
  .record-table {
    margin-top: 30px;
    margin-left: 90px;
    width:calc(100vw - 90px) !important;
    font-size: 13px;
    line-height: 1.5em;
  }
}
</style>
