<template>
  <section>
    <DingjinExternalAccountRequest :apply-obj="applicationForm" ref="externalAccount"></DingjinExternalAccountRequest>
    <!-- <el-form>
      <el-form-item style="width: 100%;text-align: right;">
        <div v-if="!fujianurl" style="width: 90%;text-align: right;margin-right: 30px;margin-top: 15px;color: red">未上传合同附件</div>
        <el-button v-if="fujianurl" @click="chakanfujian" style="margin-right: 30px;margin-top: 15px" type="primary" round>查看附件</el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      title="合同图片"
      :visible.sync="showPic"
      width="50%"
      :append-to-body="true"
      :before-close="handleClose1">
      <img :src="fujianurl" style="width: 100%">
    </el-dialog> -->
  </section>
</template>

<script>
    import {getShipLineDingjingById} from  "../../api/system/baseInit";
    import DingjinExternalAccountRequest from "./DingjinExternalAccountRequest";
    // import {getUser} from "../../api/user";

    export default {
        name: "ShipDingjinPaymentApprove",
        props: {
          datas: {
            type: Object
          },
          applyObj:{
            type: Object
          },
        },
      data(){
        return{
          tableData:[],
          fujianurl:'',
          showPic:false,
          applicationForm: {
            isApplyRecord:false,
            exter:{
              businessType:null,
            },
            obj:{},
            totalExterAmount:0,
            user:null,
            processId:null,
          },
        }
      },
        mounted() {
          this.$refs.externalAccount.baseComponent = this.$parent.$parent
          this.getData()
        },
        methods: {
          // getUserName(){
          //   getUser().then(res=>{
          //     if (res!=undefined) {
          //       this.applicationForm.user = res.user.id
          //     }
          //   })
          // },
          getData(){
            this.applicationForm = {
              exter: {
                businessType:null,
              },
              obj: {},
              params4: this.datas.params4,
              textContent:null,
              fileList:[]
            }
            this.tableData = []
            if(this.applyObj){
              this.applicationForm.isApplyRecord = false
              // this.getUserName()
              this.fujianurl = this.applyObj.url
              var querydata = {
                shipLineId:this.applyObj.id,
              }
              getShipLineDingjingById(this.datas.params1,undefined,this.datas.params4).then(res =>{
                if (res!=undefined) {
                  this.tableData = res.list
                  this.applicationForm.obj = this.tableData[0]
                  this.applicationForm.exter.businessType= "船舶定金"
                  this.cleanComData()
                  this.getCompinentData()
                }
              })
            } else {
              this.applicationForm.isApplyRecord = true
              this.applicationForm.user = this.datas.sponsorId
              this.applicationForm.processId = this.datas.processId
              this.applicationForm.textContent = this.datas.formValue.textContent
              this.applicationForm.fileList = this.datas.formValue.fileList
              this.fujianurl = this.datas.params2
              var querydata = {
                shipLineId:this.datas.params1
              }
              getShipLineDingjingById(this.datas.params1, this.applicationForm.processId,this.datas.params4).then(res =>{
                if (res!=undefined) {
                  this.tableData = res.list
                  this.applicationForm.obj = this.tableData[0]
                  this.applicationForm.exter = res.exter
                  this.applicationForm.exter.businessType= "船舶定金"
                  this.cleanComData()
                  this.getCompinentData()
                }
              })
            }
          },
          cleanComData(){
            this.$refs.externalAccount.cleandata()
          },
          getCompinentData(){
            this.$refs.externalAccount.getData(this.applicationForm)
            this.$refs.externalAccount.setFormVale(this.datas && this.datas.formValue)
          },
          getComponentsSendData(){
            var outData = this.$refs.externalAccount.sendData
            return outData
          },
          chakanfujian(){
            this.showPic = true
          },
          publicFmt(row,column,cellValue,index){
            var v = '--'
            if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
              v = cellValue
            }
            return v
          },
          handleClose1(){
            this.showPic = false
          },
          NumFmt(row,column,cellValue,index){
            var v = 0
            if(cellValue!=undefined&&cellValue!=null&&cellValue!=''){
              v = parseFloat(Math.abs(cellValue).toFixed(2)) && parseFloat(Math.abs(cellValue).toFixed(2)).toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
            }
            return v
          },
          number_format(num) {
            var v = 0
            if(!!num){
              v = parseFloat(Math.abs(num).toFixed(2)) && parseFloat(Math.abs(num).toFixed(2)).toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
            }
            return v
          },
        },
      components:{
        DingjinExternalAccountRequest
      },
    }
</script>

<style scoped>

  .query-form{
    text-align: left;
    padding-top: 10px;
  }
  .el-table-info >>> .cell{
    text-align: center;
  }
  .el-table-info >>> th {
    background: #EDF5FF;
  }
</style>
