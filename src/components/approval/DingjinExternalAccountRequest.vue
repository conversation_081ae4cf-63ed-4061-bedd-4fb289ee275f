<template>
    <section>
      <div style="width: 100%;text-align: right; margin-top: 8px;margin-bottom: 10px">
        <el-button v-show="conSpProId" @click="showSpById(conSpProId)" :type="conSpSt==2?'primary':'success'" >合同状态：{{ conSpStName }}</el-button>
        <el-button type="primary" @click="printApply">打印</el-button>
      </div>
      <div id="qingkuandan">
        <div class="print-height"></div>
      <table rules="all" frame="box" class="record-table">
        <tr>
          <td class="table-title">业务类型</td>
          <td colspan="3" class="td-checkbox">
            <div style="margin-left: 10px;">
<!--              <el-checkbox-group v-model="sendData.checkedOccurred" @change="handleCheckedTypeChange" :disabled="applicationForm.isApplyRecord">-->
<!--                <el-checkbox v-for="(item,index) in dictionaryLists['account_request']" :label="item.code" :key="index">{{item.value}}</el-checkbox>-->
<!--              </el-checkbox-group>-->
              {{applicationForm.exter?applicationForm.exter.businessType:''}}
            </div>
          </td>
          <td class="table-title">申请日期</td>
          <td colspan="4" class="td-checkbox">
            <div style="margin-left: 10px;">
              {{dayjs(applyObj.createDate).format('YYYY-MM-DD')}}
            </div>
          </td>
        </tr>
        <tr>
          <td class="table-title">付款方式</td>
          <td colspan="8" class="td-checkbox">
            <div style="margin-left: 10px;display: flex;">
              <el-checkbox-group v-model="sendData.checkedSettlement" :disabled="applicationForm.isApplyRecord">
                <el-checkbox @change="handleCheckedChange(item)" v-for="(item,index) in dictionaryLists['settlement_type']" :label="item.code" :key="index">
                  <div style="display: flex;">
                    <div style="margin-right: 5px;">{{item.value}}</div>
                    <div :style="sendData.checkedSettlement[0]==4?'':'display: none;'">
                      <input v-if="item.code == 4" class="checkInput" placeholder="请输入支票号" v-model="sendData.paybillValue" :disabled="applicationForm.isApplyRecord"/>
                    </div>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
              <el-checkbox style="margin-left: 40px;" v-model="sendData.billChecked" :disabled="applicationForm.isApplyRecord">
                <div style="display: flex;">
                  <div :style="sendData.billChecked?'':'display: none;'">
                    <select class="checkInput2" v-model="sendData.billValue" :disabled="applicationForm.isApplyRecord" v-if="!applicationForm.isApplyRecord">
                      <option v-for="(item,index) in dictionaryLists['bill_status']" :value="item.code">{{item.value}}</option>
                    </select>
                    <input :disabled="applicationForm.isApplyRecord" v-if="applicationForm.isApplyRecord" class="checkInput2" :value="statusFmt(null,null,sendData.billValue,null)"/>
<!--                    <div style="width: 40px;text-align: center" v-if="applicationForm.isApplyRecord">{{statusFmt(null,null,sendData.billValue,null)}}</div>-->
                  </div>
                  <div style="margin-left: 5px;">发票</div>
                </div>
              </el-checkbox>
            </div>
          </td>
        </tr>
        <tr v-if="baseComponent && baseComponent.datas && applicationForm.isApplyRecord">
          <td class="table-title">承兑汇票</td>
          <td colspan="8" class="td-checkbox">
            <div style="margin-right: 10px;margin-left: 10px;">{{baseComponent.datas.params5}}</div>
          </td>
        </tr>
        <tr>
          <td class="table-title">请款单位</td>
          <td colspan="8" class="td-checkbox">
            <div style="margin-right: 10px;margin-left: 10px;">{{contractCompanyFmt(null,null,applicationForm.obj?applicationForm.obj.contractCode:null,null)}}</div>
          </td>
        </tr>
        <tr>
          <td class="table-title" rowspan="3">收款方</td>
          <td>全称</td>
          <td colspan="7">{{formValue && formValue.bak_full_name ? formValue.bak_full_name :contract != null ?contract.accountName:"--"}}</td>
        </tr>
        <tr>
          <td>账号</td>
          <td colspan="7">{{formValue && formValue.bak_bank_number ? formValue.bak_bank_number :contract != null ?contract.cardNumber:"--"}}</td>
        </tr>
        <tr>
          <td>地址</td>
          <td class="td-address">{{formValue && formValue.bak_address_province? formValue.bak_address_province.replace('省','') :contract.province != null ?contract.province:(contract.address != null?contract.address.substring(0,contract.address.indexOf('省')):"")}}省</td>
          <td class="td-address">{{formValue && formValue.bak_address_city ? formValue.bak_address_city.replace('市','') :contract.city != null ?contract.city:(contract.address != null?contract.address.substring(contract.address.indexOf('省')+1,contract.address.indexOf('市')):"")}}市(县)</td>
          <td class="td-func">开户行</td>
          <td colspan="4">{{formValue && formValue.bak_bank_name ? formValue.bak_bank_name :contract != null ?contract.openingBank:"--"}}</td>
        </tr>
        <tr class="row-price">
          <td class="table-title">金额(大写)</td>
          <td colspan="5">{{revertNum(pay)}}</td>
          <td colspan="3">
            <table rules="all" cellspacing=0 cellpadding=0 class="price-table">
              <tr style="line-height: 20px;height: 20px;">
                <td>千</td>
                <td>百</td>
                <td>十</td>
                <td>万</td>
                <td>千</td>
                <td>百</td>
                <td>十</td>
                <td>元</td>
                <td>角</td>
                <td>分</td>
              </tr>
              <tr style="line-height: 20px;height: 20px;">
                <td>{{priceNum.numQianWan}}</td>
                <td>{{priceNum.numBaiWan}}</td>
                <td>{{priceNum.numShiWan}}</td>
                <td>{{priceNum.numWan}}</td>
                <td>{{priceNum.numQian}}</td>
                <td>{{priceNum.numBai}}</td>
                <td>{{priceNum.numShi}}</td>
                <td>{{priceNum.numYuan}}</td>
                <td>{{priceNum.numJiao}}</td>
                <td>{{priceNum.numFen}}</td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td>请款说明</td>
          <td colspan="9" style="text-align: left;">
            <span class="pt-show">{{ (baseComponent ? baseComponent.obj.remark : '') }}</span>
            <span class="pt-print">{{ (baseComponent ? baseComponent.obj.remark : '').replace(/\#.*\#/g, '') }}</span>
          </td>
        </tr>
          <!-- 付款信息 -->
        <PaymentWaterList :data-obj="formValue" tdColspans="1,2,7"></PaymentWaterList>
        <tr v-if="!spLabelList || spLabelList.length==0">
          <td style="text-align: left;width: 70%;" colspan="7">
            <div style="display: flex;">
              <div class="td-mar">申请人：{{userName}}</div>
              <div class="td-mar">商务审批：{{spPeoples1}}</div>
              <div class="td-mar">业务经理：{{spPeoples2}}</div>
              <div class="td-mar">往来会计：{{spPeoples3}}</div>
            </div>
          </td>
          <td style="text-align: left;width: 30%;" rowspan="2" colspan="3">
            公司负责人:{{spPeoples6}}
          </td>
        </tr>
        <tr v-if="!spLabelList || spLabelList.length==0">
          <td style="text-align: left;width: 70%;"  colspan="7">
            <div style="display: flex;">
              <div class="td-mar">财务总监：{{spPeoples5}}</div>
              <div class="td-mar">总经理：{{spPeoples4}}</div>
              <div class="td-mar">出纳：{{spPeoples7}}</div>
              <div class="td-mar">会计：{{spPeoples8}}</div>
            </div>
          </td>
        </tr>
        <tr v-if="spLabelList && spLabelList.length > 0">
        <td style="text-align: left;width: 70%;" colspan="7">
          <div style="display: flex;">
            <div class="td-mar" v-for="(sitem,sindex) in spLabelOne" :key="sindex" >{{sitem.label}}：{{ sitem.userName }} </div>
          </div>
        </td>
        <td style="text-align: left;width: 30%;" rowspan="2" colspan="3">
          <div style="margin-left: 15px">
           公司负责人：{{ spLabelEnd.label && spLabelEnd.label=='总裁'? spLabelEnd.userName:" " }}
          </div>
        </td>
      </tr>
      <tr v-if="spLabelList && spLabelList.length > 0">
        <td style="text-align: left;width: 70%;" colspan="7">
          <div style="display: flex;">
            <div class="td-mar" v-for="(sitem,sindex) in spLabelTwo" :key="sindex" >{{sitem.label}}：{{ sitem.userName }} </div>
            <!-- <span>财务经理：{{ splist[3] }} </span>
            <span style="margin-left: 15%">总经理：{{ splist[4] }} </span>
            <span style="margin-left: 15%">财务总监：{{ splist[5] }} </span> -->
          </div>
        </td>
      </tr>
      </table>
      </div>
      <ProgressComponent ref="processDrawer"></ProgressComponent>
      <div v-if="applicationForm.isApplyRecord" style="margin-top: 10px;">
        <span>备注：{{ applicationForm.textContent||'--' }}</span>
        <br>
        附件：<file-list :value="applicationForm.fileList"></file-list>
      </div>
    </section>
</template>

<script>
  // import {getDictionaryList} from "../../api/sysdictionary";
import {
  getDictionaryList,
  getSysSupplierContactList,
  getUserInfoById,
  getContractStatusByShipLineId
} from "../../api/system/baseInit";


import Select from "../esform/units/select";

import {fmtDictionary,fmtDictionarySpare} from "../../utils/util";
import { getprocesspeople } from '@/api/business/processapi'
import PaymentWaterList from '@/components/paymentWaterList/index.vue'
import FileList from "@/components/filelist/index.vue";
const processApi = require('@/api/system/process')

import ProgressComponent from '@/components/workflow/process'

export default {
  name: "ExternalAccountRequest",
  components: {Select,PaymentWaterList,ProgressComponent,FileList},
  props: {
    applyObj: {
      type: Object
    },
  },
  computed: {
    spLabelOne() {
      if (this.spLabelList && this.spLabelList.length > 0) {
        return this.spLabelList.slice(0, 4)
      }
      return []
    },
    spLabelTwo() {
      if (this.spLabelList && this.spLabelList.length > 4) {
        if (this.spLabelEnd && this.spLabelEnd.label == '总裁') {
          return this.spLabelList.slice(4, -1)
        }
        return this.spLabelList.slice(4)
      }
      return []
    },
    spLabelEnd() {
      if (this.spLabelList && this.spLabelList.length > 0) {
        return this.spLabelList[this.spLabelList.length - 1]
      }
      return {}
    }
  },
  data() {
    return {
      formValue:{},
      baseComponent: null,
      dictionaryLists:[],
      sendData:{
        checkedSettlement:[],
        billChecked:false,
        billValue:null,
        paybillValue:null,
      },
      spLabelList:[],
      contractList:[],
      applicationForm:{},
      contract:{},
      pay:0,
      priceNum:{
        numQianWan:0,
        numBaiWan:0,
        numShiWan:0,
        numWan:0,
        numQian:0,
        numBai:0,
        numShi:0,
        numYuan:0,
        numJiao:0,
        numFen:0,
      },
      userName:null,
      spPeoples1: null,
      spPeoples2: null,
      spPeoples3: null,
      spPeoples4: null,
      spPeoples5: null,
      spPeoples6: null,
      spPeoples7: null,
      spPeoples8: null,
      conSpSt: 0, // 合同审批状态 1 进行中 2 完成 3 拒绝 4 撤回
      conSpStName: '',
      conSpProId: null, // 合同审批流程id
      comId: '',
      shipLineId:'',
    };
  },
  mounted() {
    // this.getData()
    // this.getSpPerson()

  },
  methods: {
    showSpById(id) {
          this.$refs.processDrawer.drawer = true
          this.$refs.processDrawer.processId = id
          this.$refs.processDrawer.title ='合同审批流程'
          this.$refs.processDrawer.doInit()
    },
    loadDict(cid) {
      getDictionaryList("account_request,settlement_type,bill_status,contract_company",null,cid).then(res=>{
      if (res!=undefined) {
        this.dictionaryLists = res.map
      }
    })
    },
    getUserNameById(id) {
      getUserInfoById(id,null,this.comId).then(res=>{
        if (res!=undefined && !!res.user) {
          this.userName =  res.user.name

        }
      })
    },
    loadSpContByShipLineId() {
      this.conSpSt = 0
      this.conSpProId = null
      getContractStatusByShipLineId(this.shipLineId,null,this.comId).then(({ data }) => {
        console.log('data', data)
        if (data.processId) {
          this.conSpSt = data.status
          this.conSpStName = data.statusName
          this.conSpProId = data.processId
        }

      })
    },
    getSpPerson() {
        getprocesspeople(this.applicationForm.processId).then(res => {
        if (res.spList) {
          this.spLabelList = res.spList
        }
      })
      processApi.getSpPerspn(this.applicationForm.processId).then(res => {
        if (!!res.list){
          for(var i=0;i<res.list.length;i++){
            var tmp = res.list[i]
            if(tmp.nodeId == 10){
              this.spPeoples1 = tmp.userName
            } else if(tmp.nodeId == 20){
              this.spPeoples2 = tmp.userName
            } else if(tmp.nodeId == 30){
              this.spPeoples3 = tmp.userName
            } else if(tmp.nodeId == 40){
              this.spPeoples4 = tmp.userName
            } else if(tmp.nodeId == 50){
              this.spPeoples5 = tmp.userName
            } else if(tmp.nodeId == 60 || tmp.nodeId == 70){
              this.spPeoples6 = tmp.userName
            }  else if(Math.floor(tmp.nodeId/10) == 8){
              this.spPeoples7 = tmp.userName
            } else if(tmp.nodeId == 25){
              this.spPeoples8 = tmp.userName
            }
          }
        }
      }).catch(e => {

      })
    },
    printApply() {
      // $("input").each(function(){
      //   $(this).attr('value',$(this).val());
      // });
      let checkEle = window.document.getElementsByClassName('checkInput2')
      checkEle[0].setAttribute('value', checkEle[0].value)
      // var html = window.document.body.innerHTML
      var str = window.document.getElementById("qingkuandan").innerHTML
      window.document.body.innerHTML=str;
      window.print();
      window.location.reload()
    },
    handleCheckedTypeChange(value) {
    },
    handleCheckedChange(item) {
      if(this.sendData.checkedSettlement.length>0){
        this.sendData.checkedSettlement = []
        this.sendData.checkedSettlement.push(item.code)
      }
    },
    getChecked() {
      if(this.applicationForm.exter.payMethod){
        this.sendData.checkedSettlement.push(this.applicationForm.exter.payMethod)
        this.sendData.paybillValue = this.applicationForm.exter.checkNumber
        if( this.applicationForm.exter.billStatus != null){
          this.sendData.billChecked = true
          this.sendData.billValue = this.applicationForm.exter.billStatus
        }
      }
    },
    setFormVale(formVal) {
      this.formValue = formVal
    },
    getData(item) {
      this.applicationForm = {}
      if (item == null) {
        this.applicationForm = this.applyObj
      } else {
        this.applicationForm = item
      }
      console.log('this.applicationForm', this.applicationForm)
      this.comId = this.applicationForm.params4
      this.shipLineId = this.applicationForm.obj.id
      this.loadDict(this.applicationForm.params4)
      this.getUserNameById(this.applicationForm.user)
      this.getSpPerson()
      this.getChecked()
      if (this.baseComponent.obj.contract && this.baseComponent.obj.contract.cardNumber && this.baseComponent.obj.contract.cardNumber != '') {
        this.contract = {
          accountName: this.baseComponent.obj.contract.accountName,
          address: this.baseComponent.obj.contract.address,
          cardNumber: this.baseComponent.obj.contract.cardNumber,
          province: this.baseComponent.obj.contract.province,
          city: this.baseComponent.obj.contract.city,
          name: this.baseComponent.obj.contract.name,
          openingBank: this.baseComponent.obj.contract.openingBank
        }
      } else {
        var sysSupplierId = this.applicationForm.obj.settleCompany==null?this.applicationForm.obj.shipownerCompanyId:this.applicationForm.obj.settleCompany
        getSysSupplierContactList(sysSupplierId,null,this.comId).then(res=>{
          if (res!=undefined) {
            this.contractList = res.list
            if(res.list != null && res.list.length>0){
              this.contract = res.list[0]
            }
          }
        })
      }

      // this.pay = this.applicationForm.totalExterAmount-this.applicationForm.deductPrice-this.applicationForm.price
      this.pay = Number(this.applicationForm.obj.dingjin)
      this.initNum(this.pay)
      this.loadSpContByShipLineId()
    },
    cleandata() {
      this.contractList = [],
      this.applicationForm={},
      this.contract={},
      this.pay=0,
        this.userName=null,
      this.priceNum={
          numQianWan:0,
          numBaiWan:0,
          numShiWan:0,
          numWan:0,
          numQian:0,
          numBai:0,
          numShi:0,
          numYuan:0,
          numJiao:0,
          numFen:0,
      },
      this.sendData={
          checkedSettlement:[],
          billChecked:false,
          billValue:null,
          paybillValue:null,
      }
    },
    initNum(num) {
      var last = num
      if(num.toString().split('.')[0].length <8){
        if(num.toString().split('.')[0].length <7){
          this.priceNum.numQianWan = ""
        } else {
          this.priceNum.numQianWan = "￥"
        }
      } else {
        this.priceNum.numQianWan = Math.floor(last/10000000)
        last = last%10000000
      }

      if(num.toString().split('.')[0].length <7){
        if(num.toString().split('.')[0].length <6){
          this.priceNum.numBaiWan = ""
        } else {
          this.priceNum.numBaiWan = "￥"
        }

      } else {
        this.priceNum.numBaiWan = Math.floor(last/1000000)
        last = last%1000000

      }

      if(num.toString().split('.')[0].length <6){
        if(num.toString().split('.')[0].length <5){
          this.priceNum.numShiWan =""
        } else {
          this.priceNum.numShiWan ="￥"
        }
      } else {
        this.priceNum.numShiWan = Math.floor(last/100000)
        last = last%100000

      }

      if(num.toString().split('.')[0].length <5){
        if(num.toString().split('.')[0].length <4){
          this.priceNum.numWan = ""
        } else {
          this.priceNum.numWan = "￥"
        }
      } else {
        this.priceNum.numWan = Math.floor(last/10000)
        last = last%10000

      }
      if(num.toString().split('.')[0].length <4){
        if(num.toString().split('.')[0].length <3){
          this.priceNum.numQian = ""
        } else {
          this.priceNum.numQian = "￥"
        }
      } else {
        this.priceNum.numQian = Math.floor(last/1000)
        last = last%1000

      }
      if(num.toString().split('.')[0].length <3){
        if(num.toString().split('.')[0].length <2){
          this.priceNum.numBai = ""
        } else {
          this.priceNum.numBai = "￥"
        }
      } else {
        this.priceNum.numBai = Math.floor(last/100)
        last = last%100

      }
      if(num.toString().split('.')[0].length <2){
        if(num.toString().split('.')[0].length <12){
          this.priceNum.numShi = ""
        } else {
          this.priceNum.numShi = "￥"
        }
      } else {
        this.priceNum.numShi = Math.floor(last/10)
        last = last%10

      }
        if(num.toString().split('.')[0].length <1){
          this.priceNum.numYuan = 0
        } else {
          this.priceNum.numYuan = Math.floor(last)
          // last = last%1

        }
            // 计算小数点后边的数字
        if(num.toString().split('.')[1] != undefined){
          let jfen=num.toString().split('.')[1]
          if(num.toString().split('.')[1].length <1){
            this.priceNum.numJiao = 0
          } else {
            this.priceNum.numJiao = Math.floor(jfen.toString().substring(0,1))
          }
          if(num.toString().split('.')[1].length <2){
            this.priceNum.numFen = 0
          } else {
            // 截取前一位
            if(jfen.toString().length>1){
              this.priceNum.numFen = Math.floor(jfen.toString().substring(1,2))
            } else {
              this.priceNum.numFen = 0
            }
          }
        } else {
          this.priceNum.numJiao = 0
          this.priceNum.numFen = 0
        }
        // this.priceNum.numJiao = Math.floor(last*10)
        // last = (last*10)%1
        //   try{
        // if(last){
        //   last= last.toFixed(1)
        // }
        // }catch(e){}

        // this.priceNum.numFen = Math.floor(last*10)
      },
          revertNum(money) {
            var cnNums = new Array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"); //汉字的数字
            var cnIntRadice = new Array("", "拾", "佰", "仟"); //基本单位
            var cnIntUnits = new Array("", "万", "亿", "兆"); //对应整数部分扩展单位
            var cnDecUnits = new Array("角", "分", "毫", "厘"); //对应小数部分单位
            var cnInteger = "整"; //整数金额时后面跟的字符
            var cnIntLast = "元"; //整型完以后的单位
            var maxNum = 999999999999999.9999; //最大处理的数字
            var IntegerNum; //金额整数部分
            var DecimalNum; //金额小数部分
            var ChineseStr = ""; //输出的中文金额字符串
            var parts; //分离金额后用的数组，预定义
            if (money == "") {
              return "";
            }
            money = parseFloat(money);
            if (money >= maxNum) {
              alert('超出最大处理数字');
              return "";
            }
            if (money == 0) {
              ChineseStr = cnNums[0] + cnIntLast + cnInteger;
              return ChineseStr;
            }
            money = money.toString(); //转换为字符串
            if (money.indexOf(".") == -1) {
              IntegerNum = money;
              DecimalNum = '';
            } else {
              parts = money.split(".");
              IntegerNum = parts[0];
              DecimalNum = parts[1].substr(0, 4);
            }
            if (parseInt(IntegerNum, 10) > 0) { //获取整型部分转换
              var zeroCount = 0;
              var IntLen = IntegerNum.length;
              for (var i = 0; i < IntLen; i++) {
                var n = IntegerNum.substr(i, 1);
                var p = IntLen - i - 1;
                var q = p / 4;
                var m = p % 4;
                if (n == "0") {
                  zeroCount++;
                } else {
                  if (zeroCount > 0) {
                    ChineseStr += cnNums[0];
                  }
                  zeroCount = 0; //归零
                  ChineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
                }
                if (m == 0 && zeroCount < 4) {
                  ChineseStr += cnIntUnits[q];
                }
              }
              ChineseStr += cnIntLast;
              //整型部分处理完毕
            }
            if (DecimalNum != '') { //小数部分
              var decLen = DecimalNum.length;
              for (var i = 0; i < decLen; i++) {
                var n = DecimalNum.substr(i, 1);
                if (n != '0') {
                  ChineseStr += cnNums[Number(n)] + cnDecUnits[i];
                }
              }
            }
            if (ChineseStr == '') {
              ChineseStr += cnNums[0] + cnIntLast + cnInteger;
            } else if (DecimalNum == '') {
              ChineseStr += cnInteger;
            }
            return ChineseStr;
          },

          statusFmt(row,column,cellValue,index){
            return fmtDictionary(cellValue,this.dictionaryLists['bill_status']);
          },
          contractCompanyFmt(row,column,cellValue,index){
            return fmtDictionarySpare(cellValue,this.dictionaryLists['contract_company'],"spare1");
          },
        }
    }
</script>

<style scoped>
  .record-table >>> .el-checkbox__input.is-disabled+span.el-checkbox__label{
    color: #606266;
  }
  td{
    width: 5%;
    text-align: center;
    height: 25px;
    color: #606266;
  }
  .record-title{
    width: 100%;
    text-align: center;
    font-size: 1.2rem;
  }
  .record-table{
    width: 100%;
  }
  .table-title{
    width: 15%;
  }
  .td-func{
    width: 10%;
  }
  .td-address{
    width: 15%;
    text-align: right;
  }
  .td-checkbox{
    text-align: left;
  }
  .row-price{
    height: 20px;
  }
  .price-table{
    width: 100%;
  }
  .td-mar{
    width: 50%;
  }
  .checkInput{
    height: 15px;
    border-left: none;
    border-right: none;
    border-top: none;
    border-bottom: #A8A8A9 1px solid;
    text-align: center;
  }
  .checkInput2{
    width: 80px;
    height: 20px;
    border-left: none;
    border-right: none;
    border-top: none;
    border-bottom: #A8A8A9 1px solid;
    text-align: center;
  }


  table >>> .el-checkbox.is-disabled span.el-checkbox__label.el-checkbox__label{
    font-weight: normal;
    color: #606266;
  }
  table >>> .el-checkbox__input.is-disabled .el-checkbox__inner.el-checkbox__inner,.el-table >>>  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner.el-checkbox__inner {
    border-color: #bdbdbd;
    background-color: transparent;
    border-radius: 0;
  }
  table >>> .el-checkbox__input.is-disabled.is-checked.is-checked .el-checkbox__inner::after {
    border-color: #606266;
    height: 18px;
    width: 7px;
    border-width: 3px;
    top: -7px;
    left: 2px;
  }
   @media print {
    .print-height{
      height: 90px;
    }
        .record-table {
          /* padding-top: 90px; */
          margin-left:90px;
          width:88% !important;
          /* width:calc(100vw - 90px) !important; */
        }
  }
</style>
