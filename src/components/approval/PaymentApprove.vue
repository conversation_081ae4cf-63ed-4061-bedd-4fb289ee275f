<template>
  <section>
    <!--    <el-switch-->
    <!--      style="display: block;text-align: center;margin-left: 5%;margin-bottom: 20px;"-->
    <!--      v-model="inOutSwitch"-->
    <!--      active-color="#409EFF"-->
    <!--      inactive-color="#409EFF"-->
    <!--      active-text="内账请款单"-->
    <!--      inactive-text="外账请款单"-->
    <!--      @change="getCompinentData"-->
    <!--    >-->
    <!--    </el-switch>-->
    <div v-show="!inOutSwitch">
      <ExternalAccountRequest ref="externalAccount" :apply-obj="applicationForm">
        <template #buttonLeft>
          <el-button v-if="tableData && tableData.length > 0" icon="el-icon-download" @click="showExportDetail = true">导出明细</el-button>
          <el-button v-if="contractIds && contractIds.length > 0" type="primary" @click="lookContract">查看合同</el-button>
        </template>
      </ExternalAccountRequest>
    </div>
    <div v-show="payType=='goodsCostOut' ">
      <div style="text-align: center;margin-top:5px;">合同信息</div>
      <el-table
        ref="goodsCostOutTable"
        :data="heTongInfoList"
        highlight-current-row
        style="width: 100%" >
        <el-table-column align="center" property="shipName" label="船名" ></el-table-column>
        <el-table-column align="center" property="epartureTimeDate" label="离港日期" ></el-table-column>
        <el-table-column align="center" property="supplierName" label="供应商" ></el-table-column>
        <el-table-column align="center" property="costName" label="类型"  ></el-table-column>
        <el-table-column align="center" property="tonnage" label="吨位" ></el-table-column>
        <!-- <el-table-column align="center" property="priceNo" label="单价" ></el-table-column> -->
        <el-table-column align="center" property="money" label="金额" ></el-table-column>

        <el-table-column align="center"  label="合同" >
          <template slot-scope="scope">
            <el-button v-if="scope.row.contractFileUrls" @click="showFiles(scope.row.contractFileUrls)">查看</el-button>
            <span v-else style="color: red;">未关联</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-show="isFanDian && fanDianInfoList && fanDianInfoList.length>0">
      <div style="text-align: center;">业务成本客户信息</div>
      <el-table
        ref="fanDianTable"
        :data="fanDianInfoList"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" property="tonnage" label="运量/吨"  />
        <el-table-column align="center" property="price" label="海运费收入(9%)" width="80px">
          <template slot-scope="scope">
            <span>{{ scope.row.price }}
              <span v-if="scope.row.taxRateStr!='9%'">({{ scope.row.taxRateStr }})</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" property="price" label="代理费收入(6%)" width="80px">
          <template slot-scope="scope">
            <span>{{ scope.row.agencyFee }}
              <span v-if="scope.row.agencyFeeTaxRateStr!='6%'">({{ scope.row.agencyFeeTaxRateStr }})</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" width="200px" label="单吨成本(税率)" >
          <template slot-scope="scope">
            <table style="width: 100%;">
              <tr v-for="(sitem,sidx) in scope.row.incomeCostList" :key="sidx">
                <td style="min-width:70px;">收入：{{sitem.type}} <span v-if="sitem.taxRateStr">({{ sitem.taxRateStr }})</span> </td>
                <td style="min-width:70px;">{{sitem.price}}</td>
              </tr>
              <tr>
                <td style="min-width:70px;">租船价<span v-if="scope.row.seaFreightTaxRateStr">({{ scope.row.seaFreightTaxRateStr }})</span> </td>
                <td style="min-width:70px;">{{ scope.row.seaFreight }}</td>
              </tr>
              <tr v-for="(sitem,sidx) in scope.row.collCostList" :key="sidx">
                <td style="min-width:70px;">{{sitem.type}}<span v-if="sitem.taxRateStr">({{ sitem.taxRateStr }})</span> </td>
                <td style="min-width:70px;">{{sitem.price}}</td>
              </tr>
            </table>
          </template>
        </el-table-column>
        <el-table-column align="center" property="rebatePrice" label="返款"  />
        <el-table-column align="center" property="rebateTax" label="返款对应增值税" width="70px"  />
        <el-table-column align="center" property="totalCost" label="成本合计"  />
        <el-table-column align="center" property="profitPerTon" label="单吨利润"  />
        <el-table-column align="center" property="totalProfit" label="利润额"  />
        <el-table-column align="center" label="合同" >
            <template slot-scope="scope">
              <el-button v-if="scope.row.contractFileUrls" @click="showFiles(scope.row.contractFileUrls)">查看</el-button>
              <span v-else style="color: red;">未关联</span>
            </template>
          </el-table-column>
      </el-table>
    </div>
    <!--    <el-form>-->
    <!--      <el-form-item style="width: 100%;text-align: right;">-->
    <!--        <el-button style="margin-right: 30px;margin-top: 15px" type="primary" round v-if="datas && datas.btnIsShow && datas.nodeId == '80' " @click="uploadBackRecord(applicationForm.processId)">上传回执单</el-button>-->
    <!--        <el-button style="margin-right: 30px;margin-top: 15px" type="primary" round v-if="datas && applicationForm.receiptPicUrl && (!datas.btnIsShow || datas.nodeId != '80' )" @click="uploadBackRecord(applicationForm.processId)">查看回执单</el-button>-->
    <!--      </el-form-item>-->
    <!--    </el-form>-->
    <!--   -->
    <!--    <div class="checkAccount" v-if="checkedSettlement ==2 && accountlist.length > 0 && datas.btnIsShow && (datas.nodeId == '80' || datas.nodeId == '90')">-->
    <!--      <div style="margin: 10px 0"></div>-->
    <!--      <div style="display: flex;justify-content: left;align-items: center;">-->
    <!--        <div style="font-size: 14px;color: #6A6C70;">{{datas.nodeId == '80'?'选择付款账户：':'付款账户：'}}</div>-->
    <!--      </div>-->
    <!--      <div>-->
    <!--        <el-table-->
    <!--          ref="singleTable"-->
    <!--          :data="datas.nodeId == '80'?accountlist:choseAccountlist"-->
    <!--          highlight-current-row-->
    <!--          @current-change="handleCurrentChange"-->
    <!--          style="width: 100%">-->
    <!--          <el-table-column fixed align="center" property="type" label="账户类型" ></el-table-column>-->
    <!--          <el-table-column align="center" property="bankName" label="银行名称"></el-table-column>-->
    <!--          <el-table-column align="center" property="accountName" label="账户名称" width="120px"></el-table-column>-->
    <!--          <el-table-column align="center" property="accountNumber" label="账户号"></el-table-column>-->
    <!--          <el-table-column align="center" property="initializationMoney" label="初始化余额"></el-table-column>-->
    <!--        </el-table>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    <el-dialog-->
    <!--      class="xuanxiang"-->
    <!--      :title="datas && datas.btnIsShow?'上传回执单':'查看回执单'"-->
    <!--      :visible.sync="showUpload"-->
    <!--      width="60%"-->
    <!--      :before-close="handleClose"-->
    <!--      :close-on-click-modal=false-->
    <!--      :append-to-body="true"-->
    <!--    >-->
    <!--      <div style="width: 100%;text-align: left;">-->
    <!--        <el-upload-->
    <!--          ref="aaaa"-->
    <!--          accept=".jpg,.png,.gif,.jpeg,.bmp"-->
    <!--          :file-list="fileList"-->
    <!--          :action="uploadUrl"-->
    <!--          list-type="picture-card"-->
    <!--          :before-remove="handleRemove"-->
    <!--          :on-error="uploadError"-->
    <!--          :on-success="uploadSuccess"-->
    <!--          :before-upload="beforeAvatarUpload"-->
    <!--          :on-preview="handlePictureCardPreview"-->
    <!--          :class="datas &&!datas.btnIsShow?'hiddenClass':''"-->
    <!--          :disabled="datas &&!datas.btnIsShow"-->
    <!--        >-->
    <!--          <i class="el-icon-plus"></i>-->
    <!--        </el-upload>-->
    <!--        <el-dialog :visible.sync="dialogVisible"-->
    <!--        >-->
    <!--          <img width="100%" :src="dialogImageUrl" alt="">-->
    <!--        </el-dialog>-->
    <!--      </div>-->
    <!--      <span slot="footer" class="dialog-footer" v-if="datas && datas.btnIsShow">-->
    <!--                <el-button type="primary" @click="submitPic(applicationForm.processId)">提交</el-button>-->
    <!--            </span>-->
    <!--    </el-dialog>-->
    <el-dialog
      v-loading="contractPreviewLoading"
      title="合同预览"
      width="40%"
      :visible.sync="showContractPreview"
      :append-to-body="true"
    >
      <el-button v-if="contractList.length === 0" type="primary" @click="lookContract">查看合同</el-button>
      <div v-else class="contract-image__preview">
        <div v-for="(params,index) in contractList" :key="index">
          <!-- <el-image
          :src="it"
          :preview-src-list="contractList.map(item => item.previewFileUrl)">
        </el-image> -->
          <div v-if="params.previewFileUrl">
            <el-image
              style="width: 100%;"
              :src="params.previewFileUrl"
              :preview-src-list="[params.previewFileUrl]"
            />
          </div>
          <div v-else-if="params.fileUrl && (params.fileUrl.split('.').pop()=='jpg' || params.fileUrl.split('.').pop()=='png' || params.fileUrl.split('.').pop()=='jpeg')">
            <!-- 判断图片类 -->
            <el-image
              style="width: 100%;"
              :src="params.fileUrl"
              :preview-src-list="[params.fileUrl]"
            />
          </div>
          <div v-else>
            <span>合同下载：</span><a style="color:#009ff9;" :href="params.fileUrl" target="_blank">{{ params.fileName }}</a>
          </div>

          <div v-if="params.signedFileUrl">
            <div v-if="params.signedFileUrl.split('.').pop()=='jpg' || params.signedFileUrl.split('.').pop()=='png' || params.signedFileUrl.split('.').pop()=='jpeg'">
              <!-- 判断图片类 -->
              <el-image
                style="width: 100%;"
                :src="params.signedFileUrl"
                :preview-src-list="[params.signedFileUrl]"
              />
            </div>
            <div v-else>
              <span>合同下载：</span><a style="color:#009ff9;" :href="params.signedFileUrl" target="_blank">{{ params.signedFileName }}</a>
            </div>
          </div>
        </div>

      </div>

    </el-dialog>
    <el-dialog @opened="$refs.xTable.connect($refs.xToolbar);$refs.xTable.openExport()" title="导出明细" :visible.sync="showExportDetail" :append-to-body="true">
      <vxe-toolbar
        ref="xToolbar"
        export
        custom
        print
        :buttons="[]"
      />
      <vxe-table
        ref="xTable"
        :data="tableData"
        stripe
        size="small"
        border
        align="center"
        :print-config="{}"
        max-height="400"
        highlight-current-row
        :export-config="{'type':'xlsx', 'sheetName': '￥' + applicationForm.payment, 'filename': '请款明细' + parseInt(applicationForm.payment) + '', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
        resizable
      >
        <vxe-table-column field="shipName" title="船名" align="center" width="150" />
        <vxe-table-column
          field="epartureTimeDate"
          title="离港日期"
          align="center"
          width="100"
          :formatter="({cellValue}) => dayjs(cellValue).format('YYYY-MM-DD')"
        />
        <vxe-table-column field="costTypeName" title="费用类型" align="center" width="150" />
        <vxe-table-column field="billTax" title="税率" align="center" width="100" />
        <vxe-table-column
          field="settleAmount"
          title="金额"
          align="right"
          :formatter="({cellValue}) => fmtMoney(cellValue)"
        />

      </vxe-table>
    </el-dialog>
    <el-image-viewer
        :zIndex="9999"
        v-if="imgViewerVisible"
        :on-close="closeImgViewer"
        :url-list="imgList" />
  </section>
</template>

<script>
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
import ExternalAccountRequest from '../../components/approval/ExternalAccountRequest'
import { fmtDictionary } from '../../utils/util'
import { getDictionaryList, getPayment, fanDianCustomerInfo, selectbycode, selectbyId,heTongInfo } from '../../api/system/baseInit'
import { getContractByIds } from '../../api/business/contract'
import {previewPDF} from '@/utils/index'
export default {
  name: 'PaymentApprove',
  components: {
    ExternalAccountRequest,
    'ElImageViewer': () => import('element-ui/packages/image/src/image-viewer')
  },
  props: {
    datas: {
      type: Object
    },
    applyObj: {
      type: Object
    }
  },
  data() {
    return {
      showExportDetail: false,
      contractPreviewLoading: false,
      showContractPreview: false,
      contractList: [],
      contractIds: null,
      checkedSettlement: null,
      receiveCompanyList: [],
      accountlist: [],
      choseAccountlist: [],
      currentRowId: null,
      tableData: [],
      dictionaryLists: [],
      applicationForm: {
        processId: null,
        dingjin: 0,
        isApplyRecord: false,
        inter: {},
        exter: {
          businessType: null
        },
        totalExterAmount: 0,
        user: null,
        invoiceCompany: null
      },
      deductList: [],
      inOutSwitch: false,
      fileList: [],
      picList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      showUpload: false,
      isFanDian: false,
      fanDianInfoList: [],
      imgList: [],
      imgViewerVisible: false,
      heTongInfoList: [],
      payType: false
    }
  },
  beforeDestroy() {
    document.removeEventListener('paste', this.onPasteUpload)
  },
  beforeUpdate() {
    if (this.datas && this.datas.params7 == 'fandian' || this.applyObj && this.applyObj.modelCode == 'fandian') {
      this.isFanDian = true
    } else {
      this.isFanDian = false
    }
  },
  mounted() {
    this.$refs.externalAccount.baseComponent = this.$parent.$parent
    getDictionaryList('cost_type,contract_company').then(res => {
      if (res != undefined) {
        this.dictionaryLists = res.map
        this.receiveCompanyList = res.map['contract_company']
      }
    })
    this.$nextTick(function() {
          this.getData()
    })
  },
  methods: {
    loadHeTongInfo(costAddIds, type) {
      this.heTongInfoList = []
      if(type && costAddIds && costAddIds.length > 0 ) {
        heTongInfo({
          costAggregationIds: costAddIds.join(','),
          type: type,
          wxDepartmentId: this.datas.params4
        }).then(({data}) => {
          this.heTongInfoList = data
        })
      }
    },
    closeImgViewer() {
      this.imgViewerVisible = false
    },
    showFiles(urls) {
      if (urls) {
        this.imgList = urls ? urls.split(',') : []
        if (this.imgList.length == 0) {
          this.$message({
            message: '暂无附件',
            type: 'warning'
          });
          return
        }
        // 如何文件不是图片 直接下载
        let isImg = true
        this.imgList.forEach(item => {
          if (item.indexOf('.jpg') > -1 || item.indexOf('.png') > -1 || item.indexOf('.jpeg') > -1) {

          } else {
            isImg = false
          }
        })
        if (!isImg) {
          for (let i = 0; i < this.imgList.length; i++) {
            // this.downloadFile(this.imgList[i], this.imgList[i].substring(this.imgList[i].lastIndexOf('/') + 1, this.imgList[i].length))
            // 直接打开
            // this.openUrl(this.imgList[i], this.imgList[i].substring(this.imgList[i].lastIndexOf('/') + 1, this.imgList[i].length))
            // window.open(this.imgList[i], '_blank')
            previewPDF(this.imgList[i])
          }
          return
        }
        this.imgViewerVisible = true
        return
      }
      this.$message({
        type: 'info',
        message: '无合同信息'
      })
    },
    loadFanDianInfo(idArr) {
      if (this.isFanDian && idArr && idArr.length > 0) {
        this.fanDianInfoList = []
        fanDianCustomerInfo({
          costAggregationIds: idArr.join(','),
          wxDepartmentId: this.datas.params4
        }).then(({ data }) => {
          if (data) {
            this.fanDianInfoList = data
          }
        })
      }
    },
    lookContract() {
      this.showContractPreview = true
      this.contractPreviewLoading = true
      console.log(this.contractIds)
      getContractByIds({
        ids: this.contractIds.join(',')
      }).then(res => {
        console.log(res)
        this.contractList = res
      }).catch(err => {

      }).finally(() => {
        this.contractPreviewLoading = false
      })
    },
    onPasteUpload(e) {
      console.log('--------')
      const upload = this.$refs.aaaa
      if (!upload) {
        return
      }
      const items = e.clipboardData.items
      for (const item of items) {
        if (item.type === 'image/png') {
          const file = new File([item.getAsFile()], new Date().getTime() + '.png')
          upload.handleStart(file)
        }
      }
      upload.submit()
    },
    handleCurrentChange(val) {
      if (this.datas.nodeId == '80') {
        this.currentRowId = val.id
      }
    },
    selecRreceiveCompany(code) {
      selectbycode(code).then(res => {
        if (res != undefined) {
          this.accountlist = res.list
        }
      })
    },
    selectAccountById() {
      this.currentRowId = this.datas.formValue.accountId
      selectbyId(this.datas.formValue.accountId).then(res => {
        if (res != undefined) {
          this.choseAccountlist = res.list
        }
      })
    },
    handleClose() {
      this.showUpload = false
      this.fileList = []
      this.picList = []
    },
    submitPic(id) {
      var query = {
        processId: id,
        picList: this.picList.toString()
      }
      // updateReceiptPicUrl(query).then(res=>{
      //   if (res!=undefined) {
      //     this.$message.success(`回执单上传成功！`)
      //     var param = {
      //       processId:id
      //     }
      //     processApi.sendMessage(param).then(res=>{})
      //   }
      // }).finally(this.handleClose())
    },
    toremove(file, fileList) {
      if (file.response != undefined && file.response != null && file.response != '') {
        console.info(file, fileList)
        var list = Object.assign([], this.picList)
        this.picList = []
        for (var i = 0; i < list.length; i++) {
          if (list[i] != file.response.url) {
            this.picList.push(list[i])
          }
        }
      } else {
        console.info(file, fileList)
        var list = Object.assign([], this.picList)
        this.picList = []
        for (var i = 0; i < list.length; i++) {
          if (list[i] != file.url) {
            this.picList.push(list[i])
          }
        }
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 上传前对文件的大小的判断
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.warning('上传图片大小不能超过 5 MB!')
      }
      return isLt5M
    },
    // 上传成功后的回调
    uploadSuccess(response, file, fileList) {
      this.picList.push(response.url)
      console.info(this.picList.toString())
    },
    // 上传错误
    uploadError(response, file, fileList) {
      this.$message.error(`上传失败，请重试！`)
    },
    handleRemove(file, fileList) {
      var thzs = this
      return this.$confirm('此操作将删除该回执单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        thzs.toremove(file, fileList)
        return true
      })
    },
    uploadBackRecord(processId) {
      this.fileList = []
      this.picList = []
      this.showUpload = true
      this.$nextTick(() => {
        console.log(this.$refs.aaaa)
        document.addEventListener('paste', this.onPasteUpload)
      })
      this.initPicList(processId)
    },
    initPicList(id) {
      var query = {
        processId: id
      }
      // getReceiptPicUrl(query).then(res=>{
      //   if (res!=undefined) {
      //     if(!!res.picList && res.picList.length > 0){
      //       this.picList = res.picList
      //       for(var i=0;i<res.picList.length;i++){
      //         var tmp = res.picList[i]
      //         var obj = {
      //           name:"回执单"+(i+1),
      //           url:tmp
      //         }
      //         this.fileList.push(obj)
      //       }
      //     }
      //   }
      // })
    },
    setContractIds(str) {
      if (!str) {
        return
      }
      this.contractIds = str.split(',')
    },
    getData() {
      console.log('data', this.datas.formValue, this.datas.formValue.accountingType)
      console.info(this.datas.params1)
      this.applicationForm = {}
      this.tableData = []
      this.payType = this.datas.params7

      this.setContractIds(this.datas.params9)
      getPayment(this.datas.params1, 0,this.datas.params10,this.datas.params4).then(res => {
        if (res != undefined) {
          this.applicationForm = res.obj
          if ('shipExpense' !== this.datas.params10) {
            this.selecRreceiveCompany(this.applicationForm.invoiceCompany)
          }
          this.selectAccountById()
          this.applicationForm.processId = this.datas.processId
          this.applicationForm.user = res.obj.createBy
          this.applicationForm.textContent = this.datas.formValue.textContent
          this.applicationForm.fileList = this.datas.formValue.fileList
          this.applicationForm.isApplyRecord = true
          this.applicationForm.exter = res.exter
          this.applicationForm.accountingType = 0
          if (this.datas.formValue && this.datas.formValue.accountingType) {
            this.applicationForm.accountingType = this.datas.formValue.accountingType
          }
          const businessTypeBak = res.exter.businessType
          // this.applicationForm.inter = res.inter
          this.applicationForm.price = 0
          this.applicationForm.deductPrice = 0
          // this.applicationForm.dingjin = 0
          if (!this.applicationForm.dingjin) {
            this.applicationForm.dingjin = 0
          }
          this.applicationForm.totalExterAmount = 0
          this.tableData = res.list
          this.deductList = res.deduct
          var businessTypeList = []
          const costAggIds = []
          for (var i = 0; i < this.tableData.length; i++) {
            var tmp = this.tableData[i]
            tmp.charge = tmp.amountDue - tmp.settleAmount
            // this.applicationForm.totalExterAmount += tmp.exterAmount
            // var type=this.costTypeFmt(null,null,this.tableData[i].costTypeName,null)
            var type = this.tableData[i].costTypeName
            if (businessTypeList.indexOf(type) == -1) {
              businessTypeList.push(type)
            }
            // 运费 扣定金 [sungf 2022-06-02]
            // if(this.applicationForm.routeName && this.applicationForm.routeName=='applypayticketshippay'){
            //    if(tmp.dingjin){
            //     this.applicationForm.dingjin += tmp.dingjin
            //   }
            // }
            if(tmp.costAggregationId){
              costAggIds.push(tmp.costAggregationId)
            }
          }
          this.applicationForm.exter.businessType = businessTypeList.toString()
          if (this.applicationForm.exter.businessType == '') {
            this.applicationForm.exter.businessType = businessTypeBak
          }
          for (var i = 0; i < this.deductList.length; i++) {
            var tmp = this.deductList[i]
            this.applicationForm.deductPrice += Number(tmp.totalPrice)
          }
          this.cleanComData()
          this.getCompinentData()
          this.loadFanDianInfo(costAggIds)
          this.loadHeTongInfo(costAggIds,this.payType)
        }
      })
    },
    getCompinentData() {
      // this.$refs.internalAccount.cleandata()
      if (this.inOutSwitch) {
      } else {
        this.$refs.externalAccount.getData(this.applicationForm)
        this.$refs.externalAccount.setFormVale(this.datas && this.datas.formValue)
        this.$nextTick(function() {
          this.checkedSettlement = this.$refs.externalAccount.sendData.checkedSettlement[0]
          console.info(this.$refs.externalAccount.sendData)
        })
      }
    },
    cleanComData() {
      this.$refs.externalAccount.cleandata()
    },
    getComponentsSendData() {
      var outData = this.$refs.externalAccount.sendData
      var newData = {
        outData: outData,
        inData: null
      }
      return outData
    },
    costTypeFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['cost_type'])
    },
    NumFmt(row, column, cellValue, index) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = parseFloat(Math.abs(cellValue).toFixed(2))
      }
      return v
    }
  }
}
</script>

<style scoped>
.contract-image__preview {
    display: flex;
    flex-wrap: wrap;
  }
  .contract-image__preview >>> .el-image {
    margin: 0 5px 5px 0;
    flex: 1 48%;
    width: 200px;
    border: 1px solid #DCDCDC;
  }
  .contract-image__preview >>> .el-image:nth-of-type(even) {
    margin-right: 0;
  }
  .el-table-info >>> .cell{
    text-align: center;
  }
  .el-table-info >>> th {
    background: #EDF5FF;
  }

  .xuanxiang >>> .el-dialog__body{
    padding: 10px 20px;
  }
  .hiddenClass >>> .el-upload {
    display: none;
  }

</style>
