<template>
  <section id="process-contract-wrap">
    <div><span :style="{color: params.standard ==true ? 'green':'orange'}">{{ params.standard == true ? '标准合同' : '非标合同' }}</span></div>
    <div class="content-wrap" style="line-height: 25px;color:#606266;">
      <div><span>合同类型：</span><span>{{ params.contractTypeName }}</span></div>
      <div><span>甲方：</span><span>{{ params.partyAName }}</span></div>
      <div><span>乙方：</span><span>{{ params.partyBName }}</span></div>
      <div v-if="params.partyBImgBiz" style="margin-block: 5px;">
        <span style="vertical-align: top;">乙方营业执照：</span>
        <el-image
          v-for="url in params.partyBImgBiz.split(',')"
          style="width: 140px; height: 140px;margin-right: 20px;border: 1px solid #f5f5f5"
          :src="url"
          @click="handlePictureCardPreview(url)"
          fit="cover">
        </el-image>
      </div>
      <!-- <div><span>合同号：</span><span>{{ params.contractNo }}</span></div> -->
      <div><span>备注：</span><span>{{ params.contractRemark }}</span></div>
      <div v-if="params.fileUrl"><span>合同下载：</span><a style="color:#009ff9;" :href="params.fileUrl" target="_blank">{{ params.fileName }}</a></div>
    </div>

    <div v-if="params.previewFileUrl" style="box-shadow: 0 0 3px #333;margin-top: 10px;">
      <el-button style="float:right;margin: 5px 5px 0 0;" @click="printCn('printImg1')">打印</el-button>
      <div id="printImg1">
      <el-image
        style="width: 100%;"
        :src="params.previewFileUrl"
        :preview-src-list="[params.previewFileUrl]"
      />
    </div>
    </div>
    <div v-else-if="params.fileUrl && (params.fileUrl.split('.').pop()=='jpg' || params.fileUrl.split('.').pop()=='png' || params.fileUrl.split('.').pop()=='jpeg')" style="box-shadow: 0 0 3px #333;margin-top: 10px;">
      <!-- 判断图片类 -->
      <el-button style="float:right;margin: 5px 5px 0 0;" @click="printCn('printImg2')">打印</el-button>
      <div id="printImg2">
      <el-image
        style="width: 100%;"
        :src="params.fileUrl"
        :preview-src-list="[params.fileUrl]"
      />
    </div>
    </div>

    <!-- 图片预览 -->
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </section>
</template>

<script>

export default {
  name: 'Contract',
  props: {
    datas: {
      type: Object
    }
  },
  data() {
    return {
      params: {},
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  mounted() {
    console.log(this.datas)
    this.params = JSON.parse(this.datas.params2)
  },
  methods: {
    printCn(domId) {
      const str = window.document.getElementById(domId).innerHTML
      window.document.body.innerHTML=str;
      window.print();
      window.location.reload()
    },
    handlePictureCardPreview(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.content-wrap{
  >div{
    :first-child {
      width: 120px;
      display: inline-block;
    }
  }
}
</style>
