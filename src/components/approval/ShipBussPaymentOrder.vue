<template>
  <section id="dataform" style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div style="width: 100%;text-align: right;margin-top: -42px;margin-bottom: 10px">
      <el-button type="primary" @click="printApply">打印</el-button>
    </div>
    <table rules="all" frame="box" class="record-table">
      <tr>
        <td style="text-align: left" colspan="6">
          <div style="text-align: center;font-size: 1.4em">
            船舶业务付款申请
          </div>
          <div style="margin-bottom: -15px;text-align: left">
            <span style="padding-left: 20%">付款方式：{{ fukuanFmt(datas.moneytype) }}</span>
            <span style="padding-left: 50%">附件{{ filelength }}张</span>
          </div>
          <el-divider content-position="left"/>
          <div style="display: flex;">
            <div style="margin-top: -10px;margin-bottom: 5px;text-align: left;width: 50%;">
              <span style="padding-left: 10%">凭证号：{{ voucher }}</span>
            </div>
            <div style="margin-top: -10px;margin-bottom: 5px;text-align: right;width: 50%;">
              <span style="padding-right: 10%">付款编号：{{ datas.Numbers.replaceAll(/\-\d{5}$/g, '') }}</span>
            </div>
          </div>
        </td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">公司</td>
        <td style="width: 30%" colspan="2">{{ CompanyNameFmt2(datas.company) }}</td>
        <td style="width: 15%" colspan="1">费用类型</td>
        <td style="width: 30%" colspan="2">{{ datas.selTypeFee && datas.selTypeFee.join('/') }}</td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">船舶</td>
        <td style="width: 40%" colspan="2">{{ datas.selShip }}</td>
        <td style="width: 15%" colspan="1">航次</td>
        <td style="width: 40%" colspan="2">{{ datas.voyage ||'-' }}</td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">业务日期</td>
        <td style="width: 40%" colspan="2">{{ datas.year }}年{{ datas.month }}月{{ datas.ri }}日</td>
        <td style="width: 15%" colspan="1">账期</td>
        <td style="width: 30%" colspan="2">{{ datas.accountPeriod||'-' }}<span v-show="datas.accountPeriod">天</span></td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">支付金额（小写）</td>
        <td style="width: 30%" colspan="2">{{ fmtMoney(datas.sumNum) }}</td>
        <td style="width: 15%" colspan="1">支付金额（大写）</td>
        <td style="width: 40%" colspan="2">{{ datas.sumChinese }}</td>

      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">对方单位全称</td>
        <td style="width: 40%" colspan="2">{{ ordercompanynameFmt(datas.opositeUnit) }}</td>
        <td style="width: 15%" colspan="1">用途</td>
        <td style="width: 30%" colspan="2">{{ yongtu }}</td>
      </tr>
      <tr style="height: 40px">
        <td style="width: 15%" colspan="1">对方单位账号</td>
        <td style="width: 40%" colspan="2">{{ orderbankaccountFmt(datas.zhanghunum) }}</td>
        <td style="width: 15%" colspan="1">开户行</td>
        <td style="width: 30%" colspan="2">{{ orderbanknameFmt(datas.kaihu) }}</td>
      </tr>

      <!--    <div style="width: 100%;text-align: left">-->
      <tr>
        <td style="width: 100%;height: 80px;text-align: left" colspan="6">
          <div style="margin-top: -30px">
            备注：
            <span class="pt-show">{{ remark }}</span>
            <span class="pt-print" >{{ remark.replace(/\#.*\#/g, '') }}</span>
          </div>
        </td>
      </tr>
        <!-- 付款信息 -->
        <PaymentWaterList :data-obj="datas" trStyle="height: 40px" tdColspans="1,2,3"></PaymentWaterList>


      <tr v-if="splist && splist.length && datas.flowName !== '经营付款(油品)'">
        <td style="width: 70%;height: 60px;text-align: left" colspan="4">
          <div>
            <span>经办人：{{ splist[0] }} </span>
            <span style="margin-left: 15%">部门负责人：{{ splist[1] }} </span>
            <span style="margin-left: 15%">会计审核：{{ splist[2] }} </span>
          </div>
        </td>
        <td style="width: 30%;height: 60px;text-align: left" colspan="2" rowspan="2">
          <div style="margin-left: 15px">
            总裁：{{ splist[6] == '洪安前' ? splist[6] : ' ' }}
          </div>
        </td>
      </tr>
      <tr v-if="splist && splist.length && datas.flowName !== '经营付款(油品)'">
        <td style="width: 70%;height: 60px;text-align: left" colspan="4">
          <div style="display:grid; grid-template-columns: 33.33% 33.33% 33.33%;">
            <span>{{datas.sponsorCompany && datas.sponsorCompany=='SDNM'?'副总经理：':'财务经理：'}}{{ splist[3] }} </span>
            <span>总经理：{{ splist[4] }} </span>
            <span>财务总监：{{ splist[5] }} </span>
          </div>
        </td>
      </tr>
      <tr v-if="splist && splist.length && datas.flowName === '经营付款(油品)'">
        <td style="width: 70%;height: 60px;text-align: left" colspan="4">
          <div>
            <span>经办人：{{ splist[0] }} </span>
            <span style="margin-left: 10%">采购经理：{{ splist[1] }}</span>

            <span style="margin-left: 10%">部门负责人：{{ splist[2] }} </span>
            <span style="margin-left: 10%">会计审核：{{ splist[3] }} </span>
          </div>
        </td>
        <td style="width: 30%;height: 60px;text-align: left" colspan="2" rowspan="2">
          <div style="margin-left: 15px">
            总裁：{{ splist[6] == '洪安前' ? splist[6] : ' ' }}
          </div>
        </td>
      </tr>
      <tr v-if="splist && splist.length && datas.flowName === '经营付款(油品)'">
        <td style="width: 70%;height: 60px;text-align: left" colspan="4">
          <div>
            <span>出纳：{{ splist[7] }} </span>
            <span style="margin-left: 15%">总经理：{{ splist[4] }} </span>
            <span style="margin-left: 15%">财务总监：{{ splist[5] }} </span>
          </div>
        </td>
      </tr>
      <!--    </div>-->
       <tr v-show="spLabelList && spLabelList.length > 0">
        <td style="width: 70%;height: 60px;text-align: left" colspan="4">
          <div>
            <span v-for="(sitem,sindex) in spLabelOne" :key="sindex" :style="{'margin-left':sindex==0?'':'15%'}">{{sitem.label}}：{{ sitem.userName }} </span>
          </div>
        </td>
        <td style="width: 30%;height: 60px;text-align: left" colspan="2" rowspan="2">
          <div style="margin-left: 15px">
           总裁：{{ spLabelEnd.label && spLabelEnd.label=='总裁'? spLabelEnd.userName:" " }}
          </div>
        </td>
      </tr>
      <tr v-show="spLabelList && spLabelList.length > 0">
        <td style="width: 70%;height: 60px;text-align: left" colspan="4">
          <div>
            <span v-for="(sitem,sindex) in spLabelTwo" :key="sindex" :style="{'margin-left':sindex==0?'':'15%'}">{{sitem.label}}：{{ sitem.userName }} </span>
          </div>
        </td>
      </tr>
    </table>
  </section>
</template>

<script>
import { getprocesspeople } from '@/api/business/processapi'
import { getAllCompany } from '@/api/business/wxDepartmentapi'
import { getExternalAccount } from '@/api/business/sysExternalAccount'
import {getProcessVoucher} from "../../api/system/processVoucherRecord";
import PaymentWaterList from '@/components/paymentWaterList/index.vue'
export default {
  components: {
    PaymentWaterList
  },
  name: 'PaymentOrder',
  props: {
    datas: {
      type: Object
    }
  },
  computed: {
    remark() {
      const parent = this.$parent.$parent
      const formValue = parent.formValue
      if (this.datas.remake) {
        return this.datas.remake
      }
      if (!formValue) {
        return ''
      }
      if (formValue.reason && formValue.reason !== '') {
        return formValue.reason
      }
      if (formValue.remark && formValue.remark !== '') {
        return formValue.remark
      }
      if (!formValue.cruddata) {
        return ''
      }
      let rmk = ''
      for (const key of Object.keys(formValue.cruddata)) {
        for (const item of formValue.cruddata[key]) {
          if (item.remark && item.remark !== '') {
            // return item.remark
            // rmk = item.remark
            if (rmk) {
              rmk = rmk + '；'
            }
            rmk = rmk + item.remark
          }
          if (item.remake && item.remake !== '') {
            // return item.remake
            // rmk = item.remake
            if (rmk) {
              rmk = rmk + '；'
            }
            rmk = rmk + item.remake
          }
          // if (item.purpose && item.purpose !== '') {
          //   return item.purpose
          // }
        }
      }
      return rmk
      // return ''
    },
    spLabelOne() {
      if (this.spLabelList && this.spLabelList.length > 0) {
       return this.spLabelList.slice(0, 3)
      }
      return []
    },
    spLabelTwo() {
      if (this.spLabelList && this.spLabelList.length > 3) {
        if (this.spLabelEnd && this.spLabelEnd.label == '总裁') {
            return this.spLabelList.slice(3,-1)
        }
        return this.spLabelList.slice(3)
      }
      return []
    },
    spLabelEnd() {
      if (this.spLabelList && this.spLabelList.length > 0) {
        return this.spLabelList[this.spLabelList.length-1]
      }
      return {}
    }
  },
  data() {
    return {
      moneydetail: [],
      allmoney: [],
      spLabelList:[],
      splist: [],
      filelength: 0,
      PaidMoney: 0,
      allpayMoney: 0,
      companylist: [],
      yongtu: '',
      ExternalAccount: [],
      dicData: [
        {
          label: '现金',
          value: 1
        }, {
          label: '网银',
          value: 0
        }, {
          label: '银行承兑',
          value: 2
        }],
      unit: new Array('仟', '佰', '拾', '', '仟', '佰', '拾', '', '角', '分'),
      voucher:null,
    }
  },
  created() {
    getprocesspeople(this.datas.processid).then(res => {

      if (res.spList) {
        this.spLabelList = res.spList
        return
      }
      this.splist = res.user
      const parentCom = this.$parent.$parent
      if (new Set(['ExternalPaymentApplicationSYSY', 'ExternalPaymentApplicationCGCX', 'ExternalPaymentApplicationCGWL', 'ExternalPaymentApplicationWANGLIAN']).has(parentCom.flowCode)) {
        if (parentCom.obj.shebao === '是') {
          if (this.splist.length > 2) {
            this.splist.splice(2, 0, '')
          }
        } else {
          if (this.splist.length > 2) {
            this.splist.splice(1, 0, '')
            this.splist.splice(2, 0, '')
          }
        }
        return
      }

      if (parentCom.flowCode === 'ExternalPaymentApplicationHNHS') {
        if (parentCom.obj.shebao === '是') {
          if (this.splist.length >= 5) {
            // 把第4和第5个组合放到第四个里面，同时去掉第5个（海南和盛的流程中两个总经理审批：郑伟民、张清云）
            this.splist.splice(4, 2, `${this.splist[4]},${this.splist[5]}`)
          }

          if (this.splist.length >= 3) {
            this.splist.splice(2, 2, `${this.splist[2]},${this.splist[3]}`)
          }


          if (this.splist.length > 2) {
            this.splist.splice(2, 0, '')
          }
        } else {

          if (this.splist.length >= 4) {
            this.splist.splice(3, 2, `${this.splist[3]},${this.splist[4]}`)
          }

          if (this.splist.length >= 2) {
            this.splist.splice(1, 2, `${this.splist[1]},${this.splist[2]}`)
          }

          if (this.splist.length > 2) {
            this.splist.splice(1, 0, '')
            this.splist.splice(2, 0, '')
          }
        }
        console.log(this.splist)
        return
      }
      if (this.datas.sponsorCompany === 'CGWL' && this.splist.length > 2) {
        // 因为成功网联的流程没有财务审批这一步，所以手动添加一个空节点
        // 往 this.splist 的第3个元素前插入一个''
        this.splist.splice(2, 0, '')
        return
      }
    })
    if(this.datas && Object.keys(this.datas).length > 0) {
      // this.datas.kaihu
      // this.datas.zhanghunum
      // this.datas.opositeUnit
      const idsArr = [this.datas.kaihu, this.datas.zhanghunum, this.datas.opositeUnit]
      const ids = idsArr.filter(item => item).join(',')
      getExternalAccount(ids).then(res => {
        this.ExternalAccount = res.data
      })
      getAllCompany(this.datas.company).then(res => {
        this.companylist = res.data
      })
    } else {
      setTimeout(() => {
        const idsArr = [this.datas.kaihu, this.datas.zhanghunum, this.datas.opositeUnit]
        const ids = idsArr.filter(item => item).join(',')
        getExternalAccount(ids).then(res => {
          this.ExternalAccount = res.data
        })
        getAllCompany(this.datas.company).then(res => {
          this.companylist = res.data
        })
      }, 1000)
    }
    // getExternalAccount().then(res => {
    //   this.ExternalAccount = res.data
    // })
    getProcessVoucher(this.datas.processid).then(res=>{
      this.voucher =res.data
    })
    this.chuli()
  },
  methods: {
    printApply() {
      var str = window.document.getElementById('dataform').innerHTML

      // 针式打印字体优化
      const style = `<style>
      html{
        font-family: "Microsoft YaHei","Arial","SimSun", "SimHei","宋体";
      }
      </style>`
      str = style + str
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    },
    chuli() {
      this.datas.remake = ""
      this.yongtu = ''
      console.log(this.datas)
      for (const fliekey of Object.keys(this.datas.fileListg)) {
        const filelist = this.datas.fileListg[fliekey]
        if (filelist) {
          this.filelength += filelist.length
        }
      }
      for (const key of Object.keys(this.datas.cruddata)) {
        const data = this.datas.cruddata[key]
        if (data.length <= 0) {
          continue
        }
        for (var z = 0; z < data.length; z++) {
          var dataitem = data[z]
          this.allpayMoney = dataitem.allmoney
          this.PaidMoney = dataitem.PaidMoney
          if (this.yongtu && dataitem.purpose) {
            this.yongtu += "、"
          }
          this.yongtu += dataitem.purpose
          this.moneydetail.push(dataitem)
          if (dataitem.remake) {
            this.datas.remake += dataitem.remake + " "
          }
        }
      }
      for (var a = 0; a < this.moneydetail.length; a++) {
        var item = this.moneydetail[a]
        this.quzheng(item)
      }
      this.allmoney.sum = this.datas.sumNum
      this.quzheng(this.allmoney)
      this.NumberToChinese(this.datas.sumNum)
      this.datas.year = this.datas.date.slice(0, 4)
      this.datas.month = this.datas.date.slice(5, 7)
      this.datas.ri = this.datas.date.slice(8, 10)
    },
    toDx(n) { // 阿拉伯数字转换函数
      switch (n) {
        case '0':
          return '零'
        case '1':
          return '壹'
        case '2':
          return '贰'
        case '3':
          return '叁'
        case '4':
          return '肆'
        case '5':
          return '伍'
        case '6':
          return '陆'
        case '7':
          return '柒'
        case '8':
          return '捌'
        case '9':
          return '玖'
      }
    },
    NumberToChinese(money) {
      var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆',
        '柒', '捌', '玖');
      // 基本单位
      var cnIntRadice = new Array('', '拾', '佰', '仟');
      // 对应整数部分扩展单位
      var cnIntUnits = new Array('', '万', '亿', '兆');
      // 对应小数部分单位
      var cnDecUnits = new Array('角', '分', '毫', '厘');
      // 整数金额时后面跟的字符
      var cnInteger = '整';
      // 整型完以后的单位
      var cnIntLast = '元';
      // 最大处理的数字
      var maxNum = 999999999999999.9999;
      // 金额整数部分
      var integerNum;
      // 金额小数部分
      var decimalNum;
      // 输出的中文金额字符串
      var chineseStr = '';
      // 分离金额后用的数组，预定义
      var parts;
      if (money == '') {
        return '';
      }
      money = parseFloat(money);
      if (money >= maxNum) {
        // 超出最大处理数字
        return '';
      }
      if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger;
        return chineseStr;
      }
      // 转换为字符串
      money = money.toString();
      if (money.indexOf('.') == -1) {
        integerNum = money;
        decimalNum = '';
      } else {
        parts = money.split('.');
        integerNum = parts[0];
        decimalNum = parts[1].substr(0, 4);
      }
      // 获取整型部分转换
      if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0;
        var IntLen = integerNum.length;
        for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1);
          var p = IntLen - i - 1;
          var q = p / 4;
          var m = p % 4;
          if (n == '0') {
            zeroCount++;
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0];
            }
            // 归零
            zeroCount = 0;
            chineseStr += cnNums[parseInt(n)]
              + cnIntRadice[m];
          }
          if (m == 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q];
          }
        }
        chineseStr += cnIntLast;
      }
      // 小数部分
      if (decimalNum != '') {
        var decLen = decimalNum.length;
        for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1);
          if (n != '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i];
          }
        }
      }
      if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger;
      } else if (decimalNum == '') {
        chineseStr += cnInteger;
      }

      this.datas.sumChinese =  chineseStr;
    },
    fukuanFmt(id) {
      for (var a = 0; a < this.dicData.length; a++) {
        var item = this.dicData[a]
        if (item.value === id) {
          return item.label
        }
      }
      return ''
    },
    CompanyNameFmt2(id) {
      for (var a = 0; a < this.companylist.length; a++) {
        var item = this.companylist[a]
        if (item.id === id) {
          return item.label
        }
      }
      return ''
    },
    ordercompanynameFmt(id) {
      if (!id) {
        return '-'
      }
      for (var a = 0; a < this.ExternalAccount.length; a++) {
        var item = this.ExternalAccount[a]
        if (item.id === Number(id)) {
          return item.companyName
        }
      }
      return '-'
    },
    orderbanknameFmt(id) {
      if (!id) {
        return '-'
      }
      for (var a = 0; a < this.ExternalAccount.length; a++) {
        var item = this.ExternalAccount[a]
        if (item.id === Number(id)) {
          return item.bankName
        }
      }
      return '-'
    },
    orderbankaccountFmt(id) {
      if (!id) {
        return '-'
      }
      for (var a = 0; a < this.ExternalAccount.length; a++) {
        var item = this.ExternalAccount[a]
        if (item.id === Number(id)) {
          return item.bankAccount
        }
      }
      return '-'
    },
    quzheng(item) {
      item.wan = Math.floor(item.sum / 10000)
      item.qian = Math.floor((item.sum - item.wan * 10000) / 1000)
      item.bai = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000) / 100)
      item.shi = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100) / 10)
      item.yuan = Math.floor(item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10)
      item.mao = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10 - item.yuan) * 10)
      item.fen = Math.floor((item.sum - item.wan * 10000 - item.qian * 1000 - item.bai * 100 - item.shi * 10 - item.yuan - item.mao / 10) * 100)
      if (item.wan > 0) {
        return
      } else if (item.wan === 0 && item.qian > 0) {
        item.wan = '￥'
      } else if (item.qian === 0 && item.bai > 0) {
        item.wan = ''
        item.qian = '￥'
      } else if (item.bai === 0 && item.shi > 0) {
        item.wan = ''
        item.qian = ''
        item.bai = '￥'
      } else if (item.shi === 0 && item.yuan > 0) {
        item.wan = ''
        item.qian = ''
        item.bai = ''
        item.shi = '￥'
      }
    }
  }
}
</script>

<style scoped>
.record-table >>> .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
}

td {
  width: 5%;
  text-align: center;
  height: 25px;
  color: #606266;
}

.record-title {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

.record-table {
  width: 100%;
}

.table-title {
  width: 15%;
}

.td-func {
  width: 10%;
}

.td-address {
  width: 15%;
  text-align: right;
}

.td-checkbox {
  text-align: left;
}

.row-price {
  height: 20px;
}

.price-table {
  width: 100%;
}

.td-mar {
  width: 50%;
}

.td-mar-new {
  width: 33%;
}

.checkInput {
  height: 15px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}

.checkInput2 {
  width: 80px;
  height: 20px;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: #A8A8A9 1px solid;
  text-align: center;
}
@media print {

  #dataform{
    padding: 0px;
  }
  .record-table {
    margin-top: 30px;
    margin-left: 90px;
    width:calc(100vw - 90px) !important;
    font-size: 13px;
    line-height: 1.5em;
  }
}
</style>
