<template>
  <section>
    <div
      style="display: inline-flex;font-size: 22px;width: 100%;font-weight: 500;text-align: left;justify-content: space-between;"
      v-if="xianshiData">
      <div style="display: flex;flex-wrap: wrap;">
        <div class="biaotou">船舶名称：{{ publicFmtn(shipname) }}</div>
        <!-- <div class="biaotou">船期：{{myFmtDateTime(ShipLineBean.shipTime,"MM/DD")}}</div> -->
        <div class="biaotou" style="margin-left: 18px;">
          {{deptCode == 'HNHS'?'卸空完货':'离港'}}日期：{{ myFmtDateTime(ShipLineBean.epartureTimeDate, "MM/DD") }}
        </div>
        <!-- <div class="biaotou" style="margin-left: 18px;">航次号：{{publicFmtn(ShipLineBean.voyageNumber)}}</div> -->
        <div class="biaotou" style="margin-left: 18px;">
          挂账号：{{ publicFmtn(ShipLineBean.onAccountNum) }}
          {{ ShipLineBean && ShipLineBean.guaTime && "("+myFmtDateTime(ShipLineBean.guaTime, "YYYY")+")" || '--' }}
        </div>
        <div class="biaotou" style="margin-left: 18px;">
          航次号：{{ publicFmtn(ShipLineBean.voyageNumber) }}
        </div>
        <div class="biaotou" style="margin-left: 18px;">租船员：{{ fmtUserNamen(ShipLineBean.findShipUser) }}</div>
        <div class="biaotou" style="margin-left: 18px" v-if="datas && datas.finishTime">
          改帐日期：{{ datas && datas.finishTime && myFmtDateTime(datas.finishTime, "YYYY-MM-DD") || '--' }}
        </div>
        <div class="biaotou" style="margin-left: 18px" v-if="ShipLineBean && ShipLineBean.goods_type_vals">
          货物种类：{{ ShipLineBean && ShipLineBean.goods_type_vals || '--' }}
        </div>
        <br/>
        <div class="biaotou" style="margin-left: 30px;margin-top: 10px">
          船东结算公司：{{ fmtsettleCompany(ShipLineBean.settleCompany) }}
        </div>
      </div>
      <div class="biaotou">核算：{{ areaName || '--' }}</div>

    </div>
    <div style="margin-top: 10px;margin-bottom: 10px;">
      <div v-if="xianshiData" style="font-size: 22px;width: 100%;font-weight: 500;text-align: left" class="biaotou">
        改账理由：{{ publicFmtn(reasonData) }}
      </div>
    </div>
    <div class="print-hidden" style="margin-top: 10px;width: 100%;text-align: left;margin-left: 30px">
      <el-tag style="margin-right: 10px;cursor: pointer;" :type="showshipcostin?'success':'info'"
              @click="showshipcostIn" v-if="ShipCostIn.length>0"
      >船上费用收入
      </el-tag>
      <el-tag style="margin-right: 10px;cursor: pointer;" :type="showgoodscost?'success':'info'"
              @click="changeGoodsCost" v-if="GoodsCost.length>0"
      >运费收入
      </el-tag>
      <el-tag style="margin-right: 10px;cursor: pointer;" :type="showcostdetailIn?'success':'info'"
              @click="changeCostDetailIn" v-if="CostDetailIn.length>0"
      >货物费用收入
      </el-tag>

      <el-tag style="margin-right: 10px;cursor: pointer;" :type="showshipline?'success':'info'"
              @click="changeshipline" v-if="ShipLine.length>0"
      >垫资买票
      </el-tag>
      <el-tag style="margin-right: 10px;cursor: pointer;" :type="showshippay?'success':'info'"
              @click="changeshippay" v-if="ShipPay.length>0"
      >运费支出
      </el-tag>
      <el-tag style="margin-right: 10px;cursor: pointer;" :type="showshipcost?'success':'info'"
              @click="changeshipCost" v-if="ShipCostOut.length>0"
      >船上费用支出
      </el-tag>

      <el-tag style="margin-right: 10px;cursor: pointer;" :type="showcostdetail?'success':'info'"
              @click="changeCostDetail" v-if="CostDetail.length>0"
      >货物费用支出
      </el-tag>

    </div>

    <template v-if="ShipCostIn.length>0 && showshipcostin">
      <el-divider content-position="left" style="cursor: pointer">船上费用收入</el-divider>
      <el-table
        class="el-table-info"
        :data="ShipCostIn"
        stripe
        :row-class-name="tableRowClassName"
        style="width: 100%;margin-top: 30px"
        :show-overflow-tooltip="true"
        border
      >
        <el-table-column
          prop="sysSupplierId"
          :formatter="supplierListFmt"
          label="公司"
        >
          <template slot-scope="scope">
            <div style="width: 100%;text-align: right" v-if="scope.row.gaizhangtype === 1"><span
              style="color: red">new</span></div>
            <div v-if="scope.row.sysSupplierId == scope.row.sysSupplierIdold || scope.row.sysSupplierIdold == null">
              {{ supplierListFmtn(scope.row.sysSupplierId) }}
            </div>
            <div v-if="scope.row.sysSupplierId != scope.row.sysSupplierIdold && scope.row.sysSupplierIdold != null">
              <span
                style="text-decoration:line-through; color:red">{{ supplierListFmtn(scope.row.sysSupplierIdold) }}</span>
              {{ supplierListFmtn(scope.row.sysSupplierId) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costProject"
          label="费用类型"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.costProject == scope.row.costProjectold || scope.row.costProjectold == null">
              {{ costProjectFmtn(scope.row.costProject) }}
            </div>
            <div v-if="scope.row.costProject != scope.row.costProjectold && scope.row.costProjectold != null"><span
              style="text-decoration:line-through; color:red">{{ costProjectFmtn(scope.row.costProjectold) }}</span>
              {{ costProjectFmtn(scope.row.costProject) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costPriceNo"
          label="价格"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.costPriceNo == scope.row.costPriceNoold || scope.row.costPriceNoold == null">
              {{ scope.row.costPriceNo }}
            </div>
            <div v-if="scope.row.costPriceNo != scope.row.costPriceNoold && scope.row.costPriceNoold !=null">
              <span style="text-decoration:line-through; color:red">{{ scope.row.costPriceNoold }}</span>
              {{ scope.row.costPriceNo }}
              差额({{ toSubt(scope.row.costPriceNo, scope.row.costPriceNoold) }})
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="tax"
          label="税率"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.tax == scope.row.taxold || scope.row.taxold == null">
              {{ billTypeFmtn(scope.row.tax) }}
            </div>
            <div v-if="scope.row.tax != scope.row.taxold && scope.row.taxold != null"><span
              style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.taxold) }}</span>
              {{ billTypeFmtn(scope.row.tax) }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="GoodsCost.length>0 && showgoodscost">
      <el-divider content-position="left" style="cursor: pointer">运费收入</el-divider>
      <el-table
        class="el-table-info"
        :data="GoodsCost"
        stripe
        style="width: 100%;margin-top: 30px"
        :span-method="objectSpanMethod"
        :show-overflow-tooltip="true"
        border
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          prop="seckehu"
          :formatter="publicFmt"
          label="客户"
        >
          <template slot-scope="scope">
            <div style="width: 100%;text-align: right" v-if="scope.row.gaizhangtype === 1"><span
              style="color: red">new</span></div>
            <div>{{ scope.row.seckehu }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="finalwarf"
          :formatter="publicFmt"
          label="终到港"
        >
        </el-table-column>
        <el-table-column
          prop="finduser"
          :formatter="fmtUserName"
          label="业务员"
        >
        </el-table-column>
        <el-table-column
          prop="tonnage"
          :formatter="publicFmt"
          label="吨位"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.tonnage == scope.row.tonnageold">{{ scope.row.tonnage }}</div>
            <div v-if="scope.row.tonnage != scope.row.tonnageold">
              <span style="text-decoration:line-through; color:red">{{ scope.row.tonnageold }}</span>
              {{ scope.row.tonnage }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="contractCompany"
          :formatter="costProjectC"
          label="合同公司"
        >
        </el-table-column>
        <el-table-column
          prop="freightNo"
          label="海运费"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.freightNo == scope.row.freightNoold  || scope.row.freightNoold == null">
              {{ scope.row.freightNo }}
            </div>
            <div v-if="scope.row.freightNo != scope.row.freightNoold && scope.row.freightNoold !=null"><span
              style="text-decoration:line-through; color:red">{{ scope.row.freightNoold }}</span>
              {{ scope.row.freightNo }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="taxFreight"
          label="税率"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.taxFreight == scope.row.taxFreightold || scope.row.taxFreightold == null">
              {{ billTypeFmtn(scope.row.taxFreight) }}
            </div>
            <div v-if="scope.row.taxFreight != scope.row.taxFreightold && scope.row.taxFreightold != null"><span
              style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.taxFreightold) }}</span>
              {{ billTypeFmtn(scope.row.taxFreight) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="dailiNo"
          :formatter="publicFmt"
          label="代理费"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.dailiNo == scope.row.dailiNoold || scope.row.dailiNoold == null">
              {{ scope.row.dailiNo }}
            </div>
            <div v-if="scope.row.dailiNo != scope.row.dailiNoold && scope.row.dailiNoold !=null"><span
              style="text-decoration:line-through; color:red">{{ scope.row.dailiNoold }}</span> {{ scope.row.dailiNo }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="taxDaili"
          :formatter="billTypeFmt"
          label="税率"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.taxDaili == scope.row.taxDailiold || scope.row.taxDailiold == null">
              {{ billTypeFmtn(scope.row.taxDaili) }}
            </div>
            <div v-if="scope.row.taxDaili != scope.row.taxDailiold && scope.row.taxDailiold != null"><span
              style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.taxDailiold) }}</span>
              {{ billTypeFmtn(scope.row.taxDaili) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="合计">
          <template slot-scope="scope">
          <span
            v-if="isOladHeji(scope.row.tonnage,scope.row.tonnageold,scope.row.freightNo,scope.row.freightNoold,scope.row.dailiNo,scope.row.dailiNoold)"
            style="text-decoration:line-through; color:red;margin-right: 5px;">
                    {{ multisHeji(scope.row.tonnage, scope.row.tonnageold, scope.row.freightNo, scope.row.freightNoold, scope.row.dailiNo, scope.row.dailiNoold) }}
                  </span>
            <span>{{ multis(scope.row.tonnage, scope.row.freightNo, scope.row.tonnage, scope.row.dailiNo) }}</span>
            <span
              v-if="isOladHeji(scope.row.tonnage,scope.row.tonnageold,scope.row.freightNo,scope.row.freightNoold,scope.row.dailiNo,scope.row.dailiNoold)">
                    差额({{ diffHeji(scope.row.tonnage, scope.row.tonnageold, scope.row.freightNo, scope.row.freightNoold, scope.row.dailiNo, scope.row.dailiNoold) }})
                  </span>
          </template>
        </el-table-column>
        <el-table-column
          label="差额小计"
          prop="sumDiff"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.sumDiff }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="CostDetailIn.length>0 && showcostdetailIn">
      <el-divider content-position="left" style="cursor: pointer">货物费用收入</el-divider>
      <el-table
        :row-class-name="tableRowClassName"
        class="el-table-info"
        :data="CostDetailIn"
        stripe
        :span-method="objectSpanMethod"
        style="width: 100%;margin-top: 30px;"
        :show-overflow-tooltip="true"
        border
      >
        <el-table-column
          prop="kehu"
          :formatter="publicFmt"
          label="客户"
        >
          <template slot-scope="scope">
            <div style="width: 100%;text-align: right" v-if="scope.row.gaizhangtype === 1"><span
              style="color: red">new</span></div>
            <!-- <div>{{scope.row.kehu}}</div> -->
            <div>{{ kehuListFmtn(scope.row.companyName) }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="gangkou"
          :formatter="publicFmt"
          label="终到港"
        >
        </el-table-column>
        <el-table-column
          prop="tonnage"
          :formatter="publicFmt"
          label="吨位"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.tonnage == scope.row.tonnageold || scope.row.tonnageold == null">
              {{ scope.row.tonnage }}
            </div>
            <div v-if="scope.row.tonnage != scope.row.tonnageold && scope.row.tonnageold != null">
              <span style="text-decoration:line-through; color:red">{{ scope.row.tonnageold }}</span>
              {{ scope.row.tonnage }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costName"
          label="费用类型"
          :formatter="costTypeFmt"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.costName == scope.row.costNameold || scope.row.costNameold == null">
              {{ costInFmtn(scope.row.costName) }}
            </div>
            <div v-if="scope.row.costName != scope.row.costNameold && scope.row.costNameold != null"><span
              style="text-decoration:line-through; color:red">{{ costInFmtn(scope.row.costNameold) }}</span>
              {{ costTypeFmtn(scope.row.costName) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="priceNo"
          :formatter="publicFmt"
          label="价格"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.priceNo == scope.row.priceNoold || scope.row.priceNoold == null">
              {{ scope.row.priceNo }}
            </div>
            <div v-if="scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null">
              <span style="text-decoration:line-through; color:red">{{ scope.row.priceNoold }}</span>
              {{ scope.row.priceNo }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="invoiceType"
          label="发票税率">
          <template slot-scope="scope">
            <div v-if="scope.row.invoiceType == scope.row.invoiceTypeold || scope.row.invoiceTypeold == null">
              {{ billTypeFmtn(scope.row.invoiceType) }}
            </div>
            <div v-if="scope.row.invoiceType != scope.row.invoiceTypeold && scope.row.invoiceTypeold != null"><span
              style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.invoiceTypeold) }}</span>
              {{ billTypeFmtn(scope.row.invoiceType) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="合计">
          <template slot-scope="scope">
            <span v-if="scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null"
                  style="text-decoration:line-through; color:red;margin-right: 5px;">{{ multi(scope.row.tonnage, scope.row.priceNoold) }}</span>
            <span>{{ multi(scope.row.tonnage, scope.row.priceNo) }}</span>
            <span v-if="scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null">
                    差额({{ diffHeji2(scope.row.tonnage, scope.row.priceNo, scope.row.priceNoold) }})
                  </span>
          </template>
        </el-table-column>
        <el-table-column
          label="差额小计"
          prop="sumDiff"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.sumDiff }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="ShipLine.length>0 && showshipline">
      <el-divider content-position="left" style="cursor: pointer">垫资买票</el-divider>
      <el-table
        class="el-table-info"
        :data="ShipLine"
        stripe
        style="width: 100%;margin-top: 30px"
        :show-overflow-tooltip="true"
        border
        :cell-class-name="rowClass"
      >
        <el-table-column
          label="垫资"
        >
          <el-table-column
            prop="dianMoney"
            label="垫资价格"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.dianMoney == scope.row.dianMoneyold || scope.row.dianMoneyold == null">
                {{ scope.row.dianMoney }}
              </div>
              <div v-if="scope.row.dianMoney != scope.row.dianMoneyold && scope.row.dianMoneyold != null">
                <span style="text-decoration:line-through; color:red">{{ scope.row.dianMoneyold }}</span>
                {{ scope.row.dianMoney }}
                差额({{ toSubt(scope.row.dianMoney, scope.row.dianMoneyold) }})
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="dianTax"
            label="垫资税率"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.dianTax == scope.row.dianTaxold||scope.row.dianTaxold ==null">
                {{ billTypeFmtn(scope.row.dianTax) }}
              </div>
              <div v-if="scope.row.dianTax != scope.row.dianTaxold &&  scope.row.dianTaxold != null"><span
                style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.dianTaxold) }}</span>
                {{ billTypeFmtn(scope.row.dianTax) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="dianCompany"
            :formatter="costProjectdian"
            label="垫资公司"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="买票"
        >
          <el-table-column
            prop="maiMoney"
            label="买票价格"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.maiMoney == scope.row.maiMoneyold || scope.row.maiMoneyold ==null">
                {{ scope.row.maiMoney }}
              </div>
              <div v-if="scope.row.maiMoney != scope.row.maiMoneyold && scope.row.maiMoneyold != null">
                <span style="text-decoration:line-through; color:red">{{ scope.row.maiMoneyold }}</span>
                {{ scope.row.maiMoney }}
                差额({{ toSubt(scope.row.maiMoney, scope.row.maiMoneyold) }})
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="maiTax"
            label="买票税率"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.maiTax == scope.row.maiTaxold || scope.row.maiTaxold == null">
                {{ billTypeFmtn(scope.row.maiTax) }}
              </div>
              <div v-if="scope.row.maiTax != scope.row.maiTaxold && scope.row.maiTaxold != null"><span
                style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.maiTaxold) }}</span>
                {{ billTypeFmtn(scope.row.maiTax) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="contractCode"
            :formatter="costProjectmai"
            label="买票公司"
          >
          </el-table-column>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="ShipPay.length>0 && showshippay">
      <el-divider content-position="left" style="cursor: pointer">运费支出</el-divider>
      <el-table
        class="el-table-info"
        :data="ShipPay"
        stripe
        style="width: 100%;margin-top: 30px"
        :show-overflow-tooltip="true"
        border
        :cell-class-name="rowClass"
      >
        <el-table-column
          prop="startPort"
          label="起始港"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.startPort == scope.row.startPortold">{{ fmtPortName(scope.row.startPort) }}</div>
            <div v-if="scope.row.startPort != scope.row.startPortold"><span
              style="text-decoration:line-through; color:red">{{ fmtPortName(scope.row.startPortold) }}</span>
              {{ fmtPortName(scope.row.startPort) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="endPort"
          label="目的港"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.endPort == scope.row.endPortold">{{ fmtPortName(scope.row.endPort) }}</div>
            <div v-if="scope.row.endPort != scope.row.endPortold"><span style="text-decoration:line-through; color:red">{{ fmtPortName(scope.row.endPortold) }}</span>
              {{ fmtPortName(scope.row.endPort) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="loadWharf"
          label="装货码头"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.loadWharf == scope.row.loadWharfold">{{ fmtWharfName(scope.row.loadWharf) }}</div>
            <div v-if="scope.row.loadWharf != scope.row.loadWharfold"><span
              style="text-decoration:line-through; color:red">{{ fmtWharfName(scope.row.loadWharfold) }}</span>
              {{ fmtWharfName(scope.row.loadWharf) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="finalWharf"
          label="卸货码头"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.finalWharf == scope.row.finalWharfold">{{ fmtWharfName(scope.row.finalWharf) }}</div>
            <div v-if="scope.row.finalWharf != scope.row.finalWharfold"><span
              style="text-decoration:line-through; color:red">{{ fmtWharfName(scope.row.finalWharfold) }}</span>
              {{ fmtWharfName(scope.row.finalWharf) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="settleTonnage"
          :formatter="publicFmt"
          label="吨位"
        >
        </el-table-column>
        <el-table-column
          prop="shipPay"
          label="船价"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.shipPay == scope.row.shipPayold || scope.row.shipPayold ==null">
              {{ scope.row.shipPay }}
            </div>
            <div v-if="scope.row.shipPay != scope.row.shipPayold && scope.row.shipPayold !=null"><span
              style="text-decoration:line-through; color:red">{{ scope.row.shipPayold }}</span> {{ scope.row.shipPay }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="payBill"
          label="税率"
        >

          <template slot-scope="scope">
            <div v-if="scope.row.payBill == scope.row.payBillold || scope.row.payBillold == null">
              {{ billTypeFmtn(scope.row.payBill) }}
            </div>
            <div v-if="scope.row.payBill != scope.row.payBillold && scope.row.payBillold != null"><span
              style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.payBillold) }}</span>
              {{ billTypeFmtn(scope.row.payBill) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="company"
          :formatter="costProjectC"
          label="合同公司"
        >
        </el-table-column>
        <el-table-column
          label="合计">
          <template slot-scope="scope">
          <span v-if="scope.row.shipPay != scope.row.shipPayold && scope.row.shipPayold !=null"
                style="text-decoration:line-through; color:red;margin-right: 5px;">
                    {{ multi(scope.row.settleTonnage, scope.row.shipPayold) }}
                  </span>
            <span>{{ multi(scope.row.settleTonnage, scope.row.shipPay) }}</span>
            <span v-if="scope.row.shipPay != scope.row.shipPayold && scope.row.shipPayold !=null">
                    差额({{ diffHeji2(scope.row.settleTonnage, scope.row.shipPay, scope.row.shipPayold) }})
                  </span>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="ShipCostOut.length>0 && showshipcost">
      <el-divider content-position="left" style="cursor: pointer">船上费用支出</el-divider>
      <el-table
        class="el-table-info"
        :data="ShipCostOut"
        stripe
        :row-class-name="tableRowClassName"
        style="width: 100%;margin-top: 30px"
        :show-overflow-tooltip="true"
        border
      >
        <el-table-column
          prop="sysSupplierId"
          :formatter="supplierListFmt"
          label="公司"
        >
          <template slot-scope="scope">
            <div style="width: 100%;text-align: right" v-if="scope.row.gaizhangtype === 1"><span
              style="color: red">new</span></div>
            <div v-if="scope.row.sysSupplierId == scope.row.sysSupplierIdold || scope.row.sysSupplierIdold == null">
              {{ supplierListFmtn(scope.row.sysSupplierId) }}
            </div>
            <div v-if="scope.row.sysSupplierId != scope.row.sysSupplierIdold && scope.row.sysSupplierIdold != null">
              <span
                style="text-decoration:line-through; color:red">{{ supplierListFmtn(scope.row.sysSupplierIdold) }}</span>
              {{ supplierListFmtn(scope.row.sysSupplierId) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costProject"
          label="费用类型"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.costProject == scope.row.costProjectold || scope.row.costProjectold == null">
              {{ costProjectFmtn(scope.row.costProject) }}
            </div>
            <div v-if="scope.row.costProject != scope.row.costProjectold && scope.row.costProjectold != null"><span
              style="text-decoration:line-through; color:red">{{ costProjectFmtn(scope.row.costProjectold) }}</span>
              {{ costProjectFmtn(scope.row.costProject) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costPriceNo"
          label="价格"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.costPriceNo == scope.row.costPriceNoold || scope.row.costPriceNoold == null">
              {{ scope.row.costPriceNo }}
            </div>
            <div v-if="scope.row.costPriceNo != scope.row.costPriceNoold && scope.row.costPriceNoold !=null">
              <span style="text-decoration:line-through; color:red">{{ scope.row.costPriceNoold }}</span>
              {{ scope.row.costPriceNo }}
              差额({{ toSubt(scope.row.costPriceNo, scope.row.costPriceNoold) }})
            </div>

          </template>
        </el-table-column>
        <el-table-column
          prop="tax"
          label="税率"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.tax == scope.row.taxold || scope.row.taxold == null">
              {{ billTypeFmtn(scope.row.tax) }}
            </div>
            <div v-if="scope.row.tax != scope.row.taxold && scope.row.taxold != null"><span
              style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.taxold) }}</span>
              {{ billTypeFmtn(scope.row.tax) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costBear"
          :formatter="costProjectta"
          label="承担方"
        >
        </el-table-column>
      </el-table>
    </template>

    <template v-if="CostDetail.length>0 && showcostdetail">
      <el-divider content-position="left" style="cursor: pointer">货物费用支出</el-divider>
      <el-table
        class="el-table-info"
        :data="CostDetail"
        stripe
        style="width: 100%;margin-top: 30px"
        :span-method="objectSpanMethod"
        :show-overflow-tooltip="true"
        border
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          prop="kehu"
          :formatter="publicFmt"
          label="客户"
        >
          <template slot-scope="scope">
            <div style="width: 100%;text-align: right" v-if="scope.row.gaizhangtype === 1">
              <span style="color: red">new</span>
            </div>
            <div style="width: 100%;text-align: right" v-if="scope.row.gaizhangtype === 0">
              <span style="">删</span>
            </div>
            <div>{{ scope.row.kehu }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="gangkou"
          :formatter="publicFmt"
          label="终到港"
        >
        </el-table-column>
        <el-table-column
          prop="tonnage"
          :formatter="publicFmt"
          label="吨位"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.tonnage == scope.row.tonnageold || scope.row.tonnageold == null">
              {{ scope.row.tonnage }}
            </div>
            <div v-if="scope.row.tonnage != scope.row.tonnageold && scope.row.tonnageold != null">
              <span style="text-decoration:line-through; color:red">{{ scope.row.tonnageold }}</span>
              {{ scope.row.tonnage }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="companyName"
          label="公司名称"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.companyName == scope.row.companyNameold || scope.row.companyNameold == null">
              {{ supplierListFmtn(scope.row.companyName) }}
            </div>
            <div v-if="scope.row.companyName != scope.row.companyNameold && scope.row.companyNameold != null"><span
              style="text-decoration:line-through; color:red">{{ supplierListFmtn(scope.row.companyNameold) }}</span>
              {{ supplierListFmtn(scope.row.companyName) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costName"
          label="费用类型"
          :formatter="costTypeFmt"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.costName == scope.row.costNameold || scope.row.costNameold == null">
              {{ costTypeFmtn(scope.row.costName) }}
            </div>
            <div v-if="scope.row.costName != scope.row.costNameold && scope.row.costNameold != null"><span
              style="text-decoration:line-through; color:red">{{ costTypeFmtn(scope.row.costNameold) }}</span>
              {{ costTypeFmtn(scope.row.costName) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="priceNo"
          :formatter="publicFmt"
          label="价格"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.priceNo == scope.row.priceNoold || scope.row.priceNoold == null">
              {{ scope.row.priceNo }}
            </div>
            <div v-if="scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null"><span
              style="text-decoration:line-through; color:red">{{ scope.row.priceNoold }}</span> {{ scope.row.priceNo }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="invoiceType"
          label="发票税率">
          <template slot-scope="scope">
            <div v-if="scope.row.invoiceType == scope.row.invoiceTypeold || scope.row.invoiceTypeold == null">
              {{ billTypeFmtn(scope.row.invoiceType) }}
            </div>
            <div v-if="scope.row.invoiceType != scope.row.invoiceTypeold && scope.row.invoiceTypeold != null"><span
              style="text-decoration:line-through; color:red">{{ billTypeFmtn(scope.row.invoiceTypeold) }}</span>
              {{ billTypeFmtn(scope.row.invoiceType) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="合计">
          <template slot-scope="scope">
            <span v-if="scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null"
                  style="text-decoration:line-through; color:red;margin-right: 5px;">{{ multi(scope.row.tonnage, scope.row.priceNoold) }}</span>
            <span>{{ multi(scope.row.tonnage, scope.row.priceNo) }}</span>
            <span v-if="scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null">
                    差额({{ diffHeji2(scope.row.tonnage, scope.row.priceNo, scope.row.priceNoold) }})
                  </span>
          </template>
        </el-table-column>
        <el-table-column label="差额小计" prop="sumDiff">
          <template slot-scope="scope">
            <span>{{ scope.row.sumDiff }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>


  </section>
</template>

<script>
import {
  getDictionaryList,
  getAllSysWharf,
  getAllShipPort,
  getAllSysSupplier,
} from "@/api/system/baseInit.js";
import { fmtDictionary } from '../../utils/util';

import {
  getAccountAreaByShipLineId,
//   getAllSysSupplier
} from "@/api/system/onAccount.js";
import dayjs from 'dayjs'
const ERR_OK = 0;

import {
  getSecondList
} from "@/api/system/onAccount.js";
import currency from "currency.js";

export default {
  data() {
    return {
      areaName: '',
      supplierList: [],
      customerList: [],
      userList: [],
      ozancunbean: [],
      hetongTypelist: [],
      dictionaryLists: [],
      ShipLine: [],
      ShipLineBean: [],
      ShipCost: [],
      ShipCostIn: [],
      ShipCostOut: [],
      ShipPay: [],
      GoodsCost: [],
      CostDetail: [],
      uShipLine: [],
      uShipLineBean: [],
      uShipCost: [],
      uShipCostIn: [],
      uShipCostOut: [],
      uShipPay: [],
      uGoodsCost: [],
      uCostDetail: [],
      oShipLine: [],
      oShipLineBean: [],
      oShipCost: [],
      oShipCostIn: [],
      oShipCostOut: [],
      oShipPay: [],
      oGoodsCost: [],
      oCostDetail: [],
      portList: [],
      wharfList: [],
      rules: {},
      zancunbean: [],
      zancunindex: "",
      showshipline: true,
      showshippay: true,
      showshipcost: true,
      showshipcostin: true,
      showgoodscost: true,
      showcostdetail: true,
      showupdatesl: false,
      showupdateslgcin: false,
      showupdateslgcout: false,
      showupdatesltp: false,
      showupdategc: false,
      showupdategcd: false,
      updatealllist: [],
      oldalllist: [],
      haveno: true,
      reasonData: this.reason,
      shipname: "",
      xianshiData: this.xianshi,
      showcostdetailIn: true,
      CostDetailIn: [],
      applyObjData: this.applyObj
      // applyObjData:{},
    }
  },
  name: "UpdateStowage",
  props: {
    datas: {
      type: Object
    },
    applyObj: {
      type: Object,
      default: () => {
        return null
      }
    },
    reason: {
      type: String
    },
    xianshi: {
      type: Boolean
    },
    deptCode: {
      type: String,
      default:''
    },

  },
  created() {
    this.loadCustomerList()
  },
  mounted() {
    this.shipname = this.$route.query.shipname

    var getDic = getDictionaryList( "undertaker,yes_no,cost_type,bill_tax,contract_company,Payer,dian_company,mai_company,items_occurred,shipcost_in,cost_in",this.deptCode );
    var getSysWharf = getAllSysWharf(this.deptCode);
    var getShipPort = getAllShipPort(this.deptCode);
    var getSysSupplier = getAllSysSupplier(this.deptCode);
    Promise.all([getSysSupplier, getShipPort, getSysWharf, getDic]).then(value => {
      var res = value[0]
      if (res != undefined && res.resultCode == 0) {
        this.supplierList = res.list
      }
      var res = value[1]
      if (res != undefined && res.resultCode == 0) {
        this.portList = res.list
      }
      var res = value[2]
      if (res != undefined && res.resultCode == 0) {
        this.wharfList = res.list
      }
      var res = value[3]
      if (res != undefined && res.resultCode == 0) {
        this.hetongTypelist = res.map['contract_company']
        this.dictionaryLists = res.map
      }
      this.getData()
    })
  },
  methods: {
    loadCustomerList() {
      getSecondList('').then(res => {
        if (res.resultCode == 0) {
          this.customerList = res.list
        }
      })
    },
    kehuListFmtn(cellValue) {
      var v = '--'
      for (var i = 0; i < this.customerList.length; i++) {
        var tmp = this.customerList[i]
        if (cellValue == tmp.id) {
          v = tmp.abbreviationName
          break;
        }
      }
      return v
    },
    tableRowClassName({row, column, rowIndex, columnIndex}) {
      if (row.gaizhangtype == 0) {
        return 'deleterow'
      } else if (row.gaizhangtype === 1) {
        return 'newrow'
      }
      return ''
    },
    loadAreaName(shipLineId) {
      this.areaName = ''
      getAccountAreaByShipLineId(shipLineId, this.deptCode).then(res => {
        if (res.resultCode == 0 && res.areaName) {
          this.areaName = res.areaName
        }
      })
    },
    getData() {

      if (!this.applyObjData) {
        this.applyObjData = this.datas && JSON.parse(this.datas.params2)
        this.reasonData = this.datas.params3
        this.shipname = this.datas.params5
        if (this.datas.params6 === "true") {
          this.xianshiData = true
        }
      }
      console.log('this.applyObjData=====>',this.applyObjData)
      if (!this.applyObjData) {
        return
      }
      if (this.applyObjData["ShipLine"]) {
        this.ShipLine = []
        this.ShipLineBean = this.applyObjData["ShipLine"]
        var shiplist = this.ShipLineBean
        if (shiplist.dianTax !== shiplist.dianTaxold || shiplist.dianMoney !== shiplist.dianMoneyold ||
          shiplist.maiTax !== shiplist.maiTaxold || shiplist.maiMoney !== shiplist.maiMoneyold ||
          shiplist.contractCode !== shiplist.contractCodeold
        ) {
          this.ShipLine.push(shiplist)
        } else if (!this.xianshiData) {
          this.ShipLine.push(shiplist)
        }
        this.loadAreaName(this.ShipLineBean.id)
      }
      if (this.applyObjData["ShipPay"]) {
        this.ShipPay = this.applyObjData["ShipPay"]
      }
      if (this.applyObjData["ShipCostIn"]) {
        this.ShipCostIn = this.applyObjData["ShipCostIn"]
      }
      if (this.applyObjData["ShipCostOut"]) {
        this.ShipCostOut = this.applyObjData["ShipCostOut"]
      }
      if (this.applyObjData["GoodsCost"]) {
        this.GoodsCost = this.applyObjData["GoodsCost"]
        this.addSumDiffByList(this.GoodsCost)
      }
      if (this.applyObjData["CostDetail"]) {
        this.CostDetail = this.applyObjData["CostDetail"]
        this.addSumDiffByCostDetail(this.CostDetail)
       let ccc= this.CostDetail.sort(this.compare('kehu'))
      }
      if (this.applyObjData["CostDetailIn"]) {
        this.CostDetailIn = this.applyObjData["CostDetailIn"]
        this.addSumDiffByCostDetailIn(this.CostDetailIn)
      }

    },
    compare(key){
      return function(a,b){
        var val1 = a[key];
        var val2 = b[key];
        return val2 == val1 ? 0 : val2 > val1 ? 1 : -1;
      }
    },
    addSumDiffByCostDetailIn(list) {
      if (!list || list.length == 0) {
        return
      }
      const objSumDiff = {}
      const objSpan = {}
      // diffHeji2(scope.row.tonnage,scope.row.priceNo,scope.row.priceNoold)
      // multi(scope.row.tonnage,scope.row.priceNo
      // scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null
      // companyName
      list.forEach(item => {
        // 转换为数字
        // kehu
        // multi(scope.row.tonnage,scope.row.priceNo)
        // scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null
        // diffHeji2(scope.row.tonnage,scope.row.priceNo,scope.row.priceNoold)
        let diff = 0
        if (item.priceNo != item.priceNoold && item.priceNoold != null) {
          // diff = this.diffHeji2(item.tonnage, item.priceNo, item.priceNoold).replace(/,/g, '')
          const tmpd = this.diffHeji2(item.tonnage, item.priceNo, item.priceNoold)
          if (tmpd) {
            diff = (tmpd + '').replace(/,/g, '')
          } else {
            diff = 0
          }
        } else {
          diff = this.multi(item.tonnage, item.priceNo).replace(/,/g, '')
        }
        if (objSumDiff[item.companyName]) {
          // objSumDiff[item.companyName] = this.add(objSumDiff[item.companyName], diff)
          objSumDiff[item.companyName] = currency(objSumDiff[item.companyName], {precision: 3}).add(diff).value
        } else {
          objSumDiff[item.companyName] = diff
        }
        if (objSpan[item.companyName]) {
          objSpan[item.companyName] = objSpan[item.companyName] + 1
        } else {
          objSpan[item.companyName] = 1
        }
      })
      list.forEach(item => {
        if (objSumDiff.hasOwnProperty(item.companyName)) {
          item.sumDiff = objSumDiff[item.companyName]
          item.span = objSpan[item.companyName]
          item = Object.assign({}, item)
          delete objSumDiff[item.companyName]
        }

      })
    },
    addSumDiffByCostDetail(list) {
      if (!list || list.length === 0) {
        return
      }
      const objSumDiff = {}
      const objSpan = {}
      list.forEach(item => {
        // 转换为数字
        // kehu
        // multi(scope.row.tonnage,scope.row.priceNo)
        // scope.row.priceNo != scope.row.priceNoold && scope.row.priceNoold != null
        // diffHeji2(scope.row.tonnage,scope.row.priceNo,scope.row.priceNoold)
        let diff = 0
        if (item.priceNo != item.priceNoold && item.priceNoold != null) {
          diff = this.diffHeji2(item.tonnage, item.priceNo, item.priceNoold).replace(/,/g, '')
        } else {
          diff = this.multi(item.tonnage, item.priceNo).replace(/,/g, '')
        }
        if(item.gaizhangtype == 0){
          diff = diff * -1;
        }
        if (objSumDiff[item.kehu]) {
          // objSumDiff[item.kehu] += diff
          objSumDiff[item.kehu] = currency(objSumDiff[item.kehu], {precision: 3}).add(diff).value
        } else {
          objSumDiff[item.kehu] = diff
        }
        if (objSpan[item.kehu]) {
          objSpan[item.kehu] += 1
        } else {
          objSpan[item.kehu] = 1
        }
      })
      console.log("objSpan=====>",objSpan);
      list.forEach(item => {
        if (objSumDiff.hasOwnProperty(item.kehu)) {
          item.sumDiff = objSumDiff[item.kehu]
          item.span = objSpan[item.kehu]
          item = Object.assign({}, item)
          delete objSumDiff[item.kehu]
        }
      })
    },
    addSumDiffByList(list) {
      if (!list || list.length === 0) {
        return
      }
      // diffHeji(scope.row.tonnage,scope.row.tonnageold,scope.row.freightNo,scope.row.freightNoold,scope.row.dailiNo,scope.row.dailiNoold)
      // let sumDiff = 0
      const objSumDiff = {}
      const objSpan = {}
      list.forEach(item => {
        // 转换为数字
        // sumDiff += this.diffHeji(item.tonnage, item.tonnageold, item.freightNo, item.freightNoold, item.dailiNo, item.dailiNoold)
        // if(isOladHeji(scope.row.tonnage,scope.row.tonnageold,scope.row.freightNo,scope.row.freightNoold,scope.row.dailiNo,scope.row.dailiNoold))

        let diff = 0
        if (!this.isOladHeji(item.tonnage, item.tonnageold, item.freightNo, item.freightNoold, item.dailiNo, item.dailiNoold)) {
          // multis(scope.row.tonnage,scope.row.freightNo,scope.row.tonnage,scope.row.dailiNo)
          diff = this.multis(item.tonnage, item.freightNo, item.tonnage, item.dailiNo).replace(/,/g, '')
        } else {
          // diff = this.diffHeji(item.tonnage, item.tonnageold, item.freightNo, item.freightNoold, item.dailiNo, item.dailiNoold).replace(/,/g, '')
          const tmpd = this.diffHeji(item.tonnage, item.tonnageold, item.freightNo, item.freightNoold, item.dailiNo, item.dailiNoold)
          if (tmpd) {
            diff = (tmpd + '').replace(/,/g, '')
          } else {
            diff = 0
          }
        }


        // sumDiff += parseFloat(diff)
        if (!objSumDiff[item.seckehu]) {
          objSumDiff[item.seckehu] = 0
        }
        if (!objSpan[item.seckehu]) {
          objSpan[item.seckehu] = 0
        }
        // objSumDiff[item.seckehu] += parseFloat(diff)
        objSumDiff[item.seckehu] = currency(objSumDiff[item.seckehu], {precision: 3}).add(diff).value
        objSpan[item.seckehu]++
      })
      console.log('objSumDiff', objSumDiff)
      list.forEach(item => {
        if (objSumDiff.hasOwnProperty(item.seckehu)) {
          item.sumDiff = objSumDiff[item.seckehu]
          item.span = objSpan[item.seckehu]
          item = Object.assign({}, item)
          delete objSumDiff[item.seckehu]
        }
      })

      // const sdata = {
      //   seckehu: '合计',
      //   sumDiff: sumDiff
      // }
      //   list.push(sdata)
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      // prop="sumDiff"
      if (column.property === 'sumDiff') {
        if (row.span) {
          return {
            rowspan: row.span,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }

    },
    changeshipline() {
      this.showshipline = !this.showshipline
    },
    changeshippay() {
      this.showshippay = !this.showshippay
    },
    changeshipCost() {
      this.showshipcost = !this.showshipcost
    },
    showshipcostIn() {
      this.showshipcostin = !this.showshipcostin
    },
    changeGoodsCost() {
      this.showgoodscost = !this.showgoodscost
    },
    changeCostDetail() {
      this.showcostdetail = !this.showcostdetail
    },
    changeCostDetailIn() {
      this.showcostdetailIn = !this.showcostdetailIn
    },
    rowClass({row, column, rowIndex, columnIndex}) {
      if ((row.status == 0 || row.status == 1) && columnIndex == 7) {
        return 'warning-cell'
      } else if (row.status == 2 && columnIndex == 7) {
        return 'success-cell'
      }
      return '';
    },
    fmtUserName(row, column, cellValue, index) {

      var v = '--'
      if (!this.$store.getters.getAllUser){
        return v
      }
      for (var i = 0; i < this.$store.getters.getAllUser.length; i++) {
        var tmp = this.$store.getters.getAllUser[i]
        if (cellValue == tmp.id) {
          v = tmp.name
          break;
        }
      }
      return v

    },
    fmtUserNamen(cellValue) {

      var v = '--'
      if (!this.$store.getters.getAllUser){
        return v
      }
      for (var i = 0; i < this.$store.getters.getAllUser.length; i++) {
        var tmp = this.$store.getters.getAllUser[i]
        if (cellValue == tmp.id) {
          v = tmp.name
          break;
        }
      }
      return v

    },
    fmtsettleCompany(cellValue) {
      var v = '--'
      for (var i = 0; i < this.supplierList.length; i++) {
        var tmp = this.supplierList[i]
        if (cellValue == tmp.id) {
          v = tmp.simpleName
          break;
        }
      }
      return v
    },
    supplierListFmt(row, column, cellValue, index) {
      var v = '--'
      for (var i = 0; i < this.supplierList.length; i++) {
        var tmp = this.supplierList[i]
        if (cellValue == tmp.id) {
          v = tmp.simpleName
          break;
        }
      }
      return v
    },
    supplierListFmtn(cellValue) {
      var v = '--'
      for (var i = 0; i < this.supplierList.length; i++) {
        var tmp = this.supplierList[i]
        if (cellValue == tmp.id) {
          v = tmp.simpleName
          break;
        }
      }
      return v
    },
    fmtPortName(cellValue) {

      var v = '--'
      for (var i = 0; i < this.portList.length; i++) {
        var tmp = this.portList[i]
        if (cellValue == tmp.id) {
          v = tmp.name
          break;
        }
      }
      return v

    },
    fmtWharfName(cellValue) {
      var v = '--'
      for (var i = 0; i < this.wharfList.length; i++) {
        var tmp = this.wharfList[i]
        if (cellValue == tmp.id) {
          v = tmp.wharf
          break;
        }
      }
      return v

    },
    costTypeFmtn(cellValue) {
      return fmtDictionary(cellValue, this.dictionaryLists['cost_type']);
    },
    billTypeFmtn(cellValue) {
      return fmtDictionary(cellValue, this.dictionaryLists['bill_tax']);
    },
    costProjectFmtn(cellValue) {
      return fmtDictionary(cellValue, this.dictionaryLists['items_occurred']);
    },
    costTypeFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['cost_type']);
    },
    costProjectta(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['undertaker']);
    },
    billTypeFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['bill_tax']);
    },
    costProjectFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['items_occurred']);
    },
    costInFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['cost_in']);
    },
    costInFmtn(cellValue) {
      return fmtDictionary(cellValue, this.dictionaryLists['cost_in']);
    },
    costProjectC(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['contract_company']);
    },
    costProjectCn(cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['contract_company']);
    },
    costProjectdian(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['dian_company']);
    },
    costProjectmai(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['contract_company']);
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    publicFmtn(cellValue) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue == undefined || cellValue == null || cellValue == '') {
        return '--'
      }
      return dayjs(cellValue).format(fmtstr)
    },
    diffHeji2(x, y, yold) {
      let newValue = this.multi(x, y, false)
      let oldValue = this.multi(x, yold, false)
      let val = this.toSubt(newValue,oldValue);
      return this.toThousands(val)
    },
    diffHeji(x, xold, y, yold, z, zold) {
      let newValue = this.multis(x, y, x, z, false)
      let oldValue = this.multisHeji(x, xold, y, yold, z, zold, false)
      let val = this.toSubt(newValue,oldValue);
      return this.toThousands(val)
    },
    isOladHeji(x, xold, y, yold, z, zold) {
      return (xold != null && x !== xold) || (yold != null && y !== yold) || (zold != null && z !== zold)
    },
    multisHeji(x, xold, y, yold, z, zold, isBeautiful = true) {
      // xold = (xold || x)
      // yold = (yold || y)
      // zold = (zold || z)
      xold = (xold || 0)
      yold = (yold || 0)
      zold = (zold || 0)
      return this.multis(xold, yold, xold, zold, isBeautiful)
    },
    multi(x, y, isBeautiful = true) {
      if (!x || !y) {
        return ''
      }
      try {
        let result = this.toMultiply(x,y);
        if (isBeautiful) {
          return this.toThousands(result)
        }
        return result
      } catch (e) {
        return ''
      }
    },
    multis(x1, y1, x2, y2, isBeautiful = true) {
      let v1 = this.multi(x1, y1, false)
      let v2 = this.multi(x2, y2, false)
      if (!v1 && !v2) {
        return ''
      }
      v1 = v1 || 0
      v2 = v2 || 0
      try {
        if (isBeautiful) {
          let val = this.toAddition(v1,v2);
          return this.toThousands(val)
        }
        let val = this.toAddition(v1,v2);
        return val
      } catch (e) {
        return ''
      }
    },
    toSubt(a, b) {
      if (a === null || b === null) {
        return ''
      }
      return currency(a, {separator: ',', precision: 3, symbol: ''}).subtract(b).value
    },
    toAddition(a, b) {
      if (a == null || b == null) {
        return ''
      }
      return currency(a, {separator: ',', precision: 3, symbol: ''}).add(b).value
    },
    toMultiply(a, b) {
      if (a == null || b == null) {
        return ''
      }
      return currency(a, {separator: ',', precision: 3, symbol: ''}).multiply(b).value
    },
    toThousands(num) {
      if (!num) {
        return 0
      }
      return currency(num, {separator: ',', precision: 3, symbol: ''}).format()
      // let result = '',
      //   counter = 0,precision = '';
      // num = num.toString();
      // if (num.includes(".")) {
      //   let arr = num.split(".")
      //   num = arr[0]
      //   precision = arr[1]
      // }
      // for (let i = num.length - 1; i >= 0; i--) {
      //   counter++;
      //   result = num.charAt(i) + result;
      //   if (!(counter % 3) && i !== 0) {
      //     result = ',' + result;
      //   }
      // }
      // if (precision) {
      //   return result + '.' + precision;
      // }
      // return result;
    }
  }
}
</script>

<style scoped>

.biaotou {
  margin-left: 30px;
}

.el-table-info >>> .cell {
  text-align: center;
}

.el-table-info >>> .newrow {
  color: red;
}

.el-table-info >>> .deleterow {
  color: #C0C4CC
}

</style>
