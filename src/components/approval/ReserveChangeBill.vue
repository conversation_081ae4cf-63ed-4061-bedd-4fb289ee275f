<template>
   <section id="dataform" style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div id="btnPrintId" style="width: 100%;text-align: right;margin-top: -20px;margin-bottom: 10px">
      <el-button type="primary" @click="printApply">打印</el-button>
    </div>
      <el-divider content-position="left">办事处：{{applyDetail.companyName}}</el-divider>
      <div>
        改帐理由：{{changeMsg}}
      </div>
    <div>
        <!-- <div class="textcenter">日常消费报销明细</div>
          <table border="1" class="tablecls">
          <tr>
            <th>序号</th>
            <th class="tabDateCls">日期</th>
            <th>用途说明</th>
            <th >支出</th>
            <th>发票(✓)</th>
            <th>备注</th>
          </tr>
          <tr v-for="(item,index) in tmpRecordType1List" :key="index">
            <td>{{index+1}}</td>
            <td>{{item.consumptionDate}}{{item.param2 && item.param2!=item.consumptionDate ? ' ~ '+item.param2 :''}}</td>
            <td>{{item.param1}}</td>
            <td >{{item.balance}}</td>
            <td>
              {{fmtBill(item.isBill)}}
            </td>
            <td>{{item.remarks}}</td>
          </tr>

        </table>
        <div class="textcenter">招待费报销明细</div>
         <table border="1" class="tablecls">
          <tr>
            <th>序号</th>
            <th class="tabDateCls">日期</th>
            <th>用途说明</th>
            <th >支出</th>
            <th>发票(✓)</th>
            <th>备注</th>
          </tr>
          <tr v-for="(item,index) in tmpRecordType2List" :key="index">
            <td>{{index+1}}</td>
            <td>{{item.consumptionDate}}{{item.param2 && item.param2!=item.consumptionDate ? ' ~ '+item.param2 :''}}</td>
            <td>{{item.param1}}</td>
           <td >{{item.balance}}</td>
            <td>
              {{fmtBill(item.isBill)}}
            </td>
            <td>{{item.remarks}}</td>
          </tr>

        </table> -->
        <div class="textcenter" v-show="tmpRecordType3List && tmpRecordType3List.length">业务费用明细</div>
         <table border="1" v-show="tmpRecordType3List && tmpRecordType3List.length" class="tablecls">
          <tr>
            <th>序号</th>
            <td >船期</td>
            <th class="tabDateCls">付款日期</th>
            <th class="min70">用途说明</th>
            <th >支出</th>
            <th class="min70">发票(✓)</th>
            <th>备注</th>
          </tr>
          <tr v-for="(item,index) in tmpRecordType3List" :key="index">
            <td>{{index+1}}</td>
            <td>{{shipLineFmt(item.departParam||item.param3) }}</td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'consumptionDate')}">
                  {{item.consumptionDate}}{{item.param2 && item.param2!=item.consumptionDate ? ' ~ '+item.param2 :''}}
                </div>
                <span class="redtext" v-show="isNewListById(item.id,'consumptionDate')">{{newList[item.id]  && newList[item.id].consumptionDate}}</span>
            </td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'param1') }">{{item.param1}}</div>
                <span class="redtext" v-show="isNewListById(item.id,'param1')">{{newList[item.id] && newList[item.id].param1}}</span>
            </td>
            <td >
              <div :class="{'deltext':isNewListById(item.id,'balance')}">{{ item.balance|moneyFmt}}</div>
                <span class="redtext" v-show="isNewListById(item.id,'balance')">{{newList[item.id] && newList[item.id].balance | moneyFmtNull}}</span>
            </td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'isBill')}">{{ fmtBill(item.isBill)}}</div>
                <span class="redtext" v-show="isNewListById(item.id,'isBill')">{{newList[item.id] && fmtBill(newList[item.id].isBill)}}</span>
            </td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'remarks')}">{{item.remarks}}</div>
              <span class="redtext" v-show="isNewListById(item.id,'remarks')">{{newList[item.id] && newList[item.id].remarks}}</span>

            </td>
          </tr>

        </table>
        <div class="textcenter" v-show="tmpRecordIncList && tmpRecordIncList.length" style="margin-top:10px;">业务费用收入明细</div>
          <table border="1" v-show="tmpRecordIncList && tmpRecordIncList.length" class="tablecls">
          <tr>
            <th>序号</th>
            <td >船期</td>
            <th class="tabDateCls">付款日期</th>
            <th class="min70">用途说明</th>
            <th >收入</th>
            <th class="min70">发票(✓)</th>
            <th>备注</th>
          </tr>
          <tr v-for="(item,index) in tmpRecordIncList" :key="index">
            <td>{{index+1}}</td>
            <td>{{shipLineFmt(item.departParam||item.param3) }}</td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'consumptionDate')}">
                  {{item.consumptionDate}}{{item.param2 && item.param2!=item.consumptionDate ? ' ~ '+item.param2 :''}}
                </div>
                <span class="redtext" v-show="isNewListById(item.id,'consumptionDate')">{{newList[item.id]  && newList[item.id].consumptionDate}}</span>
            </td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'param1') }">{{item.param1}}</div>
                <span class="redtext" v-show="isNewListById(item.id,'param1')">{{newList[item.id] && newList[item.id].param1}}</span>
            </td>
            <td >
              <div :class="{'deltext':isNewListById(item.id,'balance')}">{{ item.balance|moneyFmt}}</div>
                <span class="redtext" v-show="isNewListById(item.id,'balance')">{{newList[item.id] && newList[item.id].balance | moneyFmtNull}}</span>
            </td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'isBill')}">{{ fmtBill(item.isBill)}}</div>
                <span class="redtext" v-show="isNewListById(item.id,'isBill')">{{newList[item.id] && fmtBill(newList[item.id].isBill)}}</span>
            </td>
            <td>
              <div :class="{'deltext':isNewListById(item.id,'remarks')}">{{item.remarks}}</div>
              <span class="redtext" v-show="isNewListById(item.id,'remarks')">{{newList[item.id] && newList[item.id].remarks}}</span>

            </td>
          </tr>

        </table>
          <div style="text-align:right;margin-top:10px;">
          <!-- <span>日常消费：{{applyDetail.expendDaily|moneyFmt}}</span>
           <el-divider direction="vertical"></el-divider>
          <span>招&nbsp;待&nbsp;费：{{applyDetail.expendHosp|moneyFmt}}</span>
           <el-divider direction="vertical"></el-divider>
          <span>业务费用：{{applyDetail.expendBus|moneyFmt}}</span>
          <el-divider direction="vertical"></el-divider> -->
          <!-- <div style="margin-top:5px;">改帐前合计：{{applyDetail.start|moneyFmt}}</div> -->
          <!-- <div style="margin-top:5px;">改帐后合计：{{applyDetail.end|moneyFmt}}</div> -->
        </div>
    </div>
  </section>
</template>
<script>
import currency from 'currency.js'
import {  fundRecordList, queryRecordSum, changeBillById, fundDetail} from '@/api/system/imprestFund'
export default {
  name: 'ReserveBill',
  props: {
    datas: {
      type: Object
    }
  },
  data() {
    return {
       billList: [
        { label: '无票', value: 0 },
        { label: '✓', value: 1 },
        {label:'收据',value:2}
      ],
      recordList: [],
      oldList: [],
      newList: [],
      changeMsg:'--',
      applyDetail: {
        expendDaily: 0,
        expendHosp: 0,
        expendBus: 0,
        totalExpenditure: 0,
        incomeBus: 0,
        companyName:''
      }
    }
  },
  created() {
    if (this.datas.fundId) {
      // 根据账户加载列表
      // console.log('fund', this.datas.fundId)
      // this.loadRecordList()
      // this.loadRecordAllSum()
      this.loadFundDetail(this.datas.fundId)
      // 加载期初期末余额
      this.applyDetail.start = this.datas.oldSum
      this.applyDetail.end = this.datas.newSum
      this.oldList = this.datas.oldList
      this.newList = this.datas.updateDataList
      this.changeMsg = this.datas.changeMsg
      this.recordList = []
      if (this.oldList) {
        for (const key in this.oldList) {
          this.recordList.push(this.oldList[key])
        }
      }
    }
    if (this.datas.params2) {
      this.changeMsg = this.datas.params5
      // 加载账单
      this.loadDetails(this.datas.params2)
    }
  },
  computed: {
     tmpRecordType1List() {
      if (this.recordList && this.recordList.length > 0) {

        return this.recordList.filter(item => item.state == 1 && item.consumptionType == 1 )
      }
      return []
    },
      tmpRecordType2List() {
        if (this.recordList && this.recordList.length > 0) {
        console.log('p2',this.recordList.filter(item => item.state == 1 &&  item.consumptionType == 2 ))
        return this.recordList.filter(item => item.state == 1 &&  item.consumptionType == 2 )
      }
      return []
    },
    tmpRecordType3List() {
      if (this.recordList && this.recordList.length > 0) {
        return this.recordList.filter(item => item.state == 1 &&  item.consumptionType == 3 )
      }
      return []
    },
    tmpRecordIncList() {
      if (this.recordList && this.recordList.length > 0) {
        return this.recordList.filter(item => item.state == 2)
      }
      return []
    },
    sumOutAndIn() {
      if (this.applyDetail.balance) {
        return this.applyDetail.balance
      }
      let sum = this.applyDetail.totalExpenditure || this.applyDetail.param1 || 0
      const sumInc = this.applyDetail.incomeBus ? this.applyDetail.incomeBus * -1 : 0
      sum = currency(sum).add(sumInc).value
      return sum
    },
  },
   filters: {
    moneyFmt(v) {
      if (v) {
       return  currency(v, { symbol: '', precision: 2 }).format()
      }
      return 0
     },
     moneyFmtNull(v) {
      if (v) {
       return  currency(v, { symbol: '', precision: 2 }).format()
      }
      return ''
     }
  },
  methods: {
    isNewListById(id, param) {
      return this.newList[id] && !this.isEq(this.newList[id][param], this.oldList[id][param])
    },
    isEq(v1, v2) {
      return v1 == v2
    },
    shipLineFmt(str) {
      if(!str){
        return '--'
      }
      const arr = str.split(',')
      let s = ''
      for (let i = 0; i < arr.length; i++) {
        if(s){
          s += ','
        }
        s += arr[i].split('|')[1]
      }
      return s
    },
    loadFundDetail(id) {
      fundDetail(id).then(res => {
        console.log('res', res)
        if (res.resultCode === '0' && res.data) {
          this.applyDetail.companyName = res.data.companyName
        }
      })
    },
    loadDetails(id) {
      this.recordList = []
      changeBillById(id).then(res => {
        console.log('res', res)
        if (res.resultCode === '0' && res.data) {
          this.applyDetail = res.data
          // 加载期初、期末余额
          this.applyDetail.start = this.applyDetail.oldBalance
          this.applyDetail.end =this.applyDetail.balance
          //   this.oldList = this.datas.oldList
          // this.newList = this.datas.updateDataList
          this.recordList = JSON.parse(this.applyDetail.oldRecordInfo)
          this.newList = this.arrToObj(JSON.parse(this.applyDetail.recordInfo))
          this.oldList = this.arrToObj(JSON.parse(this.applyDetail.oldRecordInfo))
        }
      })
    },
    arrToObj(list) {
      const obj = {}
      for (let i = 0; i < list.length; i++) {
        obj[list[i].id] = list[i]
      }
      return obj
    },
    splitStrIdx(str, idx,spl='|') {
      if (!str) {
        return ''
      }
      const arr = str.split(spl)
      if (arr.length > idx) {
        return arr[idx]
      }
      return ''
    },
    fmtBill(idx) {
      const b = this.billList.filter(item => item.value == idx)
      if (b && b.length > 0) {
        return b[0].label
      }
      return ''
    },
    loadRecordAllSum() {
      this.loadRecordSum(1, 1, undefined, 0, res => {
        if (res.resultCode === '0' && res.data) {
          this.applyDetail.expendDaily = res.data
        }
      })
      this.loadRecordSum(1, 2, undefined, 0, res => {
        if (res.resultCode === '0' && res.data) {
          this.applyDetail.expendHosp = res.data
        }
      })
      this.loadRecordSum(1, 3, undefined, 0, res => {
        if (res.resultCode === '0' && res.data) {
          this.applyDetail.expendBus = res.data
        }
      })
      this.loadRecordSum(1, undefined, undefined, 0, res => {
        if (res.resultCode === '0' && res.data) {
          this.applyDetail.totalExpenditure = res.data
        }
      })
      this.loadRecordSum(2, 3, undefined, 0, res => {
        if (res.resultCode === '0' && res.data) {
          this.applyDetail.incomeBus = res.data
        }
      })
    },
    loadRecordList() {
      // this.recordList = []
      this.recordList = this.datas.recordList
      //   fundRecordList({ fundId: this.datas.fundId, status: 0 }).then(res => {
      //   if (res.resultCode == '0' && res.data) {
      //     this.recordList = res.data
      //   }
      // })
    },
    loadRecordSum(state, consumptionType, monthRec, status,func) {
      // state  1支出、2收入
      // consumptionType, 1日常、2招待、3业务
      // monthRec,年-月
      // status 0 创建， 1提交中，3审批成功，2 审批失败
      const params = {
        fundId: this.datas.fundId, state, consumptionType, monthRec, status
      }
      queryRecordSum(params).then(res => {
        func && func(res)
      })
    },
    printApply() {
      window.document.getElementById('btnPrintId').remove()
      var str = window.document.getElementById('dataform').innerHTML
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    }
  }
}
</script>

<style scoped>
.tablecls{
  width:100%;
  border: 1px solid #212121;
  border-collapse: collapse;
  text-align: center;
}
.tablecls td,.tablecls th{
  padding:10px;
}
.tabDateCls{
  box-sizing: border-box;
  /* min-width:175px; */
}
.min70{
  min-width:70px;
}
.textcenter{
  text-align:center;
  padding:5px;
}
.deltext{
  text-decoration: line-through;
}
.redtext{
  color:#f56c6c;
}
</style>
