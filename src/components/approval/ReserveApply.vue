<template>
  <section id="dataform" style="padding-top: 10px;padding-left: 10px;padding-right: 10px">
    <div id="btnPrintId" style="width: 100%;text-align: right;margin-top: 0px;margin-bottom: 10px">
      <el-button type="primary" @click="printApply">打印</el-button>
    </div>
    <div class="textcenter">办事处：{{ comDetail.companyName }}</div>
        <table border="1" class="tablecls" style="margin-top:5px">
          <tr>
            <td rowspan="3">上期备用金申请信息</td>
            <td>请款时间</td>
            <td>{{dateFmt(comDetail.lastApplyCreateTime)}}</td>
          </tr>
          <tr>
            <td>请款金额</td>
            <td>{{comDetail.lastApplyBalance|moneyFmt}}</td>
          </tr>
          <tr>
            <td>请款后账上余额</td>
            <td>{{comDetail.lastApplyAmount|moneyFmt}}</td>
          </tr>
          <!-- <tr>
            <td rowspan="3">支出</td>
            <td>日常花销</td>
            <td class="colclk" >{{comDetail.expendDaily}}</td>
          </tr> -->
          <!-- <tr>
            <td>招待费</td>
            <td class="colclk" >{{comDetail.expendHosp}}</td>
          </tr> -->
          <tr>
            <td >支出</td>
            <td>业务费用</td>
            <td class="colclk" >{{comDetail.expendBus|moneyFmt}}</td>
          </tr>
          <tr>
            <td>收入</td>
            <td>业务费用收入</td>
            <td class="colclk" >{{comDetail.incomeBus|moneyFmt}}</td>
          </tr>
          <tr>
            <td colspan="2">支出合计</td>
            <td>{{comDetail.totalExpenditure|moneyFmt}}</td>
          </tr>
          <tr>
            <td colspan="2">现账上余额</td>
            <td>{{comDetail.accountAmount|moneyFmt }}</td>
          </tr>
          <tr>
            <td colspan="3">&nbsp;</td>
          </tr>
          <tr>
            <td rowspan="6">申请信息</td>
            <td>本次申请金额</td>
            <td>
           {{comDetail.applyBalance|moneyFmt}}
            </td>
          </tr>
          <tr>
            <td>申请后账上余额</td>
            <td>{{comDetail.applyAmount|moneyFmt}}</td>
          </tr>
          <tr>
            <td>资金用途</td>
            <td>
             {{comDetail.useRemarks}}
            </td>
          </tr>
          <tr>
            <td>户名</td>
            <td>{{comDetail.param1|bankFmt(0)}}</td>
          </tr>
          <tr>
            <td>开户行</td>
            <td>{{comDetail.param1|bankFmt(1)}}</td>
          </tr>
          <tr>
            <td>账号</td>
            <td>{{comDetail.param1|bankFmt(2)}}</td>
          </tr>
          <tr>
            <td>申请人</td>
            <td>{{comDetail.applyPersonName}}</td>
            <td>{{dateFmt(comDetail.applyCreateTime)}}</td>
          </tr>
             <!-- 付款信息 -->
        <PaymentWaterList :data-obj="datas" trStyle="height:40px" tdColspans="1,1,3"></PaymentWaterList>
          <!-- 签字 -->
          <tr v-show="spLabelList && spLabelList.length > 0">
            <td style="width: 70%;height: 60px;text-align: left" colspan="3">
              <div>
                <span v-for="(sitem,sindex) in spLabelOne" :key="sindex" :style="{'margin-left':sindex==0?'':'15%'}">{{sitem.label}}：{{ sitem.userName }} </span>
              </div>
            </td>
            <!-- <td style="width: 30%;height: 60px;text-align: left" colspan="1" rowspan="2">
              <div style="margin-left: 15px">
              总裁：{{ spLabelEnd.label && spLabelEnd.label=='总裁'? spLabelEnd.userName:" " }}
              </div>
            </td> -->
          </tr>
          <tr v-show="spLabelList && spLabelList.length > 0">
            <td style="width: 70%;height: 60px;text-align: left" colspan="3">
              <div>
                <span v-for="(sitem,sindex) in spLabelTwo" :key="sindex" :style="{'margin-left':sindex==0?'':'15%'}">{{sitem.label}}：{{ sitem.userName }} </span>
                <!-- <span>财务经理：{{ splist[3] }} </span>
                <span style="margin-left: 15%">总经理：{{ splist[4] }} </span>
                <span style="margin-left: 15%">财务总监：{{ splist[5] }} </span> -->
              </div>
            </td>
          </tr>
        </table>
  </section>
</template>

<script>
import currency from 'currency.js'
import dayjs from 'dayjs'
import { applyDetail } from '@/api/system/imprestFund'
import { getprocesspeople } from '@/api/business/processapi'
import PaymentWaterList from '@/components/paymentWaterList/index.vue'
export default {
  components: {
    PaymentWaterList
  },
  name: 'ReserveApply',
  props: {
    datas: {
      type: Object
    }
  },
  data() {
    return {
      spLabelList:[],
      applyDetail:{}
    }
  },
  filters: {
    moneyFmt(v) {
      if (v) {
       return  currency(v, { symbol: '', precision: 2 }).format()
      }
      return 0
    },
    bankFmt(v,idx){
      if (v) {
        return v.split(',')[idx]
      }
      return '--'
    }
  },
  computed: {
    spLabelOne() {
      if (this.spLabelList && this.spLabelList.length > 0) {
       return this.spLabelList.slice(0, 3)
      }
      return []
    },
    spLabelTwo() {
      if (this.spLabelList && this.spLabelList.length > 3) {
        if (this.spLabelEnd && this.spLabelEnd.label == '总裁') {
            return this.spLabelList.slice(3,-1)
        }
        return this.spLabelList.slice(3)
      }
      return []
    },
    spLabelEnd() {
      if (this.spLabelList && this.spLabelList.length > 0) {
        return this.spLabelList[this.spLabelList.length-1]
      }
      return {}
    },
    comDetail() {
      if (this.datas && this.datas.applyDetail) {
          return this.datas.applyDetail
      }
      return this.applyDetail
    }
  },

  created() {
    console.log('cc',this.datas)
    if (this.datas.fundId) {

    }
    if (this.datas.params2) {
      this.loadDetail(this.datas.params2)
    }
    if (this.datas.processId) {
      getprocesspeople(this.datas.processId).then(res => {
        if (res.spList) {
          this.spLabelList = res.spList
        }
      })
    }

  },
  methods: {
    loadDetail(id) {
      applyDetail(id).then(res => {
        console.log('a', res)
        if (res.resultCode === '0' && res.data) {
          this.applyDetail = res.data
        }
      })
    },
    printApply() {
      window.document.getElementById('btnPrintId').remove()
      var str = window.document.getElementById('dataform').innerHTML
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    },
     dateFmt(v, fmt = 'YYYY-MM-DD') {
      if(!v){
        return ''
      }
      return dayjs(v).format(fmt)
    },
  }
}
</script>


<style scoped>
.tablecls{
  width:100%;
  border: 1px solid #212121;
  border-collapse: collapse;
  text-align: center;
}
.tablecls td,.tablecls th{
  padding:10px;
}
.tabDateCls{
  box-sizing: border-box;
  min-width:260px;
}
.textcenter{
  text-align:center;
  padding:5px;
}
</style>
