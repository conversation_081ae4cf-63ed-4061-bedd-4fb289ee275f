<template>
  <span class="com-unit">
    <span class="txt">单位{{ num }}</span
    ><i class="el-icon-star-on star"></i>
  </span>
</template>

<script>
export default {
  data() {
    return {};
  },

  props: {
    num: {
      type: Number,
      required: false,
      default: 10
    }
  },

  created() {},

  computed: {},

  mounted() {},

  methods: {},
  components: {}
};
</script>

<style>
  .com-unit {
    white-space: nowrap;
  }
  .com-unit .star {
    color: #67c23a;
  }

  .com-unit .txt {
    color: #e6a23c;
  }
</style>
