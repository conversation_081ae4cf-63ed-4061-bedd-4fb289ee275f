<template>
 <div class="abs-200 ">
          <el-switch
            v-model="userAccModel"
            @change="updateAccModel(userAccModel)"

            active-value='2'
            inactive-value='1'
            active-color="#13ce66"
            inactive-color="#409EFF"
            active-text="股东核算"
            inactive-text="税务核算">
          </el-switch>
        </div>
</template>
<script>
import {accModel, upAccModel} from '@/api/system/user'
export default {
  data() {
    return {
      userAccModel:'1',
    }
  },
  mounted() {
    this.loadAccModel()
  },
  methods: {
    loadAccModel(){
      accModel().then(res=>{
        if(res){
          this.userAccModel = `${res.accModel || 1}`
          this.$emit('updateAccModel',this.userAccModel)
        }
      })
    },
    updateAccModel(type){
      upAccModel(type).then(()=>{
        this.loadAccModel()
      })
    },
  }
}
</script>
<style scoped>
.abs-200{
  position: fixed;
  right: 200px;
  top:16px;
  z-index: 1001;
}
</style>
