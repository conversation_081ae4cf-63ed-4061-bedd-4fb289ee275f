<template>
  <span>
    <template v-for="item in value">
      <el-image
        style="width: 100px; height: 100px"
        :src="item.url || item" v-if="isImg(item.url|| item)" :alt="item.fileName||''"
        :preview-src-list="[item.url||item]">
      </el-image>
      <el-button type="text" v-else @click="open(item.url||item)">{{ fileName(item.fileName||item.url||item) }}</el-button>
    </template>
    <span v-show="value.length==0">--</span>
  </span>
</template>
<script>
export default {
  props: {
    value: {
      type: Array,
      default() {
        return []
      }
    },
  },
  methods: {
    isImg(url) {
      return /\.(png|jpe?g|gif|svg)(\?.*)?$/.test(url)
    },
    open(url) {
      window.open(url,'_blank')
    },
    fileName(url) {
      console.log('--url--', url)
      if (url) {
        return url.substring(url.lastIndexOf('/') + 1)
      }
      return url
    }
  }
}
</script>
<style>

</style>
