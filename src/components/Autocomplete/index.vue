<template>
  <el-autocomplete
    v-model="inputValue"
    :disabled="disabled"
    class="el-input-autocomplete"
    clearable
    :value-key="valueKey"
    :label="label"
    :placeholder="placeholder"
    :fetch-suggestions="fetchSuggestions"
    @blur="blur"
    @change="change"
    @select="handleSelect"
  />
</template>

<script>
import { geturl } from '@/api/data.js'
export default {

  name: 'Autocomplete',
  model: {
    prop: 'defValue',
    event: 'changeDefValue'
  },
  props: {
    dicData: {
      type: Array,
      default: function() {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    dicDataShow: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    label: {
      type: String,
      default: ''
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    defValue: {
      type: String,
      default: ''
    },
    fetchUrl: {
      type: String,
      default:'',
      // default: 'http://test.dlcg.fdbiz.hy956.cn/api/users/userList?name='
    }
  },
  data() {
    return {
      suggestions: [],
      loading: false,
      focus: false,
      inputValue: this.defValue
    }
  },
  watch: {
    defValue: {
      handler(val) {
        this.inputValue = val
      },
      immediate: true
    }
  },
  methods: {
    change(val) {
      console.log('change', val)
      this.$emit('changeDefValue', val)
    },
    fetchSuggestions(querystr, cb) {
      if (this.dicDataShow) {
        cb(this.dicData)
        return
      }
      geturl(this.fetchUrl + querystr).then(res => {
        cb(res.data)
      })
    },
    handleSelect(item) {
      this.$emit('changeDefValue', item[this.valueKey])
      // this.$emit('blur')
      console.log('changeDefValue', item)
    },
    blur() {
      console.log('blur', this.inputValue)
      this.$emit('changeDefValue', this.inputValue)
    }
  }
}
</script>

<style scoped>
.el-input-autocomplete{
  width: 100%;
}
</style>
