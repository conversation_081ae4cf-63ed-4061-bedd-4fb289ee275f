<template>
    <div class="image-rotate">
      <img :src="src" :style="style" />
      <div class="image-rotate__controls">
        <i  class="el-icon-refresh-left" @click="rotateImage(-90)"></i>
        &nbsp;&nbsp;&nbsp;&nbsp;
        <i class="el-icon-refresh-right" @click="rotateImage(90)"></i>
      </div>
    </div>
</template>

<script>
export default {
  name: 'ImageRotate',
  props: {
    src: {
      type: String,
      required: true
    },
    width: {
      type: Number,
      default: 100
    },
    height: {
      type: Number,
      default: 100
    },
    rotate: {
      type: Number,
      default: 0
    }
  },
  watch: {
    src: function (val) {
      this.rotateData = 0;
    }
  },
  data() {
    return {
      rotateData: this.rotate
    }
  },
  computed: {
    style() {
      return {
        width: `${this.width}%`,
        height: `${this.height}%`,
        transform: `rotate(${this.rotateData}deg)`
      }
    }
  },
  methods: {
    rotateImage(tye) {
      this.rotateData += tye
      // this.rotate = (this.rotate + 90) % 360;
    }
  }
}
</script>

<style>
.image-rotate {
  /* position: relative; */
  width: 100%;
  height: 100%;
  /* overflow: hidden; */
}
.image-rotate img {
  /* position: absolute;
  top: 0;
  left: 0; */
  width: 100%;
  height: 100%;
  transform: rotate(0deg);
}
.image-rotate__controls {
  /* position: absolute;
  top: 0;
  left: 0; */
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
