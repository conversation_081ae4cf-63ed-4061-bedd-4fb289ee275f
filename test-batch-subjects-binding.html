<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多个会计科目批量绑定帐套功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .feature-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .feature-title {
            color: #495057;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .feature-list {
            list-style-type: disc;
            margin-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .workflow-step {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
        }
        .workflow-step h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .demo-image {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
            margin: 15px 0;
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>多个会计科目批量绑定帐套功能实现说明</h1>
    
    <div class="highlight">
        <strong>功能概述：</strong>实现了多个会计科目可以同时批量绑定到帐套的功能，用户可以在模板级别选择多个会计科目，然后一次性将它们绑定到选定的帐套，大大提升了操作效率。
    </div>

    <div class="feature-section">
        <div class="feature-title">🎯 主要功能特性</div>
        <ul class="feature-list">
            <li><strong>模板级批量操作：</strong>在模板卡片中新增"批量绑定科目"按钮</li>
            <li><strong>多选会计科目：</strong>支持选择模板下的多个会计科目进行批量操作</li>
            <li><strong>多选帐套：</strong>支持同时选择多个税务核算和股东核算帐套</li>
            <li><strong>双向选择界面：</strong>左侧选择会计科目，右侧选择帐套，界面清晰直观</li>
            <li><strong>实时统计：</strong>显示已选择的科目数量和帐套数量</li>
            <li><strong>批量全选/清空：</strong>支持一键全选或清空会计科目和帐套</li>
            <li><strong>操作确认：</strong>执行前显示确认对话框，避免误操作</li>
            <li><strong>批量绑定：</strong>一次性创建多个科目与多个帐套的绑定关系</li>
        </ul>
    </div>

    <div class="feature-section">
        <div class="feature-title">🔧 技术实现要点</div>
        <ul class="feature-list">
            <li><strong>界面设计：</strong>1000px宽度对话框，左右分栏布局</li>
            <li><strong>数据管理：</strong>动态添加选择状态，支持响应式更新</li>
            <li><strong>API优化：</strong>使用现有saveCertTemplTemp API，支持批量数据传递</li>
            <li><strong>状态控制：</strong>loading状态管理，防止重复操作</li>
            <li><strong>错误处理：</strong>完善的异常处理和用户反馈机制</li>
        </ul>
    </div>

    <div class="feature-section">
        <div class="feature-title">📋 新增方法说明</div>
        <div class="code-block">
// 批量绑定科目相关方法
batchBindSubjects(template)         // 打开批量绑定对话框
prepareBatchBindingData()           // 准备批量绑定数据
getSelectedSubjectsCount()          // 获取已选择的会计科目数量
getSelectedAccountsCount()          // 获取已选择的帐套数量

// 选择操作方法
selectAllSubjects()                 // 全选会计科目
clearAllSubjects()                  // 清空会计科目选择
selectAllBatchAccounts()            // 全选帐套
clearAllBatchAccounts()             // 清空帐套选择

// 执行方法
executeBatchBinding()               // 执行批量绑定操作
        </div>
    </div>

    <div class="feature-section">
        <div class="feature-title">🎨 界面设计</div>
        <div class="demo-image">
            <h4>批量绑定科目对话框布局</h4>
            <p>┌─────────────────────────────────────────────────────────────┐</p>
            <p>│ 批量绑定科目到帐套                                           │</p>
            <p>├─────────────────────────────────────────────────────────────┤</p>
            <p>│ 模板信息 | 统计信息: 已选科目: 2 | 已选帐套: 3              │</p>
            <p>├─────────────────────┬───────────────────────────────────────┤</p>
            <p>│ 选择会计科目         │ 选择帐套                              │</p>
            <p>│ [全选] [清空]       │ [全选] [清空]                         │</p>
            <p>│ ☑ 银行存款          │ 税务核算帐套                          │</p>
            <p>│ ☑ 应收账款          │ ☑ 公司A - 生产应用                    │</p>
            <p>│ ☐ 库存商品          │ ☐ 公司B - 销售应用                    │</p>
            <p>│ ☐ 固定资产          │ 股东核算帐套                          │</p>
            <p>│                     │ ☑ 公司A - 管理应用                    │</p>
            <p>└─────────────────────┴───────────────────────────────────────┘</p>
            <p>                    [取消] [执行批量绑定]</p>
        </div>
    </div>

    <div class="feature-section">
        <div class="feature-title">✅ 使用流程</div>
        <div class="workflow-step">
            <h4>步骤1：打开批量绑定对话框</h4>
            <p>在模板卡片中点击"批量绑定科目"按钮，打开批量绑定对话框</p>
        </div>
        
        <div class="workflow-step">
            <h4>步骤2：选择会计科目</h4>
            <p>在左侧面板中选择需要绑定的会计科目，可以使用"全选"/"清空"快速操作</p>
        </div>
        
        <div class="workflow-step">
            <h4>步骤3：选择帐套</h4>
            <p>在右侧面板中选择目标帐套，分为税务核算和股东核算两个类别</p>
        </div>
        
        <div class="workflow-step">
            <h4>步骤4：确认并执行</h4>
            <p>查看统计信息确认选择无误后，点击"执行批量绑定"按钮</p>
        </div>
        
        <div class="workflow-step">
            <h4>步骤5：确认操作</h4>
            <p>系统弹出确认对话框，显示将要绑定的科目和帐套数量，确认后执行</p>
        </div>
    </div>

    <div class="feature-section">
        <div class="feature-title">🚀 性能优势</div>
        <ul class="feature-list">
            <li><strong>批量处理：</strong>一次操作可以创建多个绑定关系，避免重复操作</li>
            <li><strong>界面友好：</strong>双栏布局，选择过程清晰直观</li>
            <li><strong>实时反馈：</strong>统计信息实时更新，操作状态一目了然</li>
            <li><strong>操作安全：</strong>确认机制防止误操作，loading状态防止重复提交</li>
        </ul>
    </div>

    <div class="feature-section">
        <div class="feature-title">📊 使用场景示例</div>
        <div class="code-block">
场景1：新建模板后批量绑定
- 创建新的凭证模板
- 添加多个会计科目（如：银行存款、应收账款、主营业务收入）
- 使用批量绑定功能将这些科目同时绑定到相关帐套

场景2：模板维护时批量调整
- 现有模板需要调整帐套绑定关系
- 选择需要调整的多个会计科目
- 批量绑定到新的帐套或解绑现有关系

场景3：跨公司业务处理
- 集团公司有多个子公司
- 统一的会计科目需要绑定到不同公司的帐套
- 批量操作提高配置效率
        </div>
    </div>

    <div class="highlight">
        <strong>注意事项：</strong>
        <ul>
            <li>批量绑定会创建多个科目与帐套的绑定关系，请确认选择正确</li>
            <li>操作前系统会显示确认对话框，请仔细核对绑定的科目和帐套数量</li>
            <li>如果操作失败，系统会显示错误信息，不会影响现有的绑定关系</li>
            <li>建议在测试环境中先验证批量绑定功能，确认无误后再在生产环境使用</li>
        </ul>
    </div>
</body>
</html>
